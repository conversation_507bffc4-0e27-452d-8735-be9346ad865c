Description: This is the design system for UpKeep, which details out color usage, radii usage, typography standards, and component guidelines for building new components and pages within the web app.

# UpKeep × Radix — Front-end Style Guide
**Version 1.0 · 2025-04-29**

## Design System Generalities
- Use Radix UI primitive components as the foundation for UI elements
- Use styled-components for additional styling components
- Apply Radix themes to components with semantic scales
- Use the Radix scale (1-12) for color variations
- Colors are based on RadixUI colors with the following guidelines:
  - Primary color: `indigo` (scales 1-12)
  - Neutral colors: `slate` (scales 1-12)
  - Semantic scales: use `background`, `surface`, `panel` appropriately
- Design for light mode first, with support for dark mode

## Color Tokens

### Brand / Primary — Indigo

| Step | CSS Token | Light Hex | Dark Hex |
|------|-----------|-----------|----------|
| 1 | `--indigo-1` | `#FDFDFE` | `#11131F` |
| 2 | `--indigo-2` | `#F7F9FF` | `#141726` |
| 3 | `--indigo-3` | `#EDF2FE` | `#182449` |
| 4 | `--indigo-4` | `#E1E9FF` | `#1D2E62` |
| 5 | `--indigo-5` | `#D2DEFF` | `#253974` |
| 6 | `--indigo-6` | `#C1D0FF` | `#304384` |
| 7 | `--indigo-7` | `#ABBDF9` | `#3A4F97` |
| 8 | `--indigo-8` | `#8DA4EF` | `#435DB1` |
| 9 | `--indigo-9` | `#3E63DD` | `#3E63DD` |
| 10 | `--indigo-10` | `#3358D4` | `#5472E4` |
| 11 | `--indigo-11` | `#3A5BC7` | `#9EB1FF` |
| 12 | `--indigo-12` | `#1F2D5C` | `#D6E1FF` |

### CSS Imports for Color Palettes

Import Radix color palettes using the following pattern:

```css
/* Light palette */
@import "@radix-ui/colors/indigo.css";
@import "@radix-ui/colors/sky.css";
@import "@radix-ui/colors/green.css";
@import "@radix-ui/colors/amber.css";
@import "@radix-ui/colors/red.css";
@import "@radix-ui/colors/slate.css";

/* Dark palette (scope inside `.dark`) */
@import "@radix-ui/colors/indigo-dark.css";
@import "@radix-ui/colors/sky-dark.css";
@import "@radix-ui/colors/green-dark.css";
@import "@radix-ui/colors/amber-dark.css";
@import "@radix-ui/colors/red-dark.css";
@import "@radix-ui/colors/slate-dark.css";

/* Alpha variants for translucent overlays & focus rings */
@import "@radix-ui/colors/indigo-alpha.css";
@import "@radix-ui/colors/slate-alpha.css";
@import "@radix-ui/colors/indigo-dark-alpha.css";
@import "@radix-ui/colors/slate-dark-alpha.css";
```

### Panel & Overlay Tokens

```css
--panel-bg-solid:       #FFFFFF;
--panel-bg-translucent: rgba(255,255,255,0.60);
--overlay-scrim:        rgba(0,0,0,0.60);
```

## Typography

### Font Families

```css
--font-system: "Inter", system-ui, sans-serif;
--font-mono:   "Menlo", ui-monospace, monospace;
--font-serif:  "Times New Roman", serif;
```

### Font Weights

```css
--font-weight-light:   300;
--font-weight-regular: 400;
--font-weight-medium:  500;
--font-weight-bold:    700;
```

### Typography Scale

| Step | Token         | Size  | Line-height | Letter-spacing | Example          |
|------|---------------|-------|-------------|----------------|------------------|
| 1    | --font-size-1 | 12 px | 16 px       | 0.0025 em      | Legal fine-print |
| 2    | --font-size-2 | 14 px | 20 px       | 0 em           | Body S           |
| 3    | --font-size-3 | 16 px | 24 px       | 0 em           | Body M (default) |
| 4    | --font-size-4 | 18 px | 26 px       | -0.0025 em     | Subtitle         |
| 5    | --font-size-5 | 20 px | 28 px       | -0.005 em      | H4               |
| 6    | --font-size-6 | 24 px | 30 px       | -0.00625 em    | H3               |
| 7    | --font-size-7 | 28 px | 36 px       | -0.0075 em     | H2               |
| 8    | --font-size-8 | 35 px | 40 px       | -0.01 em       | H1               |
| 9    | --font-size-9 | 60 px | 60 px       | -0.025 em      | Hero / Display   |

### Usage Guidelines
- Use the neutral scale from Radix UI for font colors
- Use standard semantic text elements appropriately:
  - Heading (H1-H4): For page and section headings
  - Body (S, M): For regular text content
  - Subtitle: For component titles and subheadings
  - Caption/Legal: For supplementary information and fine print
- Text should have appropriate contrast with backgrounds for accessibility

## Spacing

### Spacing Scale

| Step | Token     | px | rem (16 base) |
|------|-----------|----|---------------|
| 1    | --space-1 | 4  | 0.25          |
| 2    | --space-2 | 8  | 0.5           |
| 3    | --space-3 | 12 | 0.75          |
| 4    | --space-4 | 16 | 1             |
| 5    | --space-5 | 24 | 1.5           |
| 6    | --space-6 | 32 | 2             |
| 7    | --space-7 | 40 | 2.5           |
| 8    | --space-8 | 48 | 3             |
| 9    | --space-9 | 64 | 4             |

### Density Modifiers

Optional density modifiers are available to adjust the spacing scale:
- `--scale-90`: Reduced spacing (90% of default)
- `--scale-95`: Slightly reduced spacing (95% of default)
- `--scale-100`: Default spacing
- `--scale-105`: Slightly increased spacing (105% of default)
- `--scale-110`: Increased spacing (110% of default)

## Border Radius

### Radius Scale

| Token          | px   | Typical use        |
|----------------|------|---------------------|
| --radius-1     | 2    | Checkbox / Input    |
| --radius-2     | 4    | Small Button        |
| --radius-3     | 6    | Card                |
| --radius-4     | 8    | Modal surface       |
| --radius-5     | 12   | Large tile          |
| --radius-6     | 16   | Sheet / Drawer      |
| --radius-none  | 0    | Square              |
| --radius-full  | 9999 | Pill / Avatar       |

## Theming
- Import Radix themes for consistent styling
- Use semantic color tokens from Radix (background, foreground, surface, etc.)
- Primary accent color for the application is `indigo`
- Use `slate` for all neutral UI elements to distinguish from primary colors
- Apply generous padding for a more breathable UI experience
- Keep backgrounds neutral and clean
- Use lifted effects (shadows/elevation) primarily for:
  - Hover states
  - Focus states
  - Active states
- Reserve special background effects for call-to-action buttons and key UI elements
- Interactive states should have clear visual feedback:
  - Hover: Subtle elevation or highlight
  - Focus: Clear outline or glow effect
  - Active: Pressed or indented appearance

## Component Guidelines
- Start with Radix UI primitive components and apply themes
- For new components, begin with the Radix primitive and apply the appropriate theme
- Ensure all components support both light and dark mode via the theming system
- Use preset component styles where available

### Implementation with Radix Themes

Radix Themes provides many built-in features that simplify component implementation:

| Feature | Status with Radix Themes |
|---------|--------------------------|
| Hover / Active / Disabled styles | **Built-in** — each component maps these states to the proper scale steps automatically. |
| Focus rings (accessible) | **Built-in** |
| ARIA & keyboard handling | **Built-in** |
| Color mapping | Use the color prop (color="indigo"). Themes takes care of the rest. |

> **Note:** If you ever drop down to Radix Primitives (un-styled layer), you'll need to author your own state styles. Otherwise, Themes has you covered out of the box.

## Token Delivery Methods

There are three approaches to implementing design tokens in your project:

1. **Drop-in CSS** — Import the CSS files directly as shown in the Color Tokens section. This is the simplest approach and requires minimal setup.
   ```css
   @import "@radix-ui/colors/indigo.css";
   @import "@radix-ui/colors/indigo-dark.css";
   /* etc. */
   ```

2. **Design-token JSON** — Export tokens from Figma using Tokens Studio and process them with Style Dictionary.
   - This method provides a more structured way to manage tokens
   - Useful for cross-platform consistency (web, mobile, etc.)
   - Allows for transformation of tokens to multiple formats

3. **Package theme file** — Create a TypeScript theme object for better tree-shaking at build time.
   ```typescript
   // Example theme file
   export const theme = {
     colors: {
       primary: {
         1: '#FDFDFE',
         // ... other values
       },
       // ... other color scales
     },
     // ... typography, spacing, etc.
   };
   ```

Choose the approach that best fits your project's needs and technical requirements.