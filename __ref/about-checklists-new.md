---
description: About our checklist project, use this rule to understand the project before starting any new component or feature
globs: 
alwaysApply: false
---
# About Checklists

## Overview
Checklists are a core feature in UpKeep's CMMS platform. They provide a structured way for maintenance technicians to complete work by following a predefined series of tasks in a specific order. <PERSON><PERSON> create checklists, which are then attached to work orders and assigned to technicians.

## Workflow
1. <PERSON><PERSON> create checklists with various task types
2. Checklists are attached to work orders
3. Work orders are assigned to technicians
4. Technicians complete each task on the checklist in sequence

## Current Task Types
- Subtask Status (on hold, in progress, complete)
- Text Field
- Number Field
- Inspection Check
- Multiple Choice
- Meter Reading

## Planned Enhancements
- Signature task type
- Acknowledgement tasks with timestamps
- Informational/warning text
- Required task completion option
- Referential media (images, files, videos)
- Notes and URL links for each task
- Conditional logic for tasks
- Sections to group tasks with titles and descriptions

## Tech Stack
- React 16
- TypeScript for type definitions
- React Router for navigation
- Context API for state management
- Custom hooks for reusable logic
- Analytics tracking using custom Analytics utility

### State Management
- Context API with custom providers:
  - `useChecklistsData` - Global checklist context
  - `useAddEditChecklistData` - Editing context
- Custom stores for component state

## Core Files Location
Main directory: `src/scenes/Checklists/`

### Key Files
- `src/scenes/Checklists/index.js` - Main entry point
- `src/scenes/Checklists/types.d.ts` - TypeScript type definitions
- `src/scenes/Checklists/styled.js` - Styled components

### Key Directories
- `src/scenes/Checklists/data/` - Data management
  - `src/scenes/Checklists/data/api/` - API interactions including:
    - `getChecklists.ts` - Fetch checklists
    - `getChecklist.ts` - Fetch a single checklist
    - `upsertChecklist.ts` - Create/update checklists
    - `cloneChecklist.ts` - Clone existing checklists
    - `deleteChecklists.ts` - Delete checklists
    - `updateFormItem.ts` - Update individual checklist items
- `src/scenes/Checklists/AddEdit/` - Add/Edit interface
- `src/scenes/Checklists/TemplateView/` - Template viewing interface
- `src/scenes/Checklists/ListView/` - List view interface
- `src/scenes/Checklists/AddEdit/ChecklistBuilder` - The new add/edit interface for checklist
- `src/scenes/Checklists/AddEdit/TaskBuilder` - The new add/edit interface for tasks

### Key Components
- `src/scenes/Checklists/AddEdit/TaskBuilder/TaskTypes/index.ts` - For checklist task types
- `src/scenes/Checklists/AddEdit/ChecklistBuilder/index.tsx` - For new checklist builder 

#### Reference for Design inspiration

- `src/scenes/Checklists/AddEdit/TaskBuilder/index.tsx`
- `src/scenes/Checklists/AddEdit/TaskBuilder/*`
- `src/scenes/Checklists/AddEdit/TaskBuilder/TaskTypeSelector.tsx`
- `src/scenes/Checklists/AddEdit/ChecklistBuilder/index.tsx`