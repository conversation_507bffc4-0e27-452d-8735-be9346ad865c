import { useSession, useAdvancedPermissions, useLanguage } from 'hooks';
import { getLocalizedBusinessType } from 'utils';
import { ACCOUNT_TYPES } from 'constants/userRoles';
import {
  canViewDatahub,
  canViewEdge,
  canViewImportExportPage,
  canViewEhs,
} from 'helpers/routeProtection';

export default function useNavPermissions() {
  const session: any = useSession();
  const t = useLanguage();
  const company = session?.company;
  const isRequester = session.legacyAccountType === 'Requester';
  const businessTypeKey = `businessTypes.${getLocalizedBusinessType(
    company?.businessType,
    t('businessTypes'),
  )}`;
  const [
    hasWorkOrderView,
    hasWorkRequestView,
    hasPreventativeMaintenanceView,
    hasAnalyticsDashboardView,
    hasPurchaseOrderView,
    hasMeterView,
    hasChecklistView,
    hasFileView,
    hasRequestPortalView,
    hasLocationAccess,
    hasAssetAccess,
    hasPartsAccess,
    hasCategoryAccess,
    hasPeopleAccess,
    hasVendorView,
    hasSchedulerView,
  ] = useAdvancedPermissions([
    'workOrderView',
    'workRequestView',
    'preventativeMaintenanceView',
    'analyticsDashboardView',
    'purchaseOrderView',
    'meterView',
    'checklistView',
    'fileView',
    'requestPortalView',
    'locationView',
    'assetView',
    'partView',
    'categoryView',
    'peopleView',
    'vendorView',
    'schedulerView',
  ]);
  const hasLocationView = !isRequester && hasLocationAccess;
  const hasAssetView = !isRequester && hasAssetAccess;
  const hasPartsView = !isRequester && hasPartsAccess;
  const hasCategoryView = !isRequester && hasCategoryAccess;
  const isBasicPlan = session?.company?.plan?.type === 'BASIC';
  const isLimitedTech = session?.accountType === ACCOUNT_TYPES.LIMITED_TECH;
  const hasPeopleView =
    !isRequester && hasPeopleAccess && !(isBasicPlan && isLimitedTech);

  const hasImportExportView = canViewImportExportPage(session);
  const hasDataHubView = canViewDatahub(session);
  const hasEdgeView = canViewEdge(session);
  const hasEhsView = canViewEhs(session);

  return {
    isRequester,
    businessTypeKey,
    hasWorkOrderView,
    hasWorkRequestView,
    hasPreventativeMaintenanceView,
    hasAnalyticsDashboardView,
    hasPurchaseOrderView,
    hasMeterView,
    hasChecklistView,
    hasFileView,
    hasRequestPortalView,
    hasLocationView,
    hasAssetView,
    hasPartsView,
    hasCategoryView,
    hasPeopleView,
    hasVendorView,
    hasImportExportView,
    hasDataHubView,
    hasEdgeView,
    hasSchedulerView,
    hasEhsView,
  };
}
