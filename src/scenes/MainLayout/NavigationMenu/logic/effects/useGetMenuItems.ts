import { useHistory } from 'react-router';
import {
  usePartsData,
  useLocationsData,
  useRequestsData,
  useFilesData,
  usePurchaseOrdersData,
  useVendorsData,
  useWorkOrdersData,
} from 'contexts';
import { canViewAssetsDetailPage } from 'helpers/routeProtection';
import { useFeatureFlag, useLocalStorage, useSession } from 'hooks';
import { Analytics, hasAdvancedPermission } from 'utils';
import { Modules } from 'utils/Analytics/eventTypes/NavigationEvents';
import { SessionInterface } from 'types';
import { openOverhypedAI } from 'utils/frontEndMetrics';
import getConfig from 'utils/config';
import useNavPermissions from './useNavPermissions';
import useSubscriptionNavigation from './useSubscriptionNavigation';
import { NavigationItemProps } from '../../components/NavigationItem';

export default function getMenuItems(sidebarBadges: any) {
  // Note: match prop not correct in container component
  const { location } = useHistory();
  const session: SessionInterface = useSession();
  const [user] = useLocalStorage('currentUser');
  const path = location.pathname;

  const { handleSubscription, showSubscriptionManagement } =
    useSubscriptionNavigation();

  const { listView: filesListView }: any = useFilesData();
  const { listView: requestsListView }: any = useRequestsData();
  const { listView: locationsListView }: any = useLocationsData();
  const { listView: partsListView }: any = usePartsData();
  const { listView: purchaseOrdersListView }: any = usePurchaseOrdersData();
  const { listView: vendorsListView }: any = useVendorsData();
  const { workOrderV2Enabled } = useWorkOrdersData();
  const featureFlagAssetDetails = canViewAssetsDetailPage();
  const featureFlagFiles = useFeatureFlag('ampfAccountFiles');
  const featureFlagPurchaseOrders = useFeatureFlag('ampfAccountPurchaseOrders');
  const overHypedAIEnabled = useFeatureFlag('over-hyped-ai-enabled');
  const intelligenceEnabled = useFeatureFlag('web-intelligence');

  const {
    isRequester,
    businessTypeKey,
    hasWorkOrderView,
    hasWorkRequestView,
    hasPreventativeMaintenanceView,
    hasAnalyticsDashboardView,
    hasPurchaseOrderView,
    hasMeterView,
    hasChecklistView,
    hasFileView,
    hasRequestPortalView,
    hasLocationView,
    hasAssetView,
    hasPartsView,
    hasPeopleView,
    hasVendorView,
    hasImportExportView,
    hasEdgeView,
    hasSchedulerView,
    hasEhsView,
  } = useNavPermissions();

  const onMenuItemClick = (module: Modules) => () => {
    Analytics.track('GeneralNavigation.ModuleViewed', {
      ModuleViewed: module,
      Platform: 'Web',
      UserRole: session?.analyticsAccountType,
    });
  };

  const menuItems: NavigationItemProps[] = [
    {
      id: 'main-nav-work-orders',
      icon: 'workOrder',
      title: 'mainMenu.list.workOrders.defaultTitle',
      class: 'work-orders',
      path: workOrderV2Enabled ? '/work-orders/list' : '/work-orders',
      active:
        path === '/work-orders' ||
        (path.includes('/work-orders') && path.includes('/list')) ||
        (path.includes('/work-orders') && path.includes('/column')) ||
        (path.includes('/work-orders') && path.includes('/calendar')) ||
        (path.includes('/work-orders') && path.includes('/details')) ||
        (path.includes('/work-orders') && path.includes('/parts')) ||
        (path.includes('/work-orders') && path.includes('/activity')) ||
        (path.includes('/work-orders') && path.includes('/labor')) ||
        (path.includes('/work-orders') && path.includes('/costs')) ||
        (path.includes('/work-orders') && path.includes('/files')),
      hasAccess: hasWorkOrderView,
      onClick: onMenuItemClick('WorkOrders'),
    },
    {
      id: 'main-nav-preventative-maintenance',
      icon: 'pm',
      title: 'mainMenu.list.preventiveMaintenance.defaultTitle',
      class: 'preventative-maintenance',
      path: '/preventive-maintenance/list',
      active: path.startsWith('/preventive-maintenance'),
      disabled: !hasPreventativeMaintenanceView,
      hasAccess: hasPreventativeMaintenanceView,
      learnMore: 'https://www.onupkeep.com/preventive-maintenance-software',
      onClick: onMenuItemClick('PreventativeMaintenance'),
    },
    {
      id: 'main-nav-scheduler',
      icon: 'calendar',
      title: 'mainMenu.list.scheduler.defaultTitle',
      path: '/scheduler',
      active: path.includes('/scheduler'),
      hasAccess: hasSchedulerView,
      onClick: onMenuItemClick('Scheduler'),
      newBadge: true,
    },
    {
      id: 'main-nav-analytics',
      icon: 'analytics',
      title: 'mainMenu.list.reporting.menu',
      path: '/analytics',
      class: 'analytics',
      active:
        path.endsWith('/leaderboard') ||
        path.startsWith('/reporting') ||
        path.startsWith('/analytics'),
      badge: 0,
      disabled: !hasAnalyticsDashboardView,
      hasAccess: hasAnalyticsDashboardView,
      learnMore: 'https://www.onupkeep.com/blog/upkeep-analytics',
      onClick: onMenuItemClick('Analytics'),
    },
    {
      id: 'main-nav-requests',
      icon: 'requests',
      title: 'mainMenu.list.requests.defaultTitle',
      path: `/requests/${requestsListView}`,
      class: 'requests',
      active: path.includes('/requests/'),
      badge: sidebarBadges ? sidebarBadges.totalRequests : 0,
      hasAccess: hasWorkRequestView,
      onClick: onMenuItemClick('Requests'),
    },
    {
      spacer: true,
      hasAccess: true,
    },
    {
      id: 'main-nav-locations',
      icon: 'locations',
      title: `${businessTypeKey}.locationsName`,
      path: `/locations/${locationsListView}`,
      active: path.includes('/locations/'),
      class: 'locations',
      hasAccess: hasLocationView,
      onClick: onMenuItemClick('Locations'),
    },
    {
      id: 'main-nav-assets',
      icon: 'cube',
      title: `${businessTypeKey}.assetsName`,
      path: featureFlagAssetDetails ? '/assets/list' : '/#/app/assets',
      class: 'assets',
      active: path.includes('/assets/'),
      hasAccess: hasAssetView,
      onClick: onMenuItemClick('Assets'),
    },
    {
      id: 'main-nav-parts',
      icon: 'parts',
      title: 'mainMenu.list.parts.defaultTitle',
      class: 'parts',
      path:
        session?.company?.displayHierarchicalParts &&
        hasAdvancedPermission(session?.permissions, 'partMultipleLocation')
          ? `/inventory/${partsListView}`
          : `/parts/${partsListView}`,
      active: path.includes('/parts/'),
      badge: sidebarBadges ? sidebarBadges.totalRunningLowParts : 0,
      hasAccess: hasPartsView,
      onClick: onMenuItemClick('PartsAndInventory'),
    },
    {
      id: 'main-nav-purchase-orders',
      icon: 'purchaseOrder',
      title: 'mainMenu.list.purchaseOrders.defaultTitle',
      path: featureFlagPurchaseOrders
        ? `/purchase-orders/${purchaseOrdersListView}`
        : '/#/app/purchase-orders',
      class: 'purchase-orders',
      active: path.includes('/purchase-orders/'),
      disabled: !hasPurchaseOrderView,
      hasAccess: hasPurchaseOrderView,
      learnMore: 'https://www.upkeep.com/purchase-order-software',
      badge: sidebarBadges?.purchaseOrders || 0,
      onClick: onMenuItemClick('PurchaseOrders'),
    },
    {
      id: 'main-nav-meters',
      icon: 'meter',
      title: 'mainMenu.list.meters.defaultTitle',
      path: '/meters',
      class: 'meters',
      active: path === '/meters',
      disabled: !hasMeterView,
      badge: sidebarBadges ? sidebarBadges.pastDueMeters : 0,
      hasAccess: hasMeterView,
      learnMore: 'https://www.onupkeep.com/meter-reading-software',
      onClick: onMenuItemClick('Meters'),
    },
    {
      spacer: true,
      hasAccess:
        hasLocationView ||
        hasAssetView ||
        hasPartsView ||
        hasPurchaseOrderView ||
        hasMeterView,
    },
    {
      id: 'main-nav-people',
      icon: 'people',
      title: 'mainMenu.list.peopleTeams.defaultTitle',
      path: '/people/list',
      class: 'people',
      active: path.includes('/people') || path.includes('/teams'),
      hasAccess: hasPeopleView,
      onClick: onMenuItemClick('PeopleAndTeams'),
    },
    {
      id: 'main-nav-vendors',
      icon: 'vendors',
      title: 'mainMenu.list.vendorsCustomers.defaultTitle',
      path: `/vendors/${vendorsListView}`,
      class: 'vendors',
      active: ['/vendors/', '/customers/'].some((p) => path.includes(p)),
      hasAccess: hasVendorView,
      onClick: onMenuItemClick('VendorsAndCustomers'),
    },
    {
      spacer: true,
      hasAccess: hasPeopleView || hasVendorView,
    },
    {
      id: 'main-nav-checklists',
      icon: 'checklists',
      title: 'mainMenu.list.formTemplates.defaultTitle',
      path: '/checklists',
      class: 'checklists',
      active: path.includes('/checklists'),
      disabled: !hasChecklistView,
      hasAccess: hasChecklistView,
      onClick: onMenuItemClick('Checklists'),
    },
    {
      id: 'main-nav-files',
      icon: 'files',
      title: 'mainMenu.list.files.defaultTitle',
      class: 'files',
      path: featureFlagFiles
        ? `/files/${filesListView}`
        : '/#/app/account/files',
      active: path.includes('/files/'),
      disabled: !hasFileView,
      hasAccess: hasFileView,
      learnMore: 'https://www.upkeep.com/support/how-to-add-files',
      onClick: onMenuItemClick('Files'),
    },
    {
      id: 'main-nav-request-portal',
      icon: 'requestPortal',
      title: 'mainMenu.list.requestPortal.defaultTitle',
      path: `/settings/sections/requests/request-portal`,
      class: 'request-portal',
      active: path === '/public-requests',
      disabled: !hasRequestPortalView,
      hasAccess: hasRequestPortalView,
      learnMore:
        'https://help.onupkeep.com/en/articles/4730258-how-to-utilize-your-company-request-portal',
      onClick: onMenuItemClick('RequestPortal'),
    },
    {
      id: 'main-nav-import-export',
      icon: 'export',
      title: 'mainMenu.list.importExport.defaultTitle',
      class: 'importExport',
      path: (() => {
        const permissions = {
          workOrderImport: 'workorder',
          preventativeMaintenanceImport: 'pmtrigger',
          locationImport: 'location',
          assetImport: 'asset',
          partImport: 'part',
          partInventoryImport: 'partinventory',
          meterImport: 'meter',
          peopleImport: 'people',
          vendorImport: 'vendor',
          customerImport: 'customer',
          checklistImport: 'checklist',
        };
        return (
          Object.entries(permissions).map(
            ([permission, p]) =>
              hasAdvancedPermission(session.permissions, permission) &&
              `/imports/${p}`,
          )[0] || ''
        );
      })(),
      active: path.includes('/imports'),
      hasAccess: hasImportExportView,
      onClick: onMenuItemClick('ImportAndExport'),
    },
    {
      spacer: true,
      hasAccess:
        hasChecklistView ||
        hasFileView ||
        hasRequestPortalView ||
        hasImportExportView,
    },
    {
      id: 'main-nav-edge',
      icon: 'edge',
      title: 'mainMenu.list.edge.defaultTitle',
      class: 'edge',
      path: '/edge',
      active: path.includes('/edge'),
      hasAccess: hasEdgeView,
      onClick: onMenuItemClick('Edge'),
    },
    {
      id: 'main-nav-ehs',
      icon: 'safety',
      title: 'mainMenu.list.ehs.defaultTitle',
      class: 'ehs',
      path: '/ehs/events',
      active: path.includes('/ehs'),
      hasAccess: hasEhsView && !isRequester,
      onClick: onMenuItemClick('EHS'),
      newBadge: false,
    },
    {
      id: 'main-nav-ehs',
      icon: 'safety',
      title: 'mainMenu.list.ehs.defaultTitle',
      class: 'marketing-ehs',
      path: '/marketing-ehs',
      active: path.includes('/marketing-ehs'),
      hasAccess: !hasEhsView && !isRequester,
      onClick: onMenuItemClick('EHS'),
      newBadge: true,
    },
    {
      id: 'main-nav-intelligence',
      icon: 'brain',
      title: 'mainMenu.list.intelligence.defaultTitle',
      class: 'intelligence',
      hasAccess:
        intelligenceEnabled &&
        user?.userAccountType === '1' &&
        user?.groupId === 1,
      onClick: () => {
        // Track analytics
        onMenuItemClick('Intelligence')();
        // Open external link in new tab
        window.open(
          getConfig('UPKEEP_INTELLIGENCE_URL_EXTERNAL') || '',
          '_blank',
          'noopener,noreferrer',
        );
      },
      customBadge: 'Beta',
    },
  ];

  const handleContact = () => {
    // @ts-ignore
    if (!window.Intercom) {
      return;
    }
    // @ts-ignore
    window.Intercom('showNewMessage');
  };

  const handleHelp = () => {
    if (overHypedAIEnabled) {
      openOverhypedAI();
    } else {
      window.open(`https://help.onupkeep.com`, '_blank');
    }
  };

  const bottomMenuItems: NavigationItemProps[] = [
    {
      id: 'main-nav-help',
      icon: 'question',
      title: 'mainMenu.bottom.help',
      hasAccess: true,
      onClick: handleHelp,
      notLink: true,
    },
    {
      id: 'main-nav-contact-us',
      icon: 'chat',
      title: 'mainMenu.bottom.contactButton',
      hasAccess: true,
      onClick: handleContact,
      notLink: true,
    },
    {
      id: 'main-nav-subscription',
      icon: 'creditCard',
      title: 'mainMenu.bottom.subscriptionButton',
      hasAccess: showSubscriptionManagement,
      onClick: handleSubscription,
      notLink: true,
    },
    {
      id: 'main-nav-settings',
      icon: 'settings',
      title: 'mainMenu.bottom.settingsButton',
      path: '/settings/sections',
      pathname: location?.pathname,
      active: path.includes('settings'),
      hasAccess: hasAdvancedPermission(
        session.permissions,
        'generalSettingView',
      ),
      onClick: onMenuItemClick('Settings'),
    },
  ];

  return { menuItems, bottomMenuItems, isRequester };
}
