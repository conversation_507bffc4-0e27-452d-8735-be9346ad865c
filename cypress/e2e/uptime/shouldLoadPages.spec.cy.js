import filterTests from '../../support/filterTests';
import { upkeepPages, leftNavigation } from '../../support/constants';
import * as partsPages from '../parts/components';

filterTests(['all', 'smoke'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const testId = 'shouldLoadUpkeepPages';
  const teamId = 'super awesome team';

  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
  };

  describe('Should load Upkeep Pages', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamId, emails);
    });

    it('should load parts/sets page', () => {
      upkeepPages.PARTS.go();
      partsPages.list.createPart.shouldBeVisible();
      partsPages.list.setsTab.shouldBeVisible();
      partsPages.list.setsTab.click();
      partsPages.list.createSetButton.shouldBeVisible();
    });

    it('should load vendors page', () => {
      cy.get(leftNavigation.VENDORS_CUSTOMERS.navSelector).click();
      cy.contains('Vendors & Customers').should('be.visible');
      cy.contains('Vendor').should('be.visible');
      cy.contains('Customers').should('be.visible');
      cy.contains('No vendors').should('be.visible');
    });

    it.skip('should load shared work orders page', () => {
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with me').should('be.visible');
      cy.contains('Shared with others').should('be.visible');
    });

    it('should load locations page', () => {
      cy.get(leftNavigation.LOCATIONS.navSelector).click();
      cy.contains('[data-cy="generic.list"]', 'List').should('be.visible');
      cy.contains('[data-cy="generic.map"]', 'Map').should('be.visible');
      cy.contains('button', 'Create Location').should('be.visible');
    });

    it('should load checklists page', () => {
      cy.get(leftNavigation.CHECKLISTS.navSelector).click();
      cy.contains('h2', 'Add Checklist').should('be.visible');
      cy.contains('button', 'Your Checklists').should('be.visible');
      cy.contains('button', 'Template Library').should('be.visible');
      cy.contains('button', 'Add Checklist').should('be.visible');
    });

    it(
      'should load files page',
      { featureFlags: { ampfAccountFiles: true } },
      () => {
        cy.get(leftNavigation.FILES.navSelector).click();
        cy.reload();
        cy.contains('h2', 'Add Files').should('be.visible');
        cy.contains('button', 'Add Files').should('be.visible');
      },
    );

    it('should load requests portal page', () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      // Check if request portal nav directs to within settings page
      // or to old request portal page
      cy.url().then((requestUrl) => {
        if (requestUrl.includes('settings/sections')) {
          cy.get('[data-cy="Public Requests"]').should('be.visible');
          cy.get('[data-cy="Internal Requests"]').should('be.visible');
          cy.get(
            '[data-cy="pages.requestPortal.companyRequestPortalHelper"]',
          ).should('be.visible');
          cy.get('[aria-label="toggle switch"]').click();

          cy.contains('a', '/work-orders/create')
            .invoke('text')
            .then((url) => {
              cy.visit(url, { failOnStatusCode: false });
            });
        } else {
          cy.contains('Request Portal');
          cy.contains('h2', 'Request Portal').should('be.visible');
          cy.get('[title="Enable Request Portal"] button').click();
          cy.get('[href*="/publicrequest/"]')
            .invoke('attr', 'href')
            .then((url) => {
              cy.visit(url, { failOnStatusCode: false });
            });
        }
      });

      cy.contains('Submit Work Order Request').should('be.visible');
      cy.contains('Work Order Requests').click();

      cy.get('input[placeholder="Enter your email address"]').type(
        emails.ADMIN,
      );
      cy.contains('button', 'Log In').click();
      cy.contains('My Work Order Requests').should('be.visible');
    });

    it('should load purchase orders page', () => {
      cy.get(leftNavigation.PURCHASE_ORDERS.navSelector).click();
      cy.contains('Purchase Order').should('be.visible');
      cy.contains('button', 'Create Purchase Order').should('be.visible');
    });

    it('should load analytics page', () => {
      cy.get(leftNavigation.ANALYTICS.navSelector).click();
      cy.contains('UpKeep Analytics').should('exist');
    });
  });
});
