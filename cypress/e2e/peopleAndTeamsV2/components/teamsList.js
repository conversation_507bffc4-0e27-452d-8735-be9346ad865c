import { configureSelectorProxy } from '../../../helpers';

const teamsList = configureSelectorProxy({
  teamsTab: '[data-cy="generic.upkeepEntity.teams"]',
  addTeamButton: 'button:contains("Add Team")',
  teamNameInput: '#mainDescription',
  workersDropdown: '#WorkersListButton',
  workersListSearchInput: '#menu-list-search',
  workerInDropdownCheckbox: (user) => `li [data-cy="${user}"]`,
  createTeamButton: 'button:contains("Create Team")',
  teamInList: (team) => `td:contains("${team}")`,
  teamInListCheckbox: (team) => `td:contains("${team}") [type="checkbox"]`,
  numOfPeopleInTeam: (team) =>
    `tr:contains("${team}") td[class*="numberOfPeople"]`,
  saveChangesButton: 'button:contains("Save Changes")',
  teamNameRow: 'tbody td[class*="name"]',
  searchBar: '#search-bar',
});

export default teamsList;
