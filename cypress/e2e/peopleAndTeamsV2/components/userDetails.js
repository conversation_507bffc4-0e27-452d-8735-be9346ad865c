import { configureSelectorProxy } from '../../../helpers';

const userDetails = configureSelectorProxy({
  name: '.drawer [data-cy="Name"]',
  email: '.drawer [data-cy="Email"]',
  phoneNumber: '.drawer [data-cy="Phone Number"]',
  jobTitle: '.drawer [data-cy="Job Title"]',
  workOrdersTab: '[data-cy="generic.upkeepEntity.workorders"]',
  editButton: 'button:contains("Edit")',
  firstNameInput: '#firstName',
  lastNameInput: '#lastName',
  phoneNumberInput: '#phoneNumber',
  jobTitleInput: '#jobTitle',
  saveChangesButton: 'button:contains("Save Changes")',
  selectUserRoleButton: 'button:contains("Select User Role")',
  accountType: (role) => `#portal span[data-cy="${role}"]`,
  locationBasedCheckbox: 'div:contains("Is Location Based") [type="checkbox"]',
  selectLocationsButton: 'button:contains("Select Locations")',
  locationsPickerSearch: '[id="Select LocationsPickerSearch"]',
  locationSelectCheckbox: (location) =>
    `td:contains("${location}") [type="checkbox"]`,
  saveLocationSelectButton: 'div[class*="card-footer"] [data-cy="Save"]',
  locationBasedPermissions: (location) =>
    `[data-cy="Location Based Permissions"] a:contains("${location}")`,
  optionsButton: 'div[class*="card"] [data-cy="icon-button"]',
  deactivateOption: '[data-cy="menu-item-Deactivate"]',
  activateOption: '[data-cy="menu-item-Activate"]',
  deleteOption: '[data-cy="menu-item-Delete"]',
  confirmButton: '[data-cy="Confirm"]',
  confirmDeleteButton: 'button[data-cy="Delete"]',
});

export default userDetails;
