import { configureSelectorProxy } from '../../../helpers';

const inviteUsersModal = configureSelectorProxy({
  addPersonButton: 'button:contains("Add Person")',
  emailInput: 'div[class*="input-wrapper"]:contains("Email") input[value=""]',
  selectRoleButton: 'button:contains("Select User Role")',
  userRoleDropdownSelection: (role) =>
    `div[class*="card-inner-content"] td:contains("${role}")`,
  addUserButton: 'button:contains("Add User")',
  inviteButton: 'button:contains("Invite")',
});

export default inviteUsersModal;
