import { configureSelectorProxy } from '../../../helpers';

const peopleList = configureSelectorProxy({
  addPersonButton: 'button:contains("Add Person")',
  userInList: (email) => `td [data-cy="${email}"]`,
  userCheckbox: (email) => `tr:contains("${email}") input`,
  includeDeactivatedCheckbox: '[class*="filter-bar"] [type="checkbox"]',
  deleteToastButton: '[class="toast-container"] [role="presentation"]',
  deletePeopleCard: (num) => `span:contains("Delete ${num} People?")`,
  confirmDeleteButton: '[class*="card-footer"] [data-cy="Delete"]',
  userNameRow: 'tbody td[class*="name"]',
  searchBar: '#search-bar',
});

export default peopleList;
