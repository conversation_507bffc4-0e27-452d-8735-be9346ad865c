import { upkeepPages } from '../../../../support/constants';
import * as teamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canDeleteTeam = (emails) => {
  it(
    'Admin can delete a team',
    {
      testCaseId: 'QA-T6505',
    },
    () => {
      const now = Date.now();
      const team = `TeamA ${now}`;

      upkeepPages.PEOPLE_TEAMS_V2.go();
      teamsHelpers.visitTeamsPage();
      teamsHelpers.addTeam(team, emails);
      teamsHelpers.verifyTeamInList(team, emails);
      teamsHelpers.deleteSelectedTeam(team);
      teamsHelpers.verifyTeamDeleted(team);
    },
  );
};

export default canDeleteTeam;
