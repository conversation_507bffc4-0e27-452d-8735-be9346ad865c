import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canEnableLocationBased = (emails) => {
  it(
    'Admin can set user as Location-Based',
    {
      testCaseId: 'QA-T6497',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const location = `Location ${now}`;

      h.createLocation({ stringName: location });
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(emails.LIMITED_TECH);
      peopleAndTeamsHelpers.clickEditPerson();
      peopleAndTeamsHelpers.enableLocationBased(location);
      peopleAndTeamsHelpers.verifyLocationBased(location);
    },
  );
};

export default canEnableLocationBased;
