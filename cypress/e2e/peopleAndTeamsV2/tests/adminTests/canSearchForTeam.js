import { upkeepPages } from '../../../../support/constants';
import * as teamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canSearchForTeam = (emails) => {
  it(
    'Admin can search for a team',
    {
      testCaseId: 'QA-T6506',
    },
    () => {
      const now = Date.now();
      const team = `TeamF ${now}`;

      upkeepPages.PEOPLE_TEAMS_V2.go();
      teamsHelpers.visitTeamsPage();
      teamsHelpers.addTeam(team, emails);
      teamsHelpers.searchForTeam(team);
    },
  );
};

export default canSearchForTeam;
