import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canViewUserDetails = (testId) => {
  it(
    '<PERSON><PERSON> can view user details',
    {
      testCaseId: 'QA-T6468',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const data = {
        firstName: `Test ${now}`,
        lastName: `User ${now}`,
        jobTitle: 'Tester',
        hourlyRate: '45',
        phoneNumber: '(*************',
        email: `engineering-test+${testId}_admin_${now}@${domain}`,
      };

      h.createNewUser(data);
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(data.email);
      peopleAndTeamsHelpers.verifyUserDetails(data);
    },
  );
};

export default canViewUserDetails;
