import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canDeleteUser = () => {
  const now = Date.now();
  const testId = 'delete-user';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  it(
    'Admin can delete a user',
    {
      testCaseId: 'QA-T6487',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const email = `engineering-test+${testId}_tech_${now}@${domain}`;

      h.createNewUser({ email });
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(email);
      peopleAndTeamsHelpers.deleteUser();
      peopleAndTeamsHelpers.includeDeactivatedUsers();
      peopleAndTeamsHelpers.verifyUserDeleted(email);
    },
  );

  it(
    'Admin can delete multiple users at a time',
    {
      testCaseId: 'QA-T6488',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const emails = {
        LIMITED_TECH: `engineering-test+${testId}_limtech_${now}@${domain}`,
        VIEW_ONLY: `engineering-test+${testId}_vo_${now}@${domain}`,
        REQUESTER: `engineering-test+${testId}_requester_${now}@${domain}`,
      };

      Object.keys(emails).forEach((user) => {
        h.createNewUser({ email: emails[user] });
      });
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.deleteMultUsersInList(emails);
    },
  );
};

export default canDeleteUser;
