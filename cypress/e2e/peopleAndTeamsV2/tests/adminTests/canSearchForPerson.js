import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canSearchForPerson = (testId) => {
  it(
    '<PERSON><PERSON> can search for a person',
    {
      testCaseId: 'QA-T6507',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const email = `engineering-test+${testId}_${now}@${domain}`;
      h.createNewUser({ email });
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.searchForPerson(email);
    },
  );
};

export default canSearchForPerson;
