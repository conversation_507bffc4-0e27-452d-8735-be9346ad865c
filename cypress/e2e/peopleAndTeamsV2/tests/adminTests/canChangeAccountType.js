import { upkeepPages } from '../../../../support/constants';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canChangeAccountType = (emails) => {
  it(
    'Admin can change user account type',
    {
      testCaseId: 'QA-T6478',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const role = 'ADMIN';
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(emails.LIMITED_TECH);
      peopleAndTeamsHelpers.clickEditPerson();
      peopleAndTeamsHelpers.changeUserRole(role);
      peopleAndTeamsHelpers.verifyUserAccountType(role);
    },
  );
};

export default canChangeAccountType;
