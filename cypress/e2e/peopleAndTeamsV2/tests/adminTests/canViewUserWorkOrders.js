import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canViewUserWorkOrders = (testId) => {
  it(
    'Admin can view work orders assigned to a user via People - Work Orders tab',
    {
      testCaseId: 'QA-T6470',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const email = `engineering-test+${testId}_admin_${now}@${domain}`;
      const title = `People Admin WO ${now}`;
      const description = `work order with assigned user`;

      // Create and assign new user to a work order
      h.createNewUser({ email }).then((response) => {
        const userId = response.body.result.id;

        h.createV2WorkOrder({
          title,
          description,
          assignedToUser: userId,
        });
      });

      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(email);
      peopleAndTeamsHelpers.visitWorkOrdersTab();
      peopleAndTeamsHelpers.verifyWorkOrderInTab(title);
    },
  );
};

export default canViewUserWorkOrders;
