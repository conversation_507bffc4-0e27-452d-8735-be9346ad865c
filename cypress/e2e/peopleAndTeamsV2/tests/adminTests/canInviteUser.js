import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canInviteUser = (testId) => {
  it(
    'Admin can invite users',
    {
      testCaseId: 'QA-T6435',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const emails = {
        ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
        LIMITED_ADMIN: `engineering-test+${testId}_limitedadmin_${now}@${domain}`,
        TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
        LIMITED_TECH: `engineering-test+${testId}_limitedtech_${now}@${domain}`,
        VIEW_ONLY: `engineering-test+${testId}_viewonly_${now}@${domain}`,
        REQUESTER: `engineering-test+${testId}_requester_${now}@${domain}`,
        CUSTOM_ROLE: `engineering-test+${testId}_customrole_${now}@${domain}`,
      };
      const roleName = `Custom Role ${now}`;

      h.createRole({ name: roleName, description: roleName });
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.inviteUsers(emails);
      peopleAndTeamsHelpers.verifyUsersInvited(emails);
    },
  );
};

export default canInviteUser;
