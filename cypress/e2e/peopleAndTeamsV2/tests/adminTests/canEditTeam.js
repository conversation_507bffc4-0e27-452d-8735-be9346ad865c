import { upkeepPages } from '../../../../support/constants';
import * as teamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canEditTeam = (team, emails) => {
  it(
    'Admin can edit a team',
    {
      testCaseId: 'QA-T6494',
    },
    () => {
      const now = Date.now();
      const newTeamName = `TeamC ${now}`;
      const data = {
        ADMIN: emails.ADMIN,
        TECH: emails.TECH,
      };

      upkeepPages.PEOPLE_TEAMS_V2.go();
      teamsHelpers.visitTeamsPage();
      teamsHelpers.selectTeamInList(team);
      teamsHelpers.editTeam(newTeamName, data.TECH);
      teamsHelpers.verifyTeamInList(newTeamName, data);
    },
  );
};

export default canEditTeam;
