import { upkeepPages } from '../../../../support/constants';
import * as teamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canCreateTeam = (emails) => {
  it(
    'Admin can create a team with at least 1 member',
    {
      testCaseId: 'QA-T6491',
    },
    () => {
      const now = Date.now();
      const team = `TeamB ${now}`;

      upkeepPages.PEOPLE_TEAMS_V2.go();
      teamsHelpers.visitTeamsPage();
      teamsHelpers.addTeam(team, emails);
      teamsHelpers.verifyTeamInList(team, emails);
    },
  );
};

export default canCreateTeam;
