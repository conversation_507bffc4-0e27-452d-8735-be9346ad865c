import { upkeepPages } from '../../../../support/constants';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canEditUser = (emails) => {
  it(
    'Admin can edit user information',
    {
      testCaseId: 'QA-T6477',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      const now = Date.now();
      const data = {
        email: emails.TECH,
        firstName: `Test ${now}`,
        lastName: `User ${now}`,
        phoneNumber: '(*************',
        hourlyRate: '46',
        jobTitle: 'Tester2',
        accountType: 'TECH',
      };

      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(data.email);
      peopleAndTeamsHelpers.editUserDetails(data);
      peopleAndTeamsHelpers.verifyUserDetails(data);
    },
  );
};

export default canEditUser;
