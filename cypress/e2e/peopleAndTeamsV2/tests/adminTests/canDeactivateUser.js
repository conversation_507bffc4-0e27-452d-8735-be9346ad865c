import { upkeepPages } from '../../../../support/constants';
import * as peopleAndTeamsHelpers from '../../helpers/peopleAndTeamsHelpers';

const canDeactivateUser = (emails) => {
  it(
    'Admin can deactivate a user',
    {
      testCaseId: 'QA-T6483',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.selectUserInList(emails.LIMITED_TECH);
      peopleAndTeamsHelpers.deactivateUser();
    },
  );

  it(
    'Admin can reactivate a user',
    {
      testCaseId: 'QA-T6484',
      featureFlags: { ampfAccountPeopleAndTeamsV2: true },
    },
    () => {
      upkeepPages.PEOPLE_TEAMS_V2.go();
      peopleAndTeamsHelpers.includeDeactivatedUsers();
      peopleAndTeamsHelpers.selectUserInList(emails.LIMITED_TECH);
      peopleAndTeamsHelpers.activateUser();
    },
  );
};

export default canDeactivateUser;
