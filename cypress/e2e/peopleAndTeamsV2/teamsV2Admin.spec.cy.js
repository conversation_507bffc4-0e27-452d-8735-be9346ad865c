import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const now = Date.now();
  const testId = 'teamstier2';
  const team = 'Team Tier2';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    LIMITED_TECH: `engineering-test+${testId}_limtech_${now}@${domain}`,
  };

  describe('Teams v2 - Admin Tier2 Tests', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH', 'LIMITED_TECH'],
        'BUSINESS_PLUS',
        team,
        emails,
      );
    });

    adminTests.canDeleteTeam(emails);
    adminTests.canSearchForTeam(emails);
    adminTests.canViewTeams(emails);
  });
});

filterTests(['all', 'smoke', 'ui'], () => {
  const now = Date.now();
  const testId = 'teamsadminsmoke';
  const team = 'Team Smoke';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    LIMITED_TECH: `engineering-test+${testId}_limtech_${now}@${domain}`,
  };

  describe('Teams v2 - Admin Smoke Tests', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH', 'LIMITED_TECH'],
        'BUSINESS_PLUS',
        team,
        emails,
      );
    });

    adminTests.canCreateTeam(emails);
    adminTests.canEditTeam(team, emails);
  });
});
