import * as peopleTeamPages from '../components';
import * as h from '../../../helpers';

const userRoles = {
  ADMIN: 'Administrator',
  LIMITED_ADMIN: 'Limited Administrator',
  TECH: 'Technician',
  LIMITED_TECH: 'Limited Technician',
  VIEW_ONLY: 'View Only',
  REQUESTER: 'Requester',
  CUSTOM_ROLE: 'Custom Role',
};

export const inviteUsers = (emails) => {
  // Open Add Person/Invite User modal
  peopleTeamPages.peopleList.addPersonButton.shouldBeVisible().click();
  cy.contains('Invite Users').should('be.visible');

  // Input user emails
  Object.keys(emails).forEach((user, index) => {
    peopleTeamPages.inviteUsersModal.emailInput.scrollIntoView();
    peopleTeamPages.inviteUsersModal.emailInput.click().type(emails[user]);
    peopleTeamPages.inviteUsersModal.selectRoleButton.scrollIntoView();
    peopleTeamPages.inviteUsersModal.selectRoleButton.click();
    peopleTeamPages.inviteUsersModal
      .userRoleDropdownSelection(userRoles[user])
      .click({ force: true });
    if (index < Object.keys(emails).length - 1) {
      peopleTeamPages.inviteUsersModal.addUserButton.scrollIntoView();
      peopleTeamPages.inviteUsersModal.addUserButton.click();
    }
  });

  // Click Invite button
  peopleTeamPages.inviteUsersModal.inviteButton.click();
  cy.contains('Users invited succesfully').should('be.visible');
};

// Invited users should be showing on People list
export const verifyUsersInvited = (emails) => {
  Object.values(emails).forEach((val) => {
    peopleTeamPages.peopleList.userInList(val).shouldBeVisible();
  });
};

export const selectUserInList = (email) => {
  peopleTeamPages.peopleList.userInList(email).scrollIntoView();
  peopleTeamPages.peopleList.userInList(email).click({ force: true });
};

/**
 * Onboard using by email
 * @param {object} data - firstName, lastName, email, phoneNumber, [optional] jobTitle
 */
export const verifyUserDetails = (data) => {
  peopleTeamPages.userDetails.name.shouldContain(data.firstName);
  peopleTeamPages.userDetails.name.shouldContain(data.lastName);
  peopleTeamPages.userDetails.email.shouldContain(data.email);
  peopleTeamPages.userDetails.phoneNumber.shouldContain(data.phoneNumber);
  if (data.jobTitle) {
    peopleTeamPages.userDetails.jobTitle.shouldContain(data.jobTitle);
  }
};

export const visitWorkOrdersTab = () => {
  peopleTeamPages.userDetails.workOrdersTab.shouldBeVisible();
  peopleTeamPages.userDetails.workOrdersTab.click();
};

// Verify the work order assigned to a user shows
export const verifyWorkOrderInTab = (title) => {
  cy.contains('td', title).should('be.visible');
};

export const clickEditPerson = () => {
  peopleTeamPages.userDetails.editButton.shouldBeVisible();
  peopleTeamPages.userDetails.editButton.click();
};

/**
 * Onboard using by email
 * @param {object} data - firstName, lastName, phoneNumber, jobTitle
 */
export const editUserDetails = (data) => {
  clickEditPerson();
  // Input first name
  peopleTeamPages.userDetails.firstNameInput.click();
  peopleTeamPages.userDetails.firstNameInput.type(data.firstName);
  // Input last name
  peopleTeamPages.userDetails.lastNameInput.click();
  peopleTeamPages.userDetails.lastNameInput.type(data.lastName);
  // Input phone number
  peopleTeamPages.userDetails.phoneNumberInput.click();
  peopleTeamPages.userDetails.phoneNumberInput.type(data.phoneNumber);
  // Input job title
  peopleTeamPages.userDetails.jobTitleInput.click();
  peopleTeamPages.userDetails.jobTitleInput.type(data.jobTitle);
  // Save changes
  peopleTeamPages.userDetails.saveChangesButton.scrollIntoView();
  peopleTeamPages.userDetails.saveChangesButton.click();
};

// Checks to see if user needs to be given a first name
// Essential when editing a new or pending user
export const checkForUserFirstName = () => {
  peopleTeamPages.userDetails.firstNameInput
    .invoke('attr', 'value')
    .then((val) => {
      if (val.length === 0) {
        peopleTeamPages.userDetails.firstNameInput.click();
        peopleTeamPages.userDetails.firstNameInput.type('Tester');
      }
    });
};

// Changes user account type
export const changeUserRole = (role) => {
  checkForUserFirstName();
  peopleTeamPages.userDetails.selectUserRoleButton.scrollIntoView();
  peopleTeamPages.userDetails.selectUserRoleButton.click();
  peopleTeamPages.inviteUsersModal
    .userRoleDropdownSelection(userRoles[role])
    .click();
  peopleTeamPages.userDetails.saveChangesButton.scrollIntoView();
  peopleTeamPages.userDetails.saveChangesButton.click();
  cy.contains('Person updated').should('be.visible');
  cy.url().should('include', 'tab=details');
  cy.reload();
};

export const verifyUserAccountType = (role) => {
  peopleTeamPages.userDetails.accountType(userRoles[role]).shouldBeVisible();
};

export const enableLocationBased = (location) => {
  checkForUserFirstName();
  // Check Is Location Based
  peopleTeamPages.userDetails.locationBasedCheckbox.scrollIntoView();
  peopleTeamPages.userDetails.locationBasedCheckbox.check();
  // Search and select location
  peopleTeamPages.userDetails.selectLocationsButton.shouldBeVisible();
  peopleTeamPages.userDetails.selectLocationsButton.click();
  peopleTeamPages.userDetails.locationsPickerSearch.click();
  peopleTeamPages.userDetails.locationsPickerSearch.type(location);
  peopleTeamPages.userDetails.locationSelectCheckbox(location).check();
  peopleTeamPages.userDetails.saveLocationSelectButton.click();
  // Save changes
  peopleTeamPages.userDetails.saveChangesButton.scrollIntoView();
  peopleTeamPages.userDetails.saveChangesButton.click();
  cy.contains('Person updated').should('be.visible');
  peopleTeamPages.userDetails.editButton.shouldBeVisible();
};

export const setUserLocationBased = (email, location) => {
  selectUserInList(email);
  clickEditPerson();
  enableLocationBased(location);
  h.closeTopPortal();
};

// Confirm user details shows location
export const verifyLocationBased = (location) => {
  peopleTeamPages.userDetails
    .locationBasedPermissions(location)
    .shouldBeVisible();
};

// Filters people list to include deactivated users
export const includeDeactivatedUsers = () => {
  peopleTeamPages.peopleList.userNameRow.shouldHaveLengthGreaterThan(1);
  peopleTeamPages.peopleList.includeDeactivatedCheckbox.shouldBeVisible();
  peopleTeamPages.peopleList.includeDeactivatedCheckbox.check();
};

// Deactivates pending user
export const deactivateUser = () => {
  peopleTeamPages.userDetails.optionsButton.shouldBeVisible();
  peopleTeamPages.userDetails.optionsButton.click();
  peopleTeamPages.userDetails.deactivateOption.shouldBeVisible();
  peopleTeamPages.userDetails.deactivateOption.click();
  peopleTeamPages.userDetails.confirmButton.click();
  cy.contains('span', 'Deactivated').should('be.visible');
};

// Reactivates pending user
export const activateUser = () => {
  peopleTeamPages.userDetails.optionsButton.shouldBeVisible();
  peopleTeamPages.userDetails.optionsButton.click();
  peopleTeamPages.userDetails.activateOption.shouldBeVisible();
  peopleTeamPages.userDetails.activateOption.click();
  peopleTeamPages.userDetails.confirmButton.click();
  cy.contains('span', 'Pending').should('be.visible');
};

export const deleteUser = () => {
  peopleTeamPages.userDetails.optionsButton.shouldBeVisible();
  peopleTeamPages.userDetails.optionsButton.click();
  peopleTeamPages.userDetails.deleteOption.shouldBeVisible();
  peopleTeamPages.userDetails.deleteOption.click();
  peopleTeamPages.userDetails.confirmDeleteButton.click();
};

export const verifyUserDeleted = (email) => {
  peopleTeamPages.peopleList.userInList(email).shouldNotExist();
};

export const deleteMultUsersInList = (emails) => {
  Object.keys(emails).forEach((user) => {
    peopleTeamPages.peopleList.userCheckbox(emails[user]).scrollIntoView();
    peopleTeamPages.peopleList.userCheckbox(emails[user]).check();
  });
  peopleTeamPages.peopleList.deleteToastButton.shouldBeVisible();
  peopleTeamPages.peopleList.deleteToastButton.click();
  // Confirm correct number of users are being deleted
  peopleTeamPages.peopleList
    .deletePeopleCard(Object.entries(emails).length)
    .shouldBeVisible();
  peopleTeamPages.peopleList.confirmDeleteButton.click();
  // Confirm users no longer exist in list
  Object.keys(emails).forEach((user) => {
    verifyUserDeleted(emails[user]);
  });
};

export const searchForPerson = (user) => {
  peopleTeamPages.peopleList.searchBar.shouldBeVisible();
  peopleTeamPages.peopleList.searchBar.click();
  peopleTeamPages.peopleList.searchBar.type(user);
  peopleTeamPages.peopleList.userNameRow.shouldHaveLength(1);
  peopleTeamPages.peopleList.userInList(user).shouldBeVisible();
};

export const visitTeamsPage = () => {
  peopleTeamPages.teamsList.teamsTab.shouldBeVisible();
  peopleTeamPages.teamsList.teamsTab.click();
};

export const addTeam = (team, emails) => {
  peopleTeamPages.teamsList.addTeamButton.shouldBeVisible();
  peopleTeamPages.teamsList.addTeamButton.click();
  // Input team name
  peopleTeamPages.teamsList.teamNameInput.click();
  peopleTeamPages.teamsList.teamNameInput.type(team);
  // Select members for team
  peopleTeamPages.teamsList.workersDropdown.shouldBeVisible();
  peopleTeamPages.teamsList.workersDropdown.click();
  peopleTeamPages.teamsList.workersListSearchInput.click();
  Object.keys(emails).forEach((user) => {
    peopleTeamPages.teamsList.workersListSearchInput.type(emails[user]);
    peopleTeamPages.teamsList.workerInDropdownCheckbox(emails[user]).click();
  });
  // Submit create team
  peopleTeamPages.teamsList.createTeamButton.click({ force: true });
  cy.contains('Team created').should('be.visible');
};

export const verifyTeamInList = (team, emails) => {
  peopleTeamPages.teamsList.teamInList(team).shouldBeVisible();
  peopleTeamPages.teamsList
    .numOfPeopleInTeam(team)
    .shouldContain(Object.entries(emails).length);
};

export const selectTeamInList = (team) => {
  peopleTeamPages.teamsList.teamInList(team).click();
};

export const editTeam = (teamName, email) => {
  // Edit team name
  peopleTeamPages.teamsList.teamNameInput.click();
  peopleTeamPages.teamsList.teamNameInput.type(teamName);
  // Add another user to team
  peopleTeamPages.teamsList.workersDropdown.click();
  peopleTeamPages.teamsList.workerInDropdownCheckbox(email).click();
  // Save changes
  peopleTeamPages.teamsList.saveChangesButton.click({ force: true });
};

export const countTeamsList = () => {
  peopleTeamPages.teamsList.teamNameRow.shouldHaveLengthGreaterThan(0);
};

export const deleteSelectedTeam = (team) => {
  peopleTeamPages.teamsList.teamInListCheckbox(team).check();
  peopleTeamPages.peopleList.deleteToastButton.shouldBeVisible();
  peopleTeamPages.peopleList.deleteToastButton.click();
  peopleTeamPages.peopleList.confirmDeleteButton.click();
  cy.contains('Team deleted').should('be.visible');
};

export const verifyTeamDeleted = (team) => {
  peopleTeamPages.teamsList.teamInList(team).shouldNotExist();
};

export const searchForTeam = (team) => {
  peopleTeamPages.teamsList.searchBar.shouldBeVisible();
  peopleTeamPages.teamsList.searchBar.click();
  peopleTeamPages.teamsList.searchBar.type(team);
  peopleTeamPages.teamsList.teamNameRow.shouldHaveLength(1);
  peopleTeamPages.teamsList.teamInList(team).shouldBeVisible();
};
