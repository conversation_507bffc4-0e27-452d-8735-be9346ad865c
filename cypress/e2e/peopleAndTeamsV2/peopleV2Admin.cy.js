import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  // ampfAccountPeopleAndTeamsV2 has been deprecated
  const now = Date.now();
  const testId = 'peopleadminsmoke';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
  };

  describe('People v2 - Admin Smoke Tests', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH'],
        'BUSINESS_PLUS',
        'Team A',
        emails,
      );
    });

    adminTests.canInviteUser(testId);
    adminTests.canViewUserDetails(testId);
    adminTests.canViewUserWorkOrders(testId);
    adminTests.canEditUser(emails);
  });
});

filterTests(['all', 'tier2', 'ui'], () => {
  const now = Date.now();
  const testId = 'peopleadmintier2';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    LIMITED_TECH: `engineering-test+${testId}_limtech_${now}@${domain}`,
    TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
  };

  describe('People v2 - Admin Tier Tests', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_TECH', 'TECH'],
        'BUSINESS_PLUS',
        'Team T2',
        emails,
      );
    });

    adminTests.canChangeAccountType(emails);
    adminTests.canEnableLocationBased(emails);
    adminTests.canDeactivateUser(emails);
    adminTests.canDeleteUser();
    adminTests.canSearchForPerson(testId);
  });
});
