import { configureSelectorProxy } from '../../../helpers';

const section = configureSelectorProxy({
  tagsModule: '[href*="sections/tags/"]',
  addTagButton: '[data-cy="add-tag-button"]',
  tagNameInput: '#tagName',
  modelsDropdown: '#ModelsListButton',
  modelsOption: (model) => `li[data-cy="${model}"]`,
  portalBackdrop: '[class*="portal-backdrop"]',
  submitTagButton: '[data-cy="submit-tag-button"]',
  tagNameInList: (tag) => `td [data-cy="${tag}"]`,
  optionsForTagInRow: (tag) => `tr:contains("${tag}") [data-cy="icon-button"]`,
  deleteButton: '[role="menuitem"]:contains("Delete")',
  confirmDeleteButton: '[data-cy="Delete"]',
  editButton: '[role="menuitem"]:contains("Edit")',
  modelDeleteButton: (model) => `span.tag-text:contains("${model}") svg`,
  modelNameInList: (row, model) =>
    `td.col-Models[data-cy="${row}"]:contains("${model}")`,
  modelForTagInRow: (tag, model) =>
    `tr:contains("${tag}") td.col-Models:contains("${model}")`,
});

export default section;
