import filterTests from '../../support/filterTests';
import { upkeepPages } from '../../support/constants';
import { checklistsTagsTests } from './tests';
import * as tagHelpers from './helpers/tagsHelpers';

filterTests(['all', 'smoke', 'ui'], () => {
  const now = Date.now();
  const testId = 'tags-checklists';
  const tagName = `Checklist Tag ${now}`;
  const multiModelTagName = `Multi Model Tag ${now}`;

  describe(
    'Tags - Checklists',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      Cypress.on('uncaught:exception', () => false);

      beforeEach(() => {
        cy.createOrLoginAdmin(
          testId,
          ['ADMIN'],
          'BUSINESS_PLUS',
          'My Cool Team',
        );
      });

      it(
        'Admin can create, edit and delete checklist tags in Settings',
        {
          featureFlags: { 'tags-on-checklists': true },
          testCaseId: 'QA-T6680',
        },
        () => {
          upkeepPages.SETTINGS.go();
          tagHelpers.visitTagsModule();
          checklistsTagsTests.canAddChecklistTags(tagName, multiModelTagName);
          checklistsTagsTests.canEditChecklistTags(tagName, multiModelTagName);
          checklistsTagsTests.canDeleteChecklistTags(tagName, now);
        },
      );

      it(
        'Admin can assign tags to checklists',
        {
          featureFlags: { 'tags-on-checklists': true },
          testCaseId: 'QA-T6681',
        },
        () => {
          upkeepPages.CHECKLISTS.go();
          checklistsTagsTests.canAssignTagToChecklist(multiModelTagName, now);
        },
      );
    },
  );
});
