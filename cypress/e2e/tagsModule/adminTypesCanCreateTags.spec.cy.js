import filterTests from '../../support/filterTests';
import { filesTagsTests } from './tests';
import { AVAILABLE_PLANS_DISPLAY_NAMES } from '../../support/planConstants';

filterTests(['all', 'smoke', 'ui'], () => {
  describe(`Tags - Files - Admin ${AVAILABLE_PLANS_DISPLAY_NAMES.BUSINESS_PLUS}`, () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'createTags';
    const teamName = 'super team bros';

    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName, emails);
    });

    filesTagsTests.canCreateTag();
    filesTagsTests.canAssignTagToFile();
  });

  describe(`Tags - Files - Admin ${AVAILABLE_PLANS_DISPLAY_NAMES.BASIC}`, () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'tags-files-admin-basic';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BASIC', '');
    });

    filesTagsTests.canCreateTag();
  });

  describe(`Tags - Files - Admin ${AVAILABLE_PLANS_DISPLAY_NAMES.PROFESSIONAL}`, () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'tags-files-admin-professional';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'PROFESSIONAL', '');
    });

    filesTagsTests.canCreateTag();
    filesTagsTests.canAssignTagToFile();
  });

  describe(`Tags - Files - Admin ${AVAILABLE_PLANS_DISPLAY_NAMES.STARTER}`, () => {
    const testId = 'tags-files-admin-starter';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'STARTER', '');
    });

    filesTagsTests.canCreateTag();
    filesTagsTests.canAssignTagToFile();
  });
});
