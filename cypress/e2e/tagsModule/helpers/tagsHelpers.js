import * as tagsPages from '../components';
import * as filesPages from '../../files/components';
import * as filesHelpers from '../../files/helpers/filesHelpers';

const modelNameToTag = {
  Parts: 'parts',
  Files: 'files',
  'Purchase Orders': 'purchaseOrders',
  Checklists: 'checklists',
};

export const visitTagsModule = () => {
  tagsPages.section.tagsModule.shouldBeVisible();
  tagsPages.section.tagsModule.click();
  cy.url().should('include', 'sections/tags');
};

// Models = {Files, Parts, Purchase Orders, Checklists}
export const createTag = (tagName, models) => {
  tagsPages.section.addTagButton.shouldBeVisible();
  tagsPages.section.addTagButton.click();
  tagsPages.section.tagNameInput.click().type(tagName);
  tagsPages.section.modelsDropdown.click();
  if (Array.isArray(models)) {
    models.forEach((model) => {
      tagsPages.section.modelsOption(model).click();
    });
  } else {
    tagsPages.section.modelsOption(models).click();
  }
  tagsPages.section.portalBackdrop.click({ force: true });
  tagsPages.section.submitTagButton.click();
  tagsPages.section.tagNameInList(tagName).shouldExist();
};

export const selectFileTag = (tagName) => {
  filesPages.addEdit.tagsDropdown.click({
    force: true,
    position: 'bottomRight',
  });
  filesPages.addEdit.tagsListSearch.click();
  filesPages.addEdit.menuSearchInput.shouldBeVisible();
  filesPages.addEdit.menuSearchInput.type(tagName);
  filesPages.addEdit.tagItem(tagName).shouldBeVisible();
  filesPages.addEdit.tagItem(tagName).click();
};

export const selectAnotherFileTag = (tagName) => {
  filesPages.addEdit.menuSearchInput.clear();
  filesPages.addEdit.menuSearchInput.type(tagName);
  filesPages.addEdit.tagItem(tagName).shouldBeVisible();
  filesPages.addEdit.tagItem(tagName).click();
};

export const verifyTagInList = (tagName) => {
  filesPages.list.tagInRow(tagName).shouldBeVisible();
};

export const verifyTagNotInList = (tagName) => {
  filesPages.list.tagInRow(tagName).shouldNotExist();
};

export const uploadFileWithTag = (file, tagName) => {
  filesPages.list.pageHeader.shouldBeVisible();
  filesPages.list.addFileButton.click();
  filesHelpers.dragAndDropFile(file);
  filesPages.addEdit.tagsDropdown.click();
  filesPages.addEdit.menuSearchInput.shouldBeVisible();
  filesPages.addEdit.menuSearchInput.type(tagName);
  filesPages.addEdit.tagItem(tagName).shouldBeVisible();
  filesPages.addEdit.tagItem(tagName).click();
  tagsPages.section.portalBackdrop.click({ force: true });
};

export const replaceFileTag = (tagName1, tagName2, file) => {
  filesPages.list.filesRowEdit(file).shouldBeVisible();
  filesPages.list.filesRowEdit(file).click();
  filesPages.list.editFileButton.shouldBeVisible();
  filesPages.list.editFileButton.click();
  filesPages.addEdit.tagItemClose(tagName1).click();
  filesPages.addEdit.tagItem(tagName1).shouldBeVisible();
  filesPages.addEdit.tagsDropdown.click();
  filesPages.addEdit.tagsListSearch.click().type(tagName2);
  filesPages.addEdit.tagItem(tagName2).shouldBeVisible();
  filesPages.addEdit.tagItem(tagName2).click();
  tagsPages.section.portalBackdrop.click({ force: true });
  filesPages.addEdit.saveFileButton.click();
  cy.contains('File Updated').should('be.visible');
};

export const deleteTag = (tagName) => {
  tagsPages.section.optionsForTagInRow(tagName).shouldBeVisible();
  tagsPages.section.optionsForTagInRow(tagName).click();
  tagsPages.section.deleteButton.shouldBeVisible();
  tagsPages.section.deleteButton.click();
  tagsPages.section.confirmDeleteButton.shouldBeVisible();
  tagsPages.section.confirmDeleteButton.click();
  tagsPages.section.tagNameInList(tagName).shouldNotExist();
};

export const verifyUserCannotCreateTag = () => {
  tagsPages.section.addTagButton.shouldNotExist();
};

const selectTagEdit = (tagName) => {
  tagsPages.section.optionsForTagInRow(tagName).shouldBeVisible();
  tagsPages.section.optionsForTagInRow(tagName).click();
  tagsPages.section.editButton.shouldBeVisible();
  tagsPages.section.editButton.click();
};

export const editTagName = (tagName, update, updatedTagName) => {
  selectTagEdit(tagName);
  tagsPages.section.tagNameInput.click().type(update);
  tagsPages.section.submitTagButton.click();
  tagsPages.section.tagNameInList(updatedTagName).shouldExist();
};

export const removeModel = (
  tagName,
  modelRemoved,
  modelNotRemoved = undefined,
) => {
  selectTagEdit(tagName);
  tagsPages.section.modelDeleteButton(modelRemoved).click();
  tagsPages.section.modelDeleteButton(modelRemoved).shouldNotExist();
  tagsPages.section.submitTagButton.click();
  tagsPages.section
    .modelForTagInRow(tagName, modelNameToTag[modelRemoved])
    .shouldNotExist();
  if (modelNotRemoved) {
    tagsPages.section
      .modelForTagInRow(tagName, modelNameToTag[modelNotRemoved])
      .shouldExist();
  }
};

export const addModel = (tagName, modelName) => {
  selectTagEdit(tagName);
  tagsPages.section.modelsDropdown.click();
  tagsPages.section.modelsOption(modelName).click();
  tagsPages.section.portalBackdrop.click({ force: true });
  tagsPages.section.submitTagButton.click();
  tagsPages.section
    .modelForTagInRow(tagName, modelNameToTag[modelName])
    .shouldExist();
};
