import filterTests from '../../support/filterTests';
import { filesTagsTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const now = Date.now();
  const testId = 'tags-files';
  const teamName = 'super team bros';
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
  };

  describe('Tags - Files', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName, emails);
    });

    filesTagsTests.canAssignTagToFile();
    filesTagsTests.canAddSecondTagToFile();
    filesTagsTests.canReplaceTagOfFile();
    filesTagsTests.canDeleteTagAssignedToFile();
  });
});