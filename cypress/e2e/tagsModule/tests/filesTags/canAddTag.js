import { upkeepPages } from '../../../../support/constants';
import * as tagHelpers from '../../helpers/tagsHelpers';

const canAddTag = () => {
  it('Admin can add a tag', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const tagName = `Tag ${now}`;
    upkeepPages.SETTINGS.go();

    tagHelpers.visitTagsModule();
    tagHelpers.createTag(tagName, 'Files');
    cy.wait(1500);
    tagHelpers.createTag(`${tagName}1`, 'Parts');
    cy.wait(1500);
    tagHelpers.createTag(`${tagName}2`, 'Purchase Orders');
  });
};

export default canAddTag;
