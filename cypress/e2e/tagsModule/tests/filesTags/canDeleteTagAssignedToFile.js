import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as tagsHelpers from '../../helpers/tagsHelpers';
import * as filesHelpers from '../../../files/helpers/filesHelpers';

const canDeleteTagAssignedToFile = () => {
  it(
    'Admin can delete tags assigned to files',
    { testCaseId: 'QA-T6614' },
    () => {
      const now = Date.now();
      const tag = `Delete Tag ${now}`;
      const file = 'laptop.png';

      // Create a tag
      upkeepPages.SETTINGS.go();
      tagsHelpers.visitTagsModule();
      tagsHelpers.createTag(tag, 'Files');
      h.closeSettings();

      // Assign tag to a file
      upkeepPages.FILES.go();
      tagsHelpers.uploadFileWithTag(file, tag);
      filesHelpers.beginUploadFile();
      tagsHelpers.verifyTagInList(tag);

      // Delete tag
      upkeepPages.SETTINGS.go();
      tagsHelpers.visitTagsModule();
      tagsHelpers.deleteTag(tag);
      h.closeSettings();

      // File should be updated
      upkeepPages.FILES.go();
      cy.reload();
      cy.contains('h2', 'Files').should('be.visible');
      tagsHelpers.verifyTagNotInList(tag);
    },
  );
};

export default canDeleteTagAssignedToFile;
