import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as tagsHelpers from '../../helpers/tagsHelpers';
import * as filesHelpers from '../../../files/helpers/filesHelpers';

const canAssignTagToFile = () => {
  it(
    '<PERSON><PERSON> can create a tag and add it to a file',
    { testCaseId: 'QA-T6508' },
    () => {
      const now = Date.now();
      const tagName = `Files Tag ${now}`;
      const file = 'cat.jpeg';

      // Create a tag
      upkeepPages.SETTINGS.go();
      tagsHelpers.visitTagsModule();
      tagsHelpers.createTag(tagName, 'Files');
      h.closeSettings();

      // Upload file without tag
      upkeepPages.FILES.go();
      filesHelpers.addFile(file);
      filesHelpers.beginUploadFile();

      // Edit file and add tag
      cy.wait(600);
      filesHelpers.editFile(file);
      tagsHelpers.selectFileTag(tagName);
      filesHelpers.saveChanges();
      tagsHelpers.verifyTagInList(tagName);
    },
  );
};

export default canAssignTagToFile;
