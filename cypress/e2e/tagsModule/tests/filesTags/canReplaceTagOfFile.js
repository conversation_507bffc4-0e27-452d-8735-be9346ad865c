import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as tagsHelpers from '../../helpers/tagsHelpers';
import * as filesHelpers from '../../../files/helpers/filesHelpers';

const canReplaceTagOfFile = () => {
  it(
    'Admin can replace the first tag of a file with a second tag',
    { testCaseId: 'QA-T6510' },
    () => {
      const now = Date.now();
      const tagName1 = `Replaced Tag ${now}`;
      const tagName2 = `Replacement Tag ${now}`;
      const file = `sample.pdf`;

      // Create two tags
      upkeepPages.SETTINGS.go();
      tagsHelpers.visitTagsModule();
      tagsHelpers.createTag(tagName1, 'Files');
      tagsHelpers.createTag(tagName2, 'Files');
      h.closeSettings();

      // Upload a file with a tag
      upkeepPages.FILES.go();
      tagsHelpers.uploadFileWithTag(file, tagName1);
      filesHelpers.beginUploadFile();
      tagsHelpers.verifyTagInList(tagName1);

      // Edit file - replace first tag with second tag
      filesHelpers.editFile(file);
      tagsHelpers.selectFileTag(tagName1);
      tagsHelpers.selectAnotherFileTag(tagName2);
      filesHelpers.saveChanges();
      tagsHelpers.verifyTagInList(tagName2);
      tagsHelpers.verifyTagNotInList(tagName1);
    },
  );
};

export default canReplaceTagOfFile;
