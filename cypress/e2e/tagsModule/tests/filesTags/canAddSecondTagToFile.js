import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as tagsHelpers from '../../helpers/tagsHelpers';
import * as filesHelpers from '../../../files/helpers/filesHelpers';

const canAddSecondTagToFile = () => {
  it(
    '<PERSON><PERSON> can add a second tag to a file with an existing tag',
    { testCaseId: 'QA-T6509' },
    () => {
      const now = Date.now();
      const tagName1 = `Tag1 ${now}`;
      const tagName2 = `Tag2 ${now}`;
      const file = 'laptop.png';

      // Create two tags
      upkeepPages.SETTINGS.go();
      tagsHelpers.visitTagsModule();
      tagsHelpers.createTag(tagName1, 'Files');
      tagsHelpers.createTag(tagName2, 'Files');
      h.closeSettings();

      // Upload a file with a tag
      upkeepPages.FILES.go();
      tagsHelpers.uploadFileWithTag(file, tagName1);
      filesHelpers.beginUploadFile();
      tagsHelpers.verifyTagInList(tagName1);

      // Edit file and add second tag
      filesHelpers.editFile(file);
      tagsHelpers.selectFileTag(tagName2);
      filesHelpers.saveChanges();
      tagsHelpers.verifyTagInList(tagName2);
    },
  );
};

export default canAddSecondTagToFile;
