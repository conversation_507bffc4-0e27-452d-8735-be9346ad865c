import * as checklistPages from '../../../Checklists/components';

const canAssignTagToChecklist = (tagName, now) => {
  const checklistName = `Checklist with Tag ${now}`;

  // Create basic checklist and assign a tag
  checklistPages.list.addChecklistButton.click();
  checklistPages.addEdit.nameInput.type(checklistName, {
    scrollTo: false,
  });
  checklistPages.addEdit.addTaskButton.click();
  checklistPages.addEdit.checklistTaskNameInput.type('Task 1', {
    position: 'last',
  });
  checklistPages.addEdit.tagListDropdown.click();
  checklistPages.addEdit.tagListOption(tagName).click();
  checklistPages.addEdit.createChecklistButton.click({ force: true });

  checklistPages.list.searchInput.type(checklistName);
  checklistPages.list.checklistRow(tagName).shouldExist();
  checklistPages.list.checklistRow(checklistName).click();
  checklistPages.addEdit.tagPill(tagName).shouldExist();
};

export default canAssignTagToChecklist;
