import * as tagHelpers from '../../helpers/tagsHelpers';

const canUpdateTagName = (tagName) => {
  const update = ' - Updated';
  const updatedTagName = `${tagName}${update}`;
  tagHelpers.editTagName(tagName, update, updatedTagName);
};

const canEditChecklistTags = (tagName, multiModelTagName) => {
  canUpdateTagName(tagName);
  // Remove 'Checklists' model from multi model tag
  tagHelpers.removeModel(multiModelTagName, 'Checklists', 'Files');
  // Add back the removed model
  tagHelpers.addModel(multiModelTagName, 'Checklists');
  // Remove 'Files' model from multi model tag
  tagHelpers.removeModel(multiModelTagName, 'Files', 'Checklists');
};

export default canEditChecklistTags;
