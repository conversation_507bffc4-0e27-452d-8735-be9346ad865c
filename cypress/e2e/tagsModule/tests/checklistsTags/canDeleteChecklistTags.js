import * as tagHelpers from '../../helpers/tagsHelpers';

const canDeleteChecklistTags = (tagName, now) => {
  // Delete existing tag
  tagHelpers.deleteTag(tagName);
  // Create and delete multi model tag with 'Checklists' model
  const multiModelTagToDelete = `Multi Model Tag To Delete ${now}`;
  tagHelpers.createTag(multiModelTagToDelete, ['Checklists', 'Files']);
  tagHelpers.deleteTag(multiModelTagToDelete);
};

export default canDeleteChecklistTags;
