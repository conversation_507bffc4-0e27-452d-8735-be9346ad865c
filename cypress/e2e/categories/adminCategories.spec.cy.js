import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'ui', 'smoke'], () => {
  Cypress.on('uncaught:exception', () => false);
  const testId = 'categories';

  describe('Admin Categories', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS');
    });

    adminTests.canCreateEditTimerCategory();
    adminTests.canRenamePoCategory();
    adminTests.canDeletePoCategory();
    adminTests.canRenameAssetStatusCategory();
    adminTests.canDeleteAssetStatusCategory();
    adminTests.canDeleteWoCategory();
  });
});
