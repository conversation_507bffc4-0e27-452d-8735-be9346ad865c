import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canDeleteWoCategory = () => {
  it(
    'Admin user is able to delete a Work Order category',
    { testCaseId: 'QA-T5780' },
    () => {
      const now = Date.now();
      const categoryName = `WorkOrder ${now} Category`;

      h.createWoCategory({ category: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitWorkOrderCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.deleteCategory(categoryName);
      categoriesHelpers.confirmCategoryDeleted(categoryName);
    },
  );
};

export default canDeleteWoCategory;
