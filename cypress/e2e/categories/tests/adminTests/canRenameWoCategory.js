import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canRenameWoCategory = () => {
  it(
    'Admin user is able to rename Work Order category',
    { testCaseId: 'QA-T5787' },
    () => {
      const now = Date.now();
      const categoryName = `WorkOrder ${now} Category`;
      const newCategoryName = `Edited WorkOrder Category ${now}`;

      h.createWoCategory({ category: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitWorkOrderCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.editCategory(categoryName, newCategoryName);
      categoriesHelpers.confirmCategory(newCategoryName);
    },
  );
};

export default canRenameWoCategory;
