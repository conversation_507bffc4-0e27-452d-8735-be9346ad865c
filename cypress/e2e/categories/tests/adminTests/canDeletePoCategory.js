import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canDeletePoCategory = () => {
  it(
    'Admin user is able to delete a Purchase Order category',
    { testCaseId: 'QA-T5777' },
    () => {
      const now = Date.now();
      const categoryName = `PO ${now} Category`;

      h.createPoCategory({ name: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitPoCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.deleteCategory(categoryName);
      categoriesHelpers.confirmCategoryDeleted(categoryName);
    },
  );
};

export default canDeletePoCategory;
