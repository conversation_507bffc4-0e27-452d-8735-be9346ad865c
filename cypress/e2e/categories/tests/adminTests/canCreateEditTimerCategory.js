import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';

const canCreateEditTimerCategory = () => {
  it(
    'Admin user can create and edit timer category',
    { testCaseId: 'QA-T54' },
    () => {
      const now = Date.now();
      const timerCategory = `Timer ${now} Category`;
      const newTimerCategory = `New Timer ${now} Category`;

      upkeepPages.SETTINGS.go();

      // Verify admin can add a timer category
      categoriesHelpers.visitTimerCategories();
      categoriesHelpers.create(timerCategory);
      categoriesHelpers.confirmCategory(timerCategory);

      // Verify admin can edit a timer category
      cy.reload();
      categoriesHelpers.visitTimerCategories();
      categoriesHelpers.editCategory(timerCategory, newTimerCategory);
      categoriesHelpers.confirmCategory(newTimerCategory);
    },
  );
};

export default canCreateEditTimerCategory;
