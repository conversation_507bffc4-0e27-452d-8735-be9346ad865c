import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canDeleteAssetStatusCategory = () => {
  it(
    'Admin user is able to delete an Asset Status category',
    { testCaseId: 'QA-5779' },
    () => {
      const now = Date.now();
      const categoryName = `AssetStatus ${now} Category`;

      h.createAssetCategory({ name: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitAssetStatusCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.deleteCategory(categoryName);
      categoriesHelpers.confirmCategoryDeleted(categoryName);
    },
  );
};

export default canDeleteAssetStatusCategory;
