import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canRenameAssetStatusCategory = () => {
  it(
    'Admin user is able to rename an Asset Status category',
    { testCaseId: 'QA-T5778' },
    () => {
      const now = Date.now();
      const categoryName = `AssetStatus ${now} Category`;
      const newCategoryName = `Edited AssetStatus Category ${now}`;

      h.createAssetCategory({ name: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitAssetStatusCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.editCategory(categoryName, newCategoryName);
      categoriesHelpers.confirmCategory(newCategoryName);
    },
  );
};

export default canRenameAssetStatusCategory;
