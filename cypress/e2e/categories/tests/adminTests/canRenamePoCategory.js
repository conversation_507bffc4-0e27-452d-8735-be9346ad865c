import { upkeepPages } from '../../../../support/constants';
import * as categoriesHelpers from '../../helpers/helpers';
import * as h from '../../../../helpers';

const canRenamePoCategory = () => {
  it(
    'Admin user is able to rename a Purchase Order category',
    { testCaseId: 'QA-T5776' },
    () => {
      const now = Date.now();
      const categoryName = `PO ${now} Category`;
      const newCategoryName = `Edited PO Category ${now}`;

      h.createPoCategory({ name: categoryName });

      upkeepPages.SETTINGS.go();

      categoriesHelpers.visitPoCategories();
      categoriesHelpers.confirmCategory(categoryName);
      categoriesHelpers.editCategory(categoryName, newCategoryName);
      categoriesHelpers.confirmCategory(newCategoryName);
    },
  );
};

export default canRenamePoCategory;
