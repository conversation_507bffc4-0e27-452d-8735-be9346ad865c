import { leftNavigation } from '../../../../support/constants';

const requesterCannotGoToCategories = (emails) => {
  it(
    'Requester is unable to navigate to the Categories page',
    { testCaseId: 'QA-T5744' },
    () => {
      // Login as Requester
      cy.logout();
      cy.login(emails.REQUESTER, Cypress.env('PASSWORD'));

      cy.get(leftNavigation.REQUESTS.navSelector).should('be.visible');
      cy.get(leftNavigation.CATEGORIES.navSelector).should('not.exist');
    },
  );
};

export default requesterCannotGoToCategories;
