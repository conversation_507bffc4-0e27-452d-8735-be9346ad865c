import { leftNavigation } from '../../../../support/constants';
import * as categoriesPages from '../../components';

const canNotCreateCategories = (testId) => {
  it(
    'limited tech can navigate to categories and not create categories',
    { testCaseId: 'QA-T5753' },
    () => {
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });

      // limited technician can navigate to categories
      cy.get(leftNavigation.CATEGORIES.navSelector).click();
      cy.contains('h2', 'Categories').should('be.visible');

      categoriesPages.list.workOrdersTab.click();
      categoriesPages.list.addCategoryButton.shouldNotExist();

      categoriesPages.list.assetStatusTab.click();
      cy.contains(
        "Looks like you don't have any Asset Status Categories",
      ).should('be.visible');
      categoriesPages.list.addCategoryButton.shouldNotExist();

      categoriesPages.list.purchaseOrdersTab.click();
      cy.contains(
        "Looks like you don't have any purchase order categories yet",
      ).should('be.visible');
      categoriesPages.list.addCategoryButton.shouldNotExist();

      categoriesPages.list.meterTab.click();
      categoriesPages.list.addCategoryButton.shouldNotExist();

      categoriesPages.list.timerTab.click();
      categoriesPages.list.addCategoryButton.shouldNotExist();
    },
  );
};

export default canNotCreateCategories;
