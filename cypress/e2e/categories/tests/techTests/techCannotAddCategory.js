import { leftNavigation } from '../../../../support/constants';
import * as categoriesPages from '../../components';

const techCannotAddCategory = (testId) => {
  it('tech cannot create categories', { testCaseId: 'QA-T5768' }, () => {
    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'TECH',
        {
          firstName: 'Cool',
          lastName: 'Tech',
        },
        sessionToken,
      );
    });

    // can navigate to categories
    cy.get(leftNavigation.CATEGORIES.navSelector).click();
    cy.contains('h2', 'Categories').should('be.visible');

    categoriesPages.list.workOrdersTab.click();
    categoriesPages.list.addCategoryButton.shouldNotExist();
    categoriesPages.list.dropdownButton.shouldNotExist();

    categoriesPages.list.assetStatusTab.click();
    cy.contains("Looks like you don't have any Asset Status Categories").should(
      'be.visible',
    );
    categoriesPages.list.addCategoryButton.shouldNotExist();
    categoriesPages.list.dropdownButton.shouldNotExist();

    categoriesPages.list.purchaseOrdersTab.click();
    cy.contains(
      "Looks like you don't have any purchase order categories yet",
    ).should('be.visible');
    categoriesPages.list.addCategoryButton.shouldNotExist();
    categoriesPages.list.dropdownButton.shouldNotExist();

    categoriesPages.list.meterTab.click();
    categoriesPages.list.addCategoryButton.shouldNotExist();
    categoriesPages.list.dropdownButton.shouldNotExist();

    categoriesPages.list.timerTab.click();
    categoriesPages.list.addCategoryButton.shouldNotExist();
    categoriesPages.list.dropdownButton.shouldNotExist();
  });
};

export default techCannotAddCategory;
