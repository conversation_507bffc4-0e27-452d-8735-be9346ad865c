import * as categoriesPages from '../components';

export const create = (name) => {
  categoriesPages.list.addCategoryButton.click();
  categoriesPages.list.categoryNameInput.type(name);
  categoriesPages.list.confirmAddButton.click();
};

const createCategoriesEndpoint = {
  workOrders: 'work-order-categories/',
  assets: 'asset-categories/',
  purchaseOrders: 'purchase-order-categories/',
  meters: 'meter-categories/',
};

export const interceptCategoryCreate = (endpoint) => {
  const categoryEndpoint = `${Cypress.env(
    'CYPRESS_API_URL',
  )}/api/v1/${endpoint}`;

  cy.intercept({
    method: 'POST',
    url: categoryEndpoint,
  }).as('interceptCategoryCreate');
};

export const workOrderCategoryIntercept = () => {
  interceptCategoryCreate(createCategoriesEndpoint.workOrders);
};

export const assetsCategoryIntercept = () => {
  interceptCategoryCreate(createCategoriesEndpoint.assets);
};

export const purchaseOrderCategoryIntercept = () => {
  interceptCategoryCreate(createCategoriesEndpoint.purchaseOrders);
};

export const meterCategoryIntercept = () => {
  interceptCategoryCreate(createCategoriesEndpoint.meters);
};

export const visitPoCategories = () => {
  categoriesPages.modules.purchaseOrdersModule.shouldBeVisible();
  categoriesPages.modules.purchaseOrdersModule.click();
  categoriesPages.modules.categoriesTab.shouldBeVisible();
  categoriesPages.modules.categoriesTab.click();
};

export const visitAssetStatusCategories = () => {
  categoriesPages.modules.assetsModule.shouldBeVisible();
  categoriesPages.modules.assetsModule.click();
  categoriesPages.modules.assetStatusTab.shouldBeVisible();
  categoriesPages.modules.assetStatusTab.click();
};

export const visitWorkOrderCategories = () => {
  categoriesPages.modules.workOrdersModule.shouldBeVisible();
  categoriesPages.modules.workOrdersModule.click();
  categoriesPages.modules.categoriesTab.shouldBeVisible();
  categoriesPages.modules.categoriesTab.click();
};

export const visitTimerCategories = () => {
  categoriesPages.modules.workOrdersModule.shouldBeVisible();
  categoriesPages.modules.workOrdersModule.click();
  categoriesPages.modules.timersTab.shouldBeVisible();
  categoriesPages.modules.timersTab.click();
};

export const editCategory = (categoryName, newCategoryName) => {
  categoriesPages.list.categoryOptionsButton(categoryName).shouldBeVisible();
  categoriesPages.list.categoryOptionsButton(categoryName).click();
  categoriesPages.list.editOption.click();
  categoriesPages.addEdit.editCategoryInput.type(newCategoryName);
  categoriesPages.addEdit.confirmEditCategoryButton.click();
  categoriesPages.list.categoryInList(categoryName).shouldNotExist();
};

export const deleteCategory = (categoryName) => {
  categoriesPages.list.categoryOptionsButton(categoryName).shouldBeVisible();
  categoriesPages.list.categoryOptionsButton(categoryName).click();
  categoriesPages.list.deleteOption.click();
  categoriesPages.list.confirmDeleteButton.click();
};

export const confirmCategory = (categoryName) => {
  categoriesPages.list.categoryInList(categoryName).shouldBeVisible();
};

export const confirmCategoryDeleted = (categoryName) => {
  categoriesPages.list.categoryInList(categoryName).shouldNotExist();
};
