import * as categoriesPages from '../components';

export const visitTimerTab = () => {
  categoriesPages.list.timerTab.shouldBeVisible();
  categoriesPages.list.timerTab.click();
  cy.url('include', 'timer-categories');
};

export const interceptAddTimerCategory = () => {
  cy.intercept({
    method: 'POST',
    url: `${Cypress.env(
      'CYPRESS_API_URL',
    )}/api/v1/work-order-timer-categories/`,
  }).as('interceptAddTimerCategory');
};

export const createsTimerCategory = (categoryName) => {
  categoriesPages.list.addTimerCategoryButton.shouldBeVisible();
  categoriesPages.list.addTimerCategoryButton.click();

  interceptAddTimerCategory();
  categoriesPages.addEdit.addCategoryModal.shouldBeVisible();
  categoriesPages.addEdit.categoryNameInput.click().type(categoryName);
  categoriesPages.addEdit.saveButton.click();
};

export const verifyTimerCategoryCreated = () => {
  cy.wait('@interceptAddTimerCategory').then((res) => {
    expect(res.response.statusCode).to.eq(200);
  });
};

export const renameTimerCategory = (categoryName) => {
  categoriesPages.list.toggleButton.click();
  categoriesPages.list.renameInDropdown.click();

  categoriesPages.list.categoryEditInput.clear().type(categoryName);
  categoriesPages.list.saveUpdateButton.click();
  cy.contains('Category saved').should('be.visible');
};

export const deleteTimerCategory = () => {
  categoriesPages.list.toggleButton.click();
  categoriesPages.list.deleteInDropdown.click();
  categoriesPages.addEdit.confirmDeleteButton.click();
  cy.contains('Category deleted').should('be.visible');
};
