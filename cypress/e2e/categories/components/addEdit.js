import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  addCategoryModal: '[class="modal-content"]',
  categoryNameInput: 'input[name="category_name"]',
  cancelButton: 'button[ng-click="cancelAdd()"]',
  saveButton: 'button[ng-click="saveTc()"]',
  cancelDeleteButton: '[ng-click="TimerCategoriesCtrl.cancel()"]',
  confirmDeleteButton: '[ng-click="TimerCategoriesCtrl.deleteTc(category)"]',
  editCategoryInput: '[class*="card-inner-content"] input',
  confirmEditCategoryButton: '[data-cy="Confirm"]',
});

export default addEdit;
