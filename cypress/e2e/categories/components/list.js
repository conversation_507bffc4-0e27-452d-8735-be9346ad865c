import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  addCategoryButton: 'button:contains("Add")',
  workOrdersTab: '[ui-sref="app.account.maintenanceCategories"]',
  assetStatusTab: '[ui-sref="app.account.assetStatusCategories"]',
  purchaseOrdersTab: '[ui-sref="app.account.purchaseOrderCategories"]',
  meterTab: '[ui-sref="app.account.meterCategories"]',
  timerTab: '[ui-sref="app.account.timerCategories"]',
  addTimerCategoryButton: '[ng-click="TimerCategoriesCtrl.openAddTcModal()"]',
  toggleButton: '[on-toggle="toggled(open)"]:last',
  renameInDropdown: 'div[class*="dropdown-menu"]>a:contains("Rename"):visible',
  deleteInDropdown: 'div[class*="dropdown-menu"]>a:contains("Delete"):visible',
  saveUpdateButton: '[ng-click="updateCategory($index)"]',
  categoryEditInput: 'div[class*="list-title"]>input:not([disabled])',
  categoryNameInput: 'div[class*="input-wrapper"] input',
  confirmAddButton: '[data-cy="Confirm"]',
  categoryListItem: (name) => `.list-item input:contains("${name}")`,
  dropdownButton: '[id="simple-dropdown"]',
  categoryInList: (name) => `td [data-cy="${name}"]`,
  categoryOptionsButton: (name) =>
    `tr:contains("${name}") [data-cy="icon-button"]`,
  editOption: 'button:contains("Edit")',
  deleteOption: 'button:contains("Delete")',
  confirmDeleteButton: '[data-cy="Delete"]',
});

export default list;
