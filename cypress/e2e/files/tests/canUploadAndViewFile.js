import { upkeepPages } from '../../../support/constants';
import * as filesHelpers from '../helpers/filesHelpers';

const canUploadAndViewFile = () => {
  it(
    'should upload a pdf file',
    { testCaseId: 'QA-T6346', featureFlags: { ampfAccountFiles: true } },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const pdf = 'sample.pdf';

      // go to Files page
      upkeepPages.FILES.go();

      // upload pdf file and verify it can be opened
      filesHelpers.addFile(pdf);
      filesHelpers.beginUploadFile();
      filesHelpers.clickViewFileAndVerifyOpen(pdf);
    },
  );

  it(
    'should upload an image file',
    { testCaseId: 'QA-T6347', featureFlags: { ampfAccountFiles: true } },
    () => {
      const image = 'cat.jpeg';

      // go to Files page
      upkeepPages.FILES.go();

      // upload jpeg file and verify it can be opened
      filesHelpers.addFile(image);
      filesHelpers.beginUploadFile();
      filesHelpers.clickViewFileAndVerifyOpen(image);
    },
  );

  it(
    'should upload a png file',
    { testCaseId: 'QA-T6348', featureFlags: { ampfAccountFiles: true } },
    () => {
      const image = 'laptop.png';

      // go to Files page
      upkeepPages.FILES.go();

      // upload png file and verify it can be opened
      filesHelpers.addFile(image);
      filesHelpers.beginUploadFile();

      filesHelpers.clickViewFileAndVerifyOpen(image);
    },
  );

  it(
    'should upload an mp4 file',
    { testCaseId: 'QA-T6349', featureFlags: { ampfAccountFiles: true } },
    () => {
      const video = 'cityroad.mp4';

      // go to Files page
      upkeepPages.FILES.go();

      // upload mp4 file and verify it can be opened
      filesHelpers.addFile(video);
      filesHelpers.beginUploadFile();
      // video opens in a new tab, no visible to cypres
    },
  );
};

export default canUploadAndViewFile;
