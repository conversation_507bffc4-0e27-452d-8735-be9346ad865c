import { upkeepPages } from '../../../support/constants';
import * as filesPages from '../components';
import * as filesHelpers from '../helpers/filesHelpers';

const canDeleteFile = () => {
  it(
    'should delete a file',
    { testCaseId: 'QA-T6351', featureFlags: { ampfAccountFiles: true } },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const pdf = 'delete_sample.pdf';

      // go to Files page
      upkeepPages.FILES.go();
      filesPages.list.pageHeader.shouldBeVisible();
      filesPages.list.addFileButton.click();
      filesHelpers.dragAndDropFile(pdf);
      filesHelpers.verifyFileUpload();
      filesPages.addEdit.startUploadButton.click();

      // delete uploaded file
      filesPages.list.filesRowCheckbox(pdf).click({ multiple: true });
      filesPages.list.deleteFileButton.click({
        force: true,
      });
      filesPages.list.confirmDeleteButton.click();
      filesPages.list.filesRowCheckbox(pdf).shouldNotExist();
    },
  );
};

export default canDeleteFile;
