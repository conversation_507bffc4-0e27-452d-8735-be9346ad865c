import { upkeepPages } from '../../../support/constants';

import * as filesPages from '../components';

const canEditFile = () => {
  it(
    'should edit/rename a file',
    { testCaseId: 'QA-T6350', featureFlags: { ampfAccountFiles: true } },
    () => {
      const image = 'cat.jpeg';
      const newFileName = 'kitten.jpeg';

      // go to Files page
      upkeepPages.FILES.go();

      // edit file name with new name
      filesPages.list.filesRow(image).shouldBeVisible();
      filesPages.list.specificRenameButton(image).click();
      cy.contains('Edit File').click();

      filesPages.addEdit.fileNameInput.type(newFileName);
      filesPages.list.confirmRenameButton.click();
      filesPages.list.fileCardModal.shouldNotExist();
      cy.reload();
      filesPages.list.filesRow(newFileName).shouldBeVisible();
    },
  );
};

export default canEditFile;
