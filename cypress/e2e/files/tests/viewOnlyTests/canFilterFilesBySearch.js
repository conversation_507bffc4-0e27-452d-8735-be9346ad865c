import { upkeepPages } from '../../../../support/constants';
import * as filesHelpers from '../../helpers/filesHelpers';

const canFilterFilesBySearch = () => {
  it(
    'View Only user can filter for a file using the search bar',
    { testCaseId: 'QA-T717' },
    () => {
      const pdf = 'sample.pdf';
      const image = 'cat.jpeg';

      // Go to Files page
      upkeepPages.FILES.go();
      cy.contains('Files').should('be.visible');

      // Upload files
      filesHelpers.uploadFile(pdf);
      filesHelpers.uploadFile(image);

      // Search for file
      filesHelpers.searchForFile(image);

      // Verify list is filtered
      filesHelpers.verifyFilesListFiltered(image, pdf);
    },
  );
};

export default canFilterFilesBySearch;
