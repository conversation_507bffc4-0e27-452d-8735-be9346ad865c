import { upkeepPages } from '../../../../support/constants';
import * as filesHelpers from '../../helpers/filesHelpers';

const canViewFile = () => {
  it(
    'View Only user can navigate to Files and view a file',
    { testCaseId: 'QA-T714' },
    () => {
      const file = 'laptop.png';

      // Go to Files page
      upkeepPages.FILES.go();
      cy.contains('Files').should('be.visible');

      // Upload file
      filesHelpers.uploadFile(file);

      // Click and view file
      filesHelpers.clickViewFileAndVerifyOpen(file);
    },
  );
};

export default canViewFile;
