import filterTests from '../../support/filterTests';
import * as filesTests from './tests';

filterTests(['all', 'smoke', 'ui', 'tier2'], () => {
  const testId = 'filestest';

  describe('Files', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'My Cool Team');
      cy.contains('Work Orders').should('be.visible');
    });

    filesTests.canDeleteFile();
    filesTests.canUploadAndViewFile();
    filesTests.canEditFile();
    filesTests.canFilterFilesBySearch();
  });
});
