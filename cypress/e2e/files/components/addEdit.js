import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  fileUploadBox: '.dropzone',
  startUploadButton: '[data-cy="pages.files.add.actionText"]',
  closeButton: '.icon-close',
  fileNameInput: '.modal input',
  tagsDropdown: '[data-cy="TagsListButton"]',
  tagsListSearch: '[class*="card-inner-content"] [id="menu-list-search"]',
  tagItem: (tag) => `.item-container:contains("${tag}")`,
  tagItemClose: (tag) =>
    `#TagsListButton:contains("${tag}") div[class*="icon-close"]`,
  saveFileButton: '[data-cy="pages.files.edit.actionText"]',
  menuSearchInput: '[id="menu-list-search"]',
});

export default addEdit;
