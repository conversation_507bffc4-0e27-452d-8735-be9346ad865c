import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  pageHeader: 'h2:contains("Files")',
  addFileButton: 'button:contains("Add Files")',
  filesRow: (name) => `tr:contains("${name}")`,
  filesRowEdit: (name) =>
    `tr:contains("${name}") button[data-cy="icon-button"]`,
  filesRowCheckbox: (name) => `tr:contains("${name}") input[type="checkbox"]`,
  specificDeleteFileButton: (name) =>
    `tr:contains("${name}") a:contains("Delete")`,
  editFileButton: '[data-cy="pages.files.edit.editFile"]',
  deleteFileButton: '.toast-container .action',
  confirmDeleteButton: 'button:contains("Delete")',
  videoPreview: 'video',
  fileId: (name) => `tr:contains("${name}")>td:first-child`,
  specificRenameButton: (name) => `tr:contains("${name}") button`,
  confirmRenameButton: 'button:contains("Save File")',
  summariesTab: '[ui-sref="app.account.daily-summary"]',
  searchBar: '[id="search-bar"]',
  tagInRow: (tag) => `[class*="labels.tags"]:contains("${tag}")`,
  fileCardModal: 'div[class*="modal"]',
});

export default list;
