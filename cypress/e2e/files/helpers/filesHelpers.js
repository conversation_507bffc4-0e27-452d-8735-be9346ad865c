import * as filesPages from '../components';

export const dragAndDropFile = (file) => {
  filesPages.addEdit.fileUploadBox.shouldBeVisible();
  filesPages.addEdit.fileUploadBox.selectFile(`cypress/fixtures/${file}`, {
    action: 'drag-drop',
    force: true,
  });
  cy.wait(500);
};

export const verifyFileUpload = () => {
  cy.intercept({
    method: 'POST',
    url: `${Cypress.env('CYPRESS_API_URL')}/api/v1/files/upload`,
  }).as('verifyFileUpload');
};

export const addFile = (file) => {
  filesPages.list.pageHeader.shouldBeVisible();
  filesPages.list.addFileButton.click();
  cy.wait(800);
  dragAndDropFile(file);
  verifyFileUpload();
};

export const beginUploadFile = () => {
  filesPages.addEdit.startUploadButton.click();
};

export const clickViewFile = (file) => {
  filesPages.list.filesRow(file).click();
};

export const clickViewFileAndVerifyOpen = (file) => {
  cy.window().then((win) => {
    cy.stub(win, 'open').as('open');
  });
  clickViewFile(file);
  cy.get('@open').should('have.been.calledOnce');
};

export const verifyFileDelete = (id) => {
  cy.intercept({
    method: 'DELETE',
    url: `${Cypress.env('CYPRESS_API_URL')}/api/v1/files/${id}`,
  }).as('verifyFileDelete');
};

export const verifyRenameFile = (id) => {
  cy.intercept({
    method: 'PATCH',
    url: `${Cypress.env('CYPRESS_API_URL')}/api/v1/files/${id}`,
  }).as('verifyRenameFile');
};

export const getFileId = (file, action) => {
  filesPages.list
    .fileId(file)
    .get()
    .then(($res) => {
      if (action === 'delete') {
        verifyFileDelete($res.text());
      } else if (action === 'rename') {
        verifyRenameFile($res.text());
      }
    });
};

export const uploadFile = (file) => {
  filesPages.list.addFileButton.click();
  dragAndDropFile(file);
  verifyFileUpload();
  filesPages.addEdit.startUploadButton.click();
};

export const searchForFile = (name) => {
  filesPages.list.searchBar.type(name);
};

/**
 * @param {string} file1 file searched for which should be visible
 * @param {string} file2 file filtered out which should not be visible
 */
export const verifyFilesListFiltered = (file1, file2) => {
  filesPages.list.filesRow(file1).shouldBeVisible();
  filesPages.list.filesRow(file2).shouldNotExist();
};

export const editFile = (file) => {
  filesPages.list.filesRowEdit(file).shouldBeVisible();
  filesPages.list.filesRowEdit(file).click();
  filesPages.list.editFileButton.shouldBeVisible();
  filesPages.list.editFileButton.click();
  cy.contains('span', 'Edit File').should('be.visible');
};

export const saveChanges = () => {
  filesPages.addEdit.saveFileButton.click();
  // Verify that modal closes after saving
  filesPages.list.fileCardModal.shouldNotExist();
};
