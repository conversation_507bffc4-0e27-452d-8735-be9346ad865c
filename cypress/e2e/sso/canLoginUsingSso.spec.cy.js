import filterTests from '../../support/filterTests';
import { upkeepPages } from '../../support/constants';
import * as loginPages from './components';
import azure from './credentials';

filterTests(['all', 'smoke', 'ui', 'login'], () => {
  Cypress.on('uncaught:exception', () => false);
  describe.skip('SSO', () => {
    it(
      'can login using SSO - azure',
      { retries: { runMode: 0, openMode: 0 } },
      () => {
        const env = Cypress.env('CYPRESS_ENV');
        const runnableEnvs = ['staging', 'staging3', 'production'];

        if (!runnableEnvs.includes(env)) {
          cy.state('runnable').ctx.skip();
        }
        const sso = azure[env];

        upkeepPages.LOGIN.go();
        loginPages.login.continueWithSsoButton.click();
        loginPages.sso.companyIdentifier.type(sso.identifier);
        loginPages.sso.continueButton.click();

        cy.origin(
          azure.origin,
          {
            args: {
              username: sso.admin.email,
              password: Cypress.env('CYPRESS_SSO_AZURE_PASSWORD'),
            },
          },
          ({ username, password }) => {
            cy.get('input[type="email"]').type(username);
            cy.get('input[type="submit"]').click();
            cy.get('input[type="password"]').type(password, {
              log: false,
            });
            cy.get('input[type="submit"]').click();
            cy.get('#idBtn_Back').click();
          },
        );

        cy.get('[id="upkeep-logo"]').should('be.visible');
      },
    );
  });
});
