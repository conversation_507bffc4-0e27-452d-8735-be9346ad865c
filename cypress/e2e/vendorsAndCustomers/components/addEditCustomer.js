import { configureSelectorProxy } from '../../../helpers';

const addEditCustomer = configureSelectorProxy({
  customerNameInput: '[data-cy="customerName"]',
  addressInput: '[data-cy="customerAddress"]',
  phoneInput: '[data-cy="phoneNumber"]',
  websiteInput: '[data-cy="website"]',
  emailInput: '[data-cy="email"]',
  customerTypeInput: '[data-cy="customerType"]',
  descriptionInput: '[data-cy="customerDescription"]',
  hourlyRateInput: '[data-cy="hourlyRate"]',
  billingNameInput: '[data-cy="billingName"]',
  createCustomerButton: 'button:contains("Create Customer")',
  saveEditButton: 'button:contains("Save Changes")',
});

export default addEditCustomer;
