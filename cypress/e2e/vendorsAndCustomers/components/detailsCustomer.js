import { configureSelectorProxy } from '../../../helpers';

const detailsCustomer = configureSelectorProxy({
  customerName: '[data-cy="Name"]',
  address: '[data-cy="Address"]',
  phoneNumber: '[data-cy="Phone Number"]',
  website: '[data-cy="Website"]',
  companyName: '[data-cy="Company Name"]',
  email: '[data-cy="Email"]',
  type: '[data-cy="Type"]',
  description: '[data-cy="Description"]',
  hourlyRate: '[data-cy="Hourly Rate"]',
  editButton: 'button:contains("Edit")',
  threeDotsMenuButton: '[data-cy="icon-button"]',
  deleteButton: '[data-cy="menu-item-Delete"]',
});

export default detailsCustomer;
