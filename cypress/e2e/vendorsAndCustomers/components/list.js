import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  pageHeading: 'h2:contains("Vendors & Customers")',
  vendorsTab: '[data-cy="generic.upkeepEntity.vendors"]',
  customersTab: '[data-cy="generic.upkeepEntity.customers"]',
  customerRow: (name) => `tr:contains("${name}")`,
  vendorsInList: (name) => `td:contains("${name}")`,
  modalCustomerName: 'h2',
  addCustomerButton: 'button:contains("Create Customer")',
  addVendorButton: 'button:contains("Create Vendor")',
  searchBar: '[data-cy="search-bar"]',
  threeDotsMenuButton: '[data-cy="icon-button"]',
  exportFilteredViewButton: '[data-cy="menu-item-Export Filtered View"]',
  importExportButton: '[data-cy="menu-item-Import/Export"]',
  downloadTemplate: 'a:contains("Download Template")',
});

export default list;
