import { configureSelectorProxy } from '../../../helpers';

const detailsVendor = configureSelectorProxy({
  companyName: '[data-cy="Company Name"]',
  address: '[data-cy="Address"]',
  phoneNumber: '[data-cy="Phone Number"]',
  website: '[data-cy="Website"]',
  nameOfContact: '[data-cy="Name"]',
  email: '[data-cy="Email"]',
  name: '[data-cy="Name"]',
  type: '[data-cy="Type"]',
  description: '[data-cy="Description"]',
  hourlyRate: '[data-cy="Hourly Rate"]',
  editButton: 'button:contains("Edit")',
  backButton: '[data-cy="backButton"]',
  threeDotsMenuButton: '[class="MuiIconButton-label"]',
  deleteButton: '[data-cy="menu-item-Delete"]',
});

export default detailsVendor;
