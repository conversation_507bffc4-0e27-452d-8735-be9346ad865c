import { configureSelectorProxy } from '../../../helpers';

const addEditVendor = configureSelectorProxy({
  companyNameInput: '[data-cy="businessName"]',
  addressInput: '[data-cy="businessAddress"]',
  phoneInput: '[data-cy="phoneNumber"]',
  websiteInput: '[data-cy="website"]',
  nameOfContactInput: '[data-cy="mainPointOfContact"]',
  emailInput: '[data-cy="email"]',
  vendorTypeInput: '[data-cy="vendorType"]',
  descriptionInput: '[data-cy="businessDescription"]',
  hourlyRateInput: '[data-cy="hourlyRate"]',
  createVendorButton: 'button:contains("Create Vendor")',
  saveChangesButton: 'button:contains("Save Changes")',
});

export default addEditVendor;
