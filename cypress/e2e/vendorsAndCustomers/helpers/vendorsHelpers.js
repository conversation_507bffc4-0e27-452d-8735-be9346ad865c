import * as vendorsPages from '../components';

export const createNewVendor = ({ vendorName, contact, address }) => {
  vendorsPages.list.addVendorButton.click();
  vendorsPages.addEditVendor.companyNameInput.type(vendorName);

  if (address) {
    vendorsPages.addEditVendor.addressInput.type(address);
  }
  if (contact) {
    vendorsPages.addEditVendor.nameOfContactInput.type(contact);
  }
  vendorsPages.addEditVendor.createVendorButton.click();
};

export const verifyVendorInList = ({ vendorName, contact, address }) => {
  cy.contains('h2', 'Vendors').should('be.visible');
  vendorsPages.list.vendorsInList(vendorName).shouldBeVisible();

  if (contact) {
    vendorsPages.list.vendorsInList(contact).shouldBeVisible();
  }
  if (address) {
    vendorsPages.list.vendorsInList(address).shouldBeVisible();
  }
};

export const verifyVendorDetails = ({ vendorName, contact, address }) => {
  vendorsPages.list.vendorsInList(vendorName).click();
  vendorsPages.detailsVendor.companyName.shouldContain(vendorName);

  if (address) {
    vendorsPages.detailsVendor.address.shouldContain(address);
  }
  if (contact) {
    vendorsPages.detailsVendor.name.shouldContain(contact);
  }
};

export const searchVendors = (vendorName) => {
  vendorsPages.list.searchBar.shouldBeVisible();
  vendorsPages.list.searchBar.clear();
  vendorsPages.list.searchBar.type(vendorName);
  cy.wait(300);
};

export const editVendor = (vendorName1, { vendorName2, address }) => {
  vendorsPages.list.vendorsInList(vendorName1).click();
  vendorsPages.detailsVendor.editButton.click();
  vendorsPages.addEditVendor.companyNameInput.clear();
  vendorsPages.addEditVendor.companyNameInput.type(vendorName2);
  vendorsPages.addEditVendor.addressInput.clear();
  vendorsPages.addEditVendor.addressInput.type(address);
  vendorsPages.addEditVendor.saveChangesButton.click();
  cy.contains('Vendor updated').should('be.visible');
  vendorsPages.detailsVendor.backButton.click();
};

export const canNotEditVendor = () => {
  vendorsPages.detailsVendor.editButton.shouldNotExist();
};

export const readExportCsvFile = (vendorName) => {
  return cy.readFile(`cypress/downloads/upkeep-vendors.csv`).then((content) => {
    expect(content).to.contain(vendorName);
  });
};

export const exportVendors = (vendorName) => {
  vendorsPages.list.threeDotsMenuButton.click();
  vendorsPages.list.exportFilteredViewButton.click();
  readExportCsvFile(vendorName);
};

export const clearSearchBar = () => {
  vendorsPages.list.searchBar.clear();
};

/**
 * Searches for customers in list
 * @param {string} customerName
 * @param {boolean} shouldResult {to determine if we should see customer in list}
 */
export const searchForVendor = (vendorName, shouldResult = true) => {
  vendorsPages.list.searchBar.type(vendorName);
  if (shouldResult) {
    vendorsPages.list.vendorsInList(vendorName).shouldBeVisible();
  } else {
    vendorsPages.list.vendorsInList(vendorName).shouldNotExist();
  }
};

export const canCreateVendor = (shouldExist = true) => {
  if (shouldExist === false) {
    vendorsPages.list.addVendorButton.shouldNotExist();
  } else {
    vendorsPages.list.addVendorButton.shouldBeVisible();
  }
};

export const canNotDeleteVendor = () => {
  vendorsPages.detailsVendor.threeDotsMenuButton.shouldNotExist();
  vendorsPages.detailsVendor.deleteButton.shouldNotExist();
};

export const canImportVendors = (shouldExist = true) => {
  if (shouldExist === false) {
    vendorsPages.list.threeDotsMenuButton.click();
    vendorsPages.list.exportFilteredViewButton.shouldBeVisible();
    vendorsPages.list.importExportButton.shouldNotExist();
  } else {
    vendorsPages.list.threeDotsMenuButton.click();
    vendorsPages.list.exportFilteredViewButton.shouldBeVisible();
    vendorsPages.list.importExportButton.shouldBeVisible();
  }
};
