import * as vendorsCustomersPages from '../components';
import { csvJSON } from '../../../helpers/csv';

export const visitCustomersTab = () => {
  vendorsCustomersPages.list.customersTab.shouldExist();
  vendorsCustomersPages.list.customersTab.click();
  cy.url('include', '/app/customers');
};

export const verifyCustomerInList = (customerName) => {
  vendorsCustomersPages.list.customerRow(customerName).shouldBeVisible();
};

export const verifyCustomerDetails = (
  customerName,
  customerAddress,
  phoneNumber,
) => {
  vendorsCustomersPages.list.customerRow(customerName).shouldExist();
  vendorsCustomersPages.list.customerRow(customerName).click();
  vendorsCustomersPages.detailsCustomer.customerName.shouldContain(
    customerName,
  );

  // Check other fields if applicable
  if (customerAddress) {
    vendorsCustomersPages.detailsCustomer.address.shouldContain(
      customerAddress,
    );
  }
  if (phoneNumber) {
    vendorsCustomersPages.detailsCustomer.phoneNumber.shouldContain(
      phoneNumber,
    );
  }
};

/**
 * Searches for customers in list
 * @param {string} customerName
 * @param {boolean} shouldResult {to determine if we should see customer in list}
 */
export const searchForCustomer = (customerName, shouldResult = true) => {
  vendorsCustomersPages.list.searchBar.type(customerName);
  if (shouldResult) {
    vendorsCustomersPages.list.customerRow(customerName).shouldBeVisible();
  } else {
    vendorsCustomersPages.list.customerRow(customerName).shouldNotExist();
  }
};

export const clearSearchBar = () => {
  vendorsCustomersPages.list.searchBar.clear();
};

export const clickExportCustomers = () => {
  vendorsCustomersPages.list.threeDotsMenuButton.click();
  vendorsCustomersPages.list.exportFilteredViewButton.click();
};

export const readExportCsvFile = (name) => {
  return cy.readFile(`cypress/downloads/${name}`).then((content) => {
    return csvJSON(content);
  });
};

export const canCreateCustomer = (shouldExist = true) => {
  if (shouldExist === false) {
    vendorsCustomersPages.list.addCustomerButton.shouldNotExist();
  } else {
    vendorsCustomersPages.list.addCustomerButton.shouldBeVisible();
  }
};

export const canImportCustomers = (shouldExist = true) => {
  if (shouldExist === false) {
    vendorsCustomersPages.list.threeDotsMenuButton.click();
    vendorsCustomersPages.list.exportFilteredViewButton.shouldBeVisible();
    vendorsCustomersPages.list.importExportButton.shouldNotExist();
  } else {
    vendorsCustomersPages.list.threeDotsMenuButton.click();
    vendorsCustomersPages.list.exportFilteredViewButton.shouldBeVisible();
    vendorsCustomersPages.list.importExportButton.shouldBeVisible();
  }
};

export const canNotDeleteCustomer = () => {
  vendorsCustomersPages.detailsCustomer.threeDotsMenuButton.shouldNotExist();
  vendorsCustomersPages.detailsCustomer.deleteButton.shouldNotExist();
};

export const canNotEditCustomer = () => {
  vendorsCustomersPages.detailsCustomer.editButton.shouldNotExist();
};
