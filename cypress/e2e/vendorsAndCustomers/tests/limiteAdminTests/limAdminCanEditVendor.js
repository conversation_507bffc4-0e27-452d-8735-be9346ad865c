import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as h from '../../../../helpers';
import * as vendorsCustomersPages from '../../components';

const limAdminCanEditVendor = (testId) => {
  it('limited admin can edit vendor details', { testCase: 'QA-T5680' }, () => {
    const vendor = faker.company.name();
    const vendorEdit = {
      name: faker.company.name(),
      address: faker.address.streetAddress(),
      phone: faker.phone.number(),
      website: `http://${faker.internet.domainName()}.${faker.internet.domainSuffix()}`,
      email: faker.internet.email().toLowerCase(),
      description: faker.lorem.text(),
      billingName: faker.finance.accountName(),
      type: 'Electrical',
    };

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'TECH', {}, sessionToken);
    });

    h.createVendor(vendor);
    cy.contains('Work Orders').should('be.visible');

    upkeepPages.VENDORS_CUSTOMERS.go();
    vendorsCustomersPages.list.vendorsTab.click();

    vendorsCustomersPages.list.customerRow(vendor).shouldBeVisible();
    vendorsCustomersPages.list.customerRow(vendor).click();
    vendorsCustomersPages.detailsVendor.editButton.click();

    vendorsCustomersPages.addEditVendor.companyNameInput.type(vendorEdit.name);
    vendorsCustomersPages.addEditVendor.addressInput.type(vendorEdit.address);
    vendorsCustomersPages.addEditVendor.phoneInput.type(vendorEdit.phone);
    vendorsCustomersPages.addEditVendor.websiteInput.type(vendorEdit.website);
    vendorsCustomersPages.addEditVendor.emailInput.type(vendorEdit.email);
    vendorsCustomersPages.addEditVendor.nameOfContactInput.type('Tester');
    vendorsCustomersPages.addEditVendor.vendorTypeInput.type(vendorEdit.type);
    vendorsCustomersPages.addEditVendor.descriptionInput.type(
      vendorEdit.description,
    );
    vendorsCustomersPages.addEditVendor.saveChangesButton.click();

    vendorsCustomersPages.detailsVendor.companyName.shouldContain(
      vendorEdit.name,
    );
    vendorsCustomersPages.detailsVendor.address.shouldContain(
      vendorEdit.address,
    );
    vendorsCustomersPages.detailsVendor.phoneNumber.shouldContain(
      vendorEdit.phone,
    );
    vendorsCustomersPages.detailsVendor.website.shouldContain(
      vendorEdit.website,
    );
    vendorsCustomersPages.detailsVendor.nameOfContact.shouldContain('Tester');
    vendorsCustomersPages.detailsVendor.email.shouldContain(vendorEdit.email);
    vendorsCustomersPages.detailsVendor.type.shouldContain(vendorEdit.type);
    vendorsCustomersPages.detailsVendor.description.shouldContain(
      vendorEdit.description,
    );
    // list.customerRow(customer.customerName).shouldExist();
    // list.customerRow(customer.customerName).click();

    // detailsCustomer.editButton.click();
    // addEditCustomer.customerNameInput.type(customerEdit.name);
    // addEditCustomer.addressInput.type(customerEdit.address);
    // addEditCustomer.phoneInput.type(customerEdit.phone);
    // addEditCustomer.emailInput.type(customerEdit.email);
    // addEditCustomer.saveEditButton.click();
    // upkeepPages.VENDORS_CUSTOMERS.go();
    // customerHelpers.visitCustomersTab();

    // list.customerRow(customerEdit.name).shouldBeVisible();
    // list.customerRow(customerEdit.name).click();

    // detailsCustomer.customerName.shouldContain(customerEdit.name);
    // detailsCustomer.address.shouldContain(customerEdit.address);
    // detailsCustomer.phoneNumber.shouldContain(customerEdit.phone);
    // detailsCustomer.email.shouldContain(customerEdit.email);
  });
};

export default limAdminCanEditVendor;
