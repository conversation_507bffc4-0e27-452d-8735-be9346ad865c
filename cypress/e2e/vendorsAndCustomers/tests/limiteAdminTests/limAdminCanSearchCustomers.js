import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';
import * as vendorsCustomersPages from '../../components';
import * as h from '../../../../helpers';
import * as customerHelpers from '../../helpers/customersHelpers';

const limAdminCanSearchCustomers = (testId) => {
  it('limited admin can search customer', { testCase: 'QA-T5709' }, () => {
    const customerName = `${faker.company.name()}`;
    const customerName2 = `${faker.company.name()}`;

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
    });

    h.createCustomerWithData({ customerName, hourlyRate: null });

    upkeepPages.VENDORS_CUSTOMERS.go();
    customerHelpers.visitCustomersTab();
    vendorsCustomersPages.list.customerRow(customerName).shouldExist();
    vendorsCustomersPages.list.searchBar.type(customerName2);
    vendorsCustomersPages.list.customerRow(customerName).shouldNotExist();
    cy.contains('No Results').should('be.visible');

    vendorsCustomersPages.list.searchBar.type(customerName);
    vendorsCustomersPages.list.customerRow(customerName).shouldExist();
  });
};

export default limAdminCanSearchCustomers;
