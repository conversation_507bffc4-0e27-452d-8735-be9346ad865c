import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as vendorsCustomersPages from '../../components';
import * as h from '../../../../helpers';
import * as customerHelpers from '../../helpers/customersHelpers';

const limAdminCanExportCustomers = (testId) => {
  it(
    'limited admin can export customers csv file',
    { testCase: 'QA-T5706' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const customersCsvExportFile = 'upkeep-customers.csv';
      const customerName = `${faker.company.name()}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
      });

      h.createCustomerWithData({ customerName, hourlyRate: null });
      cy.contains('Work Orders').should('be.visible');

      upkeepPages.VENDORS_CUSTOMERS.go();
      customerHelpers.visitCustomersTab();
      vendorsCustomersPages.list.customerRow(customerName).shouldExist();
      vendorsCustomersPages.list.threeDotsMenuButton.click();
      vendorsCustomersPages.list.exportFilteredViewButton.click();

      customerHelpers
        .readExportCsvFile(customersCsvExportFile)
        .then((fileContent) => {
          cy.log(fileContent);
          Object.values(fileContent).includes(customerName);
        });
    },
  );
};

export default limAdminCanExportCustomers;
