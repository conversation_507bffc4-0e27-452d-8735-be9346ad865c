// eslint-disable-next-line import/no-unresolved
import { faker } from '@faker-js/faker';
import { getIframeBody } from '../../../../helpers/iframeHelpers';
import { upkeepPages } from '../../../../support/constants';
import { startDromoVendorsImport } from '../../../../helpers/dromoImportHelpers';
import * as vendorsCustomersPages from '../../components';

const limAdminCanDownloadCsvTemplate = (testId) => {
  it('limited admin can import vendors', { testCase: 'QA-T5678' }, () => {
    Cypress.on('uncaught:exception', () => false);

    const csvFile = 'vendors-import.csv';

    const vendor1 = {
      name: faker.company.name(),
      address: faker.address.streetAddress(),
      phone: faker.phone.number(),
      website: `http://${faker.internet.domainName()}.${faker.internet.domainSuffix()}`,
      email: faker.internet.email().toLowerCase(),
      description: 'desc',
      billingName: faker.finance.accountName(),
      type: 'Electrical',
    };

    const csv =
      ' Vendor ID,Business Name,Vendor Type,Phone Number,Point Of Contact,Website,Email,Business Address,Business Description\n' +
      `,"${vendor1.name}",${vendor1.type},${vendor1.phone},Tester,${vendor1.website},${vendor1.email},${vendor1.address},${vendor1.description}`;
    cy.writeFile(`cypress/fixtures/${csvFile}`, csv);

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
    });
    cy.contains('Work Orders').should('be.visible');

    upkeepPages.VENDORS_CUSTOMERS.go();
    vendorsCustomersPages.list.vendorsTab.click();

    vendorsCustomersPages.list.threeDotsMenuButton.click();
    vendorsCustomersPages.list.importExportButton.click();

    vendorsCustomersPages.dromoImport.startImport.click();

    getIframeBody('https://widget.dromo.io/')
      .find('[data-cy="file-input"]')
      .selectFile(`cypress/fixtures/${csvFile}`, {
        action: 'drag-drop',
        force: true,
      });

    startDromoVendorsImport();
    cy.contains('Import Complete. Created 1 and updated 0 Vendors').should(
      'be.visible',
    );

    upkeepPages.VENDORS_CUSTOMERS.go();
    cy.reload();
    vendorsCustomersPages.list.vendorsTab.click();

    vendorsCustomersPages.list.customerRow(vendor1.name).shouldBeVisible();
    vendorsCustomersPages.list.customerRow(vendor1.name).click();

    // vendor details
    vendorsCustomersPages.detailsVendor.companyName.shouldContain(vendor1.name);
    vendorsCustomersPages.detailsVendor.address.shouldContain(vendor1.address);
    vendorsCustomersPages.detailsVendor.phoneNumber.shouldContain(
      vendor1.phone,
    );
    vendorsCustomersPages.detailsVendor.website.shouldContain(vendor1.website);
    vendorsCustomersPages.detailsVendor.nameOfContact.shouldContain('Tester');
    vendorsCustomersPages.detailsVendor.email.shouldContain(vendor1.email);
    vendorsCustomersPages.detailsVendor.type.shouldContain(vendor1.type);
    vendorsCustomersPages.detailsVendor.description.shouldContain(
      vendor1.description,
    );
  });
};

export default limAdminCanDownloadCsvTemplate;
