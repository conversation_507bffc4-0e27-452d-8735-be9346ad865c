import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as vendorsCustomersPages from '../../components';
import * as h from '../../../../helpers';
import * as customerHelpers from '../../helpers/customersHelpers';

const limAdminCanExportVendors = (testId) => {
  it(
    'limited admin can export vendors csv file',
    { testCase: 'QA-T5676' },
    () => {
      const csvExportFile = 'upkeep-vendors.csv';
      Cypress.on('uncaught:exception', () => false);
      const vendor1 = faker.company.name();
      const vendor2 = faker.company.name();
      const vendor3 = faker.company.name();

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
      });

      h.createVendor(vendor1);
      h.createVendor(vendor2);
      h.createVendor(vendor3);
      cy.contains('Work Orders').should('be.visible');

      upkeepPages.VENDORS_CUSTOMERS.go();

      vendorsCustomersPages.list.vendorsTab.click();
      vendorsCustomersPages.list.customerRow(vendor1).shouldExist();
      vendorsCustomersPages.list.customerRow(vendor2).shouldExist();
      vendorsCustomersPages.list.customerRow(vendor3).shouldExist();
      vendorsCustomersPages.list.threeDotsMenuButton.click();
      vendorsCustomersPages.list.exportFilteredViewButton.click();

      customerHelpers.readExportCsvFile(csvExportFile).then((fileContent) => {
        cy.log(fileContent);
        Object.values(fileContent).includes(vendor1);
        Object.values(fileContent).includes(vendor2);
        Object.values(fileContent).includes(vendor3);
      });
    },
  );
};

export default limAdminCanExportVendors;
