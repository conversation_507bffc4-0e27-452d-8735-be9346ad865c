import { upkeepPages } from '../../../../support/constants';

import * as vendorsCustomersPages from '../../components';
import * as h from '../../../../helpers';

const limAdminCanSearchVendors = (testId) => {
  it('limited admin can search vendors', { testCase: 'QA-T5679' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const vendor1 = `vendor1 ${now}`;
    const vendor2 = `vendor2 ${now}`;
    const vendor3 = `vendor3 ${now}`;

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
    });

    h.createVendor(vendor1);
    h.createVendor(vendor2);
    h.createVendor(vendor3);
    cy.contains('Work Orders').should('be.visible');

    upkeepPages.VENDORS_CUSTOMERS.go();

    vendorsCustomersPages.list.vendorsTab.click();
    vendorsCustomersPages.list.customerRow(vendor1).shouldBeVisible();
    vendorsCustomersPages.list.customerRow(vendor2).shouldBeVisible();
    vendorsCustomersPages.list.customerRow(vendor3).shouldBeVisible();

    vendorsCustomersPages.list.searchBar.type(vendor1);
    cy.wait(1000);
    vendorsCustomersPages.list.customerRow(vendor1).shouldBeVisible();
    vendorsCustomersPages.list.customerRow(vendor2).shouldNotExist();
    vendorsCustomersPages.list.customerRow(vendor3).shouldNotExist();

    vendorsCustomersPages.list.searchBar.type(vendor3);
    cy.wait(1000);
    vendorsCustomersPages.list.customerRow(vendor1).shouldNotExist();
    vendorsCustomersPages.list.customerRow(vendor2).shouldNotExist();
    vendorsCustomersPages.list.customerRow(vendor3).shouldBeVisible();
  });
};

export default limAdminCanSearchVendors;
