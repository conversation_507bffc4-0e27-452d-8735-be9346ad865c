// eslint-disable-next-line import/no-unresolved
import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';
import { getIframeBody } from '../../../../helpers/iframeHelpers';
import { startDromoVendorsImport } from '../../../../helpers/dromoImportHelpers';
import { list, detailsCustomer } from '../../components';

import * as customerHelpers from '../../helpers/customersHelpers';
import * as vendorsCustomersPages from '../../components';

const limAdminCanImportCustomer = (testId) => {
  it('limited admin can import customer', { testCase: 'QA-T5708' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const customer = {
      name: faker.company.name(),
      address: faker.address.streetAddress(),
      phone: faker.phone.number(),
      website: `http://${faker.internet.domainName()}.${faker.internet.domainSuffix()}`,
      email: faker.internet.email().toLowerCase(),
      description: faker.lorem.text(),
      rate: '1123',
      billingName: faker.finance.accountName(),
    };

    const csvFile = 'lim-admin-import-customer.csv';

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
    });
    cy.contains('Work Orders').should('be.visible');

    const csv =
      'Customer ID,Customer name,Phone number,Customer address,Website,Billing name,Billing Address Line 1,Billing Address Line 2,Billing Address Line 3,Customer type,Customer description,Email,Currency name\n' +
      `,"${customer.name}","${customer.phone}", "${customer.address}", ${customer.website},"${customer.billingName}",,,,,"${customer.description}",${customer.email},,`;
    cy.writeFile(`cypress/fixtures/${csvFile}`, csv);

    upkeepPages.VENDORS_CUSTOMERS.go();
    customerHelpers.visitCustomersTab();
    vendorsCustomersPages.list.threeDotsMenuButton.click();
    vendorsCustomersPages.list.importExportButton.click();
    vendorsCustomersPages.dromoImport.startCustomerImport.click();

    getIframeBody('https://widget.dromo.io/')
      .find('[data-cy="file-input"]')
      .selectFile(`cypress/fixtures/${csvFile}`, {
        action: 'drag-drop',
        force: true,
      });

    startDromoVendorsImport();
    cy.contains('Import Complete. Created 1 and updated 0 Customers.').should(
      'be.visible',
    );

    upkeepPages.VENDORS_CUSTOMERS.go();
    cy.reload();
    customerHelpers.visitCustomersTab();

    list.customerRow(customer.name).shouldBeVisible();
    list.customerRow(customer.name).click();

    detailsCustomer.customerName.shouldContain(customer.name);
    detailsCustomer.address.shouldContain(customer.address);
    detailsCustomer.phoneNumber.shouldContain(customer.phone);
    detailsCustomer.website.shouldContain(customer.website);
    detailsCustomer.companyName.shouldContain(customer.billingName);
    detailsCustomer.email.shouldContain(customer.email);
    detailsCustomer.description.shouldContain(customer.description);
  });
};

export default limAdminCanImportCustomer;
