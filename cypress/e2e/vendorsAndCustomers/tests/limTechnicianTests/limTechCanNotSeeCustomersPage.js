import { leftNavigation } from '../../../../support/constants';
import * as vendorPages from '../../components';

const limTechCanNotSeeCustomersPage = (testId) => {
  it('Lim Tech cant see customers page', () => {
    Cypress.on('uncaught:exception', () => false);
    cy.window().then((win) => {
      cy.switchUserAndLoginUser(
        testId,
        'LIMITED_TECH',
        {},
        win.localStorage.authToken,
      );
    });

    cy.get(leftNavigation.VENDORS_CUSTOMERS.navSelector).should('not.exist');
    cy.visit('/web/customers/list?sort=createdAt');
    cy.contains("The page you were looking for doesn't").should('be.visible');

    vendorPages.list.pageHeading.shouldNotExist();
  });
};

export default limTechCanNotSeeCustomersPage;
