import { leftNavigation } from '../../../../support/constants';
import * as vendorPages from '../../components';

const limTechCanNotSeeVendorsPage = (testId) => {
  it('Lim tech cant see vendors page', () => {
    Cypress.on('uncaught:exception', () => false);
    // Login as Limited Tech user
    cy.window().then((win) => {
      cy.switchUserAndLoginUser(
        testId,
        'LIMITED_TECH',
        {},
        win.localStorage.authToken,
      );
    });

    cy.get(leftNavigation.VENDORS_CUSTOMERS.navSelector).should('not.exist');
    cy.visit('/web/vendors/list?sort=createdAt');
    cy.contains("The page you were looking for doesn't").should('be.visible');

    vendorPages.list.pageHeading.shouldNotExist();
  });
};

export default limTechCanNotSeeVendorsPage;
