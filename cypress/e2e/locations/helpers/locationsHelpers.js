import { getIframeBody } from '../../../helpers/iframeHelpers';
import { upkeepPages } from '../../../support/constants';
import * as locationsPages from '../components';
import * as h from '../../../helpers';

export const createCsvData = (testData = {}) => {
  let csv = '';
  csv =
    `Name,Address,Assigned To Emails,Customer Assigned Names,Vendor Assigned Names\n` +
    `${testData.locationName},${testData.address},${testData.workerEmail},${testData.customerName},${testData.vendorName}`;
  return csv;
};

export const goToLocationsImportPage = () => {
  locationsPages.list.dropdownButton.shouldBeVisible();
  locationsPages.list.dropdownButton.click();
  locationsPages.list.importDropdown.click();

  cy.url().should('include', '/imports/location');
};

export const downloadLocationsTemplate = () => {
  locationsPages.list.downloadTemplate.click();
};

export const importLocations = (file) => {
  goToLocationsImportPage();
  locationsPages.list.startLocationImportButton.click();
  getIframeBody('https://widget.dromo.io/')
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportCreateSuccess = () => {
  cy.contains('Import Complete. Created 1 and updated 0 Locations.').should(
    'be.visible',
  );
};

export const readLocationTemplate = () => {
  cy.readFile('cypress/downloads/location-sample.csv').then((content) => {
    expect(content).to.contain(
      'Importing Notes (delete this column before importing),Location ID,Name,Address,Longitude,Latitude,Parent Location Name,Assigned To Emails,Team Assigned Names,Customer Assigned Names,Vendor Assigned Names',
    );
  });
};

export const createNewLocation = () => {
  cy.contains('Locations').should('be.visible');
  locationsPages.list.addLocationButton.click();
};

export const fillCreateLocationsForm = ({ locationName, locationAddress }) => {
  locationsPages.form.locationNameInput.type(locationName);
  locationsPages.form.addressInput.type(locationAddress);
};

export const submitCreateLocation = () => {
  locationsPages.form.submitButton.click({ scrollBehavior: 'bottom' });
  cy.contains('Location created').should('be.visible');
};

export const selectLocationInList = (location) => {
  locationsPages.list.locationInList(location).click();
};

export const confirmLocationDetails = ({ locationName, locationAddress }) => {
  locationsPages.details.locationName.shouldContain(locationName);
  locationsPages.details.locationAddress.shouldContain(locationAddress);
};

export const verifyLocationInList = (location) => {
  locationsPages.list.locationInList(location).shouldBeVisible();
};

export const createPartsForLocations = (testData) => {
  // Create Locations and Parts with Locations for general user and location-based user
  for (let i = 0; i < Object.keys(testData).length; i++) {
    // Create a location and attach to a part
    h.createLocation({
      stringName: testData.locations[i],
    }).then((loc) => {
      h.createStockLine(
        {
          partName: testData.parts[i],
          partLines: [{ objectLocation: loc.body.result.id }],
        },
        true,
      );
    });
  }
  // Verify locations have been successfully created
  upkeepPages.LOCATIONS.go();
  cy.reload();
  testData.locations.forEach((location) => {
    verifyLocationInList(location);
  });
};

export const verifyUserCannotAddAssets = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.assetsTab.click();
  locationsPages.details.addAssetButton.shouldNotExist();
};

export const verifyUserCannotAddParts = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.partsTab.click();
  locationsPages.details.addPartButton.shouldNotExist();
};

export const verifyUserCannotAddWorkOrders = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.workOrdersTab.click();
  locationsPages.details.addWoButton.shouldNotExist();
};

export const verifyUserCannotAddFiles = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.filesTab.click();
  locationsPages.details.uploadFiles.shouldNotExist();
};

export const verifyUserCannotAddFloorPlan = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.floorPlansTab.click();
  locationsPages.details.addFloorPlanButton
    .get()
    .should('have.class', 'isLocked');
};

export const dragAndDropFloorPlan = (floorPlanImage) => {
  locationsPages.details.floorPlanUploadBox.selectFile(
    `cypress/fixtures/${floorPlanImage}`,
    {
      action: 'drag-drop',
      force: true,
    },
  );
};

export const addFloorPlan = (locationName, floorPlanName, floorPlanImage) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.floorPlansTab.click();

  // Add floor plan
  locationsPages.details.addFloorPlanButton.shouldBeVisible();
  locationsPages.details.addFloorPlanButton.click();

  // Name floor plan
  locationsPages.details.floorPlanNameInput.type(floorPlanName);

  // Upload floor plan
  dragAndDropFloorPlan(floorPlanImage);
  cy.wait(400);
  locationsPages.details.submitAddFloorPlanButton.click();
  cy.contains('Floorplan added').should('be.visible');
};

export const addMappingPoint = (locationName, asset) => {
  cy.contains(locationName).should('be.visible');

  locationsPages.details.currentFloorPlan.click();
  locationsPages.details.selectAssetMappingPoint.select(asset);
  locationsPages.details.submitAddMappingPointButton.click();
  cy.contains('td', asset).should('be.visible');
};

export const verifyUserEditAddFloorPlan = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.floorPlansTab.click();
  locationsPages.details.editFloorplan.shouldNotExist();
};

export const verifyUserCannotDeleteMappingPoint = (locationName) => {
  cy.contains(locationName).should('be.visible');
  locationsPages.details.floorPlansTab.click();
  locationsPages.details.currentAssetMappingPoint.click();
  locationsPages.details.deleteAssetMappingPoint.shouldNotExist();
};
