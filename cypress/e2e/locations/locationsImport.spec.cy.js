import filterTests from '../../support/filterTests';
import * as locationsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Locations Imports', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'locations-import';
    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      LIMITED_ADMIN: `engineering-test+${testId}_limited_admin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_ADMIN'],
        'BUSINESS_PLUS',
        'team2',
        emails,
      );
    });
    after(() => {
      cy.exec('rm -rf cypress/fixtures/locations-import.csv');
      cy.exec('rm -rf cypress/downloads/location-sample.csv');
    });

    locationsTests.canCreateLocationsViaImport(emails);
    locationsTests.canDownloadLocationsTemplate();
  });
});
