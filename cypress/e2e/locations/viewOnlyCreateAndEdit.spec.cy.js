import filterTests from '../../support/filterTests';
import * as voLocationTests from './tests/viewOnlyTests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('View Only General Locations Tests', () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');

    const now = Date.now();
    const testId = 'vo-locations';
    const emails = {
      VIEW_ONLY: `engineering-test+${testId}_vo_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'tea', emails);
    });

    voLocationTests.voCannotCreateNewLocation(emails, testId);
    voLocationTests.voCannotEditLocation(emails, testId);
    voLocationTests.voCannotDeleteLocation(emails, testId);
    voLocationTests.voCannotCreateAssetViaLocationsDetails(emails, testId);
    voLocationTests.voCannotCreatePartViaLocationsDetails(emails, testId);
    voLocationTests.voCannotCreateWorkOrderViaLocationsDetails(emails, testId);
    voLocationTests.voCannotUploadFileViaLocationDetails(emails, testId);
  });
});
