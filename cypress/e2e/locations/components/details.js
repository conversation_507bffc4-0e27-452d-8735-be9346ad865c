import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  locationName: '[data-cy="Name"]',
  locationAddress: '[data-cy="Address"]',
  editLocationButton: 'button:contains("Edit")',
  moreButton: 'button[data-cy="icon-button"]',
  deleteLocationButton: 'button:contains("Delete")',
  assetsTab: '[data-cy="generic.upkeepEntity.assets"]',
  partsTab: '[data-cy="generic.upkeepEntity.parts"]',
  workOrdersTab: '[data-cy="generic.upkeepEntity.workorders"]',
  filesTab: '[data-cy="generic.upkeepEntity.files"]',
  floorPlansTab: '[data-cy="pages.locations.details.floorplans.title"]',
  addAssetButton: 'a:contains("Create Asset")',
  addPartButton: 'a:contains("Create Part")',
  addWoButton: 'a:contains("Create Work Order")',
  uploadFiles: 'button:contains("Upload")',
  addFloorPlanButton: '[data-cy="Add new floorplan"]',
  floorPlanNameInput: '[data-cy="floorplanName"]',
  floorPlanUploadBox: '[data-cy="dropzone"]',
  submitAddFloorPlanButton: 'button:contains("Submit")',
  editFloorplan: '[data-cy="editFloorplan"]',
  deleteFloorplan: '[data-cy="deleteFloorplan"]',
  currentFloorPlan: '.leaflet-container',
  // floorPlanDropdownLink:
  //   'div[ng-if="canEditFloorPlan()"] a[id="simple-dropdown"]',
  // addMappingPointButton: '[ng-click="moreVm.addMapPointModal()"]',
  selectAssetMappingPoint: '#AssetListButton',
  submitAddMappingPointButton: 'button:contains("Submit")',
  deleteAssetMappingPoint: 'button:contains("Delete")',
  currentAssetMappingPoint: `.custom-marker-icon`,
});

export default details;
