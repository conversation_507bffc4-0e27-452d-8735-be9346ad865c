import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  searchBar: 'input[data-cy="search-bar"]',
  firstLocationInList: 'tr:nth-child(1)',
  dropdownButton: '[data-cy="icon-button"]',
  importDropdown: '[data-cy="menu-item-Import"]',
  startLocationImportButton: '[data-cy="StartLocationImportProcessButton"]',
  downloadTemplate: 'a:contains("Download Template")',
  addLocationButton: 'button:contains("Create Location")',
  mapView: '[data-cy="generic.map"]',
  locationInList: (location) => `tbody tr:contains("${location}")`,
});

export default list;
