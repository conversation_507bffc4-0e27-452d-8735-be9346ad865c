import { AVAILABLE_PLANS_DISPLAY_NAMES } from '../../support/planConstants';
import filterTests from '../../support/filterTests';
import * as locationTests from './tests';
import * as floorPlanTests from './tests/floorPlanTests';

filterTests(['all', 'ui', 'smoke'], () => {
  describe('Create and edit Locations - Admin Business_Plus', () => {
    const testId = 'locations';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'team1');
      cy.contains('h2', 'Work Orders').should('be.visible');
    });

    locationTests.canCreateNewLocation();
    locationTests.canViewLocationWithMap();
  });

  describe('Floor Plans Access - Admin Professional', () => {
    const testId = 'locations-admin-professional';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'PROFESSIONAL', 'team1');
    });

    floorPlanTests.canAddFloorPlan();
  });

  describe(`Floor Plans Access - Admin ${AVAILABLE_PLANS_DISPLAY_NAMES.STARTER}`, () => {
    const testId = 'locations-admin-starter';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'STARTER', 'team1');
    });

    floorPlanTests.cannotAddFloorPlan();
  });
});
