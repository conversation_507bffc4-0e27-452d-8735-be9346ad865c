import filterTests from '../../support/filterTests';
import * as voLocationTests from './tests/viewOnlyTests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('View Only Location Floor Plans Tests', () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');

    const now = Date.now();
    const testId = 'vo-locations-fp';
    const emails = {
      VIEW_ONLY: `engineering-test+${testId}_vo_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'team4');
    });

    voLocationTests.voCannotCreateFloorPlan(emails, testId);
    voLocationTests.voCannotEditFloorPlan(emails, testId);
    voLocationTests.voCannotDeleteFloorPlan(emails, testId);
    voLocationTests.voCannotDeleteMappingPoint(emails, testId);
  });
});
