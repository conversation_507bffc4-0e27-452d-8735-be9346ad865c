import { upkeepPages } from '../../../support/constants';
import * as locationsHelpers from '../helpers/locationsHelpers';

const canCreateNewLocation = () => {
  it('Admin can create a new location', { testCaseId: 'QA-T5819' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const locationName = `Location A ${now}`;
    const locationAddress = '123 South Figueroa Street, Los Angeles, CA, USA';

    const testData = {
      locationName,
      locationAddress,
    };

    cy.wait(600);
    upkeepPages.LOCATIONS.go();
    locationsHelpers.createNewLocation();
    locationsHelpers.fillCreateLocationsForm(testData);
    locationsHelpers.submitCreateLocation();
    locationsHelpers.selectLocationInList(locationName);
    locationsHelpers.confirmLocationDetails(testData);
  });
};

export default canCreateNewLocation;
