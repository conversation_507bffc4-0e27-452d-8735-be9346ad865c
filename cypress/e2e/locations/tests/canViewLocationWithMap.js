import { upkeepPages } from '../../../support/constants';
import * as locationsHelpers from '../helpers/locationsHelpers';
import * as locationsPages from '../components';

const canViewLocationWithMap = () => {
  it(
    'user can view locations with map coordinates',
    { testCaseId: 'QA-T5818' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const locationName = `Location A ${now}`;
      const locationAddress = 'LAX';
      const locationToClick = 'World Way, Los Angeles, CA, USA';

      upkeepPages.LOCATIONS.go();
      locationsHelpers.createNewLocation();
      locationsPages.form.locationNameInput.click().type(locationName);
      locationsPages.form.addressInput.click().type(locationAddress).wait(500);
      // eslint-disable-next-line cypress/no-force
      cy.contains(locationToClick).click({ force: true });
      cy.wait(750);
      locationsHelpers.submitCreateLocation();
      cy.contains('Location created').should('be.visible');
      locationsPages.list.mapView.click();
      cy.reload();
      cy.get('[class*=marker-view]').should('exist');
    },
  );
};

export default canViewLocationWithMap;
