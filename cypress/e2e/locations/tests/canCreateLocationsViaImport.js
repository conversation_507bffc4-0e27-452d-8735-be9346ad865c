import { upkeepPages } from '../../../support/constants';
import { startDromoImport } from '../../../helpers/dromoImportHelpers';

import * as locationsHelpers from '../helpers/locationsHelpers';
import * as h from '../../../helpers';

const canCreateLocationsViaImport = (emails) => {
  it('can create locations via import', { testCaseId: 'QA-T5524' }, () => {
    Cypress.on('uncaught:exception', () => false);

    const csvFile = 'locations-imports.csv';
    const now = Date.now();
    const workerEmail = emails.LIMITED_ADMIN;
    const locationName = `Location A ${now}`;
    const address = '123 Test Ave';
    const vendorName = `Vendor A${now}`;
    const customerName = `Customer A ${now}`;

    const testData = {
      workerEmail,
      locationName,
      address,
      vendorName,
      customerName,
    };

    h.createVendor(vendorName);
    h.createCustomer(customerName);

    // Create csv
    const csvData = locationsHelpers.createCsvData(testData);
    cy.writeFile(`cypress/fixtures/${csvFile}`, csvData);

    // Begin import
    upkeepPages.LOCATIONS.go();
    locationsHelpers.importLocations(csvFile);
    startDromoImport();
    locationsHelpers.verifyImportCreateSuccess();
  });
};

export default canCreateLocationsViaImport;
