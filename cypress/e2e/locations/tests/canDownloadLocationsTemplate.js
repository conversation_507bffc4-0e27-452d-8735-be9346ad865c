import { upkeepPages } from '../../../support/constants';
import * as locationsHelpers from '../helpers/locationsHelpers';

const canDownloadLocationsTemplate = () => {
  it(
    'Admin can download Locations template from dromo-import page',
    { testCaseId: 'QA-T5521' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      // verify app has loaded completely
      cy.contains('Work Order').should('be.visible');

      upkeepPages.LOCATIONS.go();
      locationsHelpers.goToLocationsImportPage();
      locationsHelpers.downloadLocationsTemplate();
      locationsHelpers.readLocationTemplate();
    },
  );
};

export default canDownloadLocationsTemplate;
