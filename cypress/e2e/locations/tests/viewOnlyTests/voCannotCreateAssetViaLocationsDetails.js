import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotCreateAssetViaLocationsDetails = (emails, testId) => {
  it(
    'View Only user cannot create a new asset via location details page',
    { testCaseId: 'QA-T5984' },
    () => {
      const now = Date.now();
      const location = `Asset Location ${now}`;

      // As admin user, create a new location
      h.createLocation({ stringName: location });
      upkeepPages.LOCATIONS.go();
      locationHelpers.verifyLocationInList(location);

      // Login is view only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Verify user is unable to edit location
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.verifyUserCannotAddAssets(location);
    },
  );
};

export default voCannotCreateAssetViaLocationsDetails;
