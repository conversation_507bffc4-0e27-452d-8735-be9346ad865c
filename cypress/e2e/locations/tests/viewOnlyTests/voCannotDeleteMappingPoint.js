import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotDeleteMappingPoint = (emails, testId) => {
  it(
    'View Only user cannot delete a mapping point of a location floor plan',
    { testCaseId: 'QA-T5992' },
    () => {
      const now = Date.now();
      const asset = `Asset ${now}`;
      const location = `Mapping Point Floor Plan ${now}`;
      const floorPlanName = 'First Floor';
      const floorPlanImage = 'floorplan.png';

      // Create location and asset as Admin user
      h.createAsset({ Name: asset }, true);
      h.createLocation({ stringName: location });

      // Create a floor plan as Admin user
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.addFloorPlan(location, floorPlanName, floorPlanImage);
      locationHelpers.addMappingPoint(location, asset);

      // Add a mapping point to floor plan

      // Login as View Only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Go to Admin location and verify user cannot add a floor plan
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.verifyUserCannotDeleteMappingPoint(location, asset);
    },
  );
};

export default voCannotDeleteMappingPoint;
