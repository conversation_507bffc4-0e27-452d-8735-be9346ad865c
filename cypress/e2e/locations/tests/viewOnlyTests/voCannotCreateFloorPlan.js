import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotCreateFloorPlan = (emails, testId) => {
  it(
    'View Only user cannot create a new floor plan via floor plans tab of Location Details page',
    { testCaseId: 'QA-T5989' },
    () => {
      const now = Date.now();
      const location = `Create Floor Plan ${now}`;

      // Create a location as Admin user
      h.createLocation({ stringName: location });

      // Login as View Only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Go to Admin location and verify user cannot add a floor plan
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.verifyUserCannotAddFloorPlan(location);
    },
  );
};

export default voCannotCreateFloorPlan;
