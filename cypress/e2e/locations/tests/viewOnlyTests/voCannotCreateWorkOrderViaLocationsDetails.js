import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotCreateWorkOrderViaLocationsDetails = (emails, testId) => {
  it(
    'View Only user cannot create a new work order via locations details page',
    { testCaseId: 'QA-T5987' },
    () => {
      const now = Date.now();
      const location = `WO Location ${now}`;

      // As admin user, create a new location
      h.createLocation({ stringName: location });
      upkeepPages.LOCATIONS.go();
      locationHelpers.verifyLocationInList(location);

      // Login is view only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Verify user is unable to edit location
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.verifyUserCannotAddWorkOrders(location);
    },
  );
};

export default voCannotCreateWorkOrderViaLocationsDetails;
