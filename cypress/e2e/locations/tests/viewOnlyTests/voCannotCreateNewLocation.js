import { upkeepPages } from '../../../../support/constants';
import * as locationHelpers from '../../helpers/locationsHelpers';
import * as locationsPages from '../../components';

const voCannotCreateNewLocation = (emails, testId) => {
  it(
    'View Only user cannot create/import locations',
    { testCaseId: 'QA-T5983' },
    () => {
      // Verify location can be created as admin user
      upkeepPages.LOCATIONS.go();
      locationHelpers.createNewLocation();
      cy.url().should('contain', '/locations/add');
      upkeepPages.LOCATIONS_URL.go();

      // Login is view only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Verify location cannot be created as view only user
      upkeepPages.LOCATIONS.go();
      cy.contains('Create Location').should('be.visible');
      locationsPages.list.addLocationButton.shouldNotExist();
      locationsPages.list.dropdownButton.click();
      locationsPages.list.importButton.shouldNotExist();
    },
  );
};

export default voCannotCreateNewLocation;
