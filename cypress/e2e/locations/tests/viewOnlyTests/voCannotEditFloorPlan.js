import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotEditFloorPlan = (emails, testId) => {
  it(
    'View Only user cannot edit floor plan via floor plans tab of Location Details page',
    { testCaseId: 'QA-T5990' },
    () => {
      const now = Date.now();
      const location = `Edit Floor Plan ${now}`;
      const floorPlanName = 'First Floor';
      const floorPlanImage = 'floorplan.png';

      // Create a location and add a floor plan as Admin user
      h.createLocation({ stringName: location });
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.addFloorPlan(location, floorPlanName, floorPlanImage);

      // Login as View Only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Go to Admin location and verify user cannot add a floor plan
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationHelpers.verifyUserEditAddFloorPlan(location);
    },
  );
};

export default voCannotEditFloorPlan;
