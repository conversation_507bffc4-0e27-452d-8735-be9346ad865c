import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationsPages from '../../components';
import * as locationHelpers from '../../helpers/locationsHelpers';

const voCannotDeleteLocation = (emails, testId) => {
  it(
    'View Only user cannot delete a location via location details page',
    { testCaseId: 'QA-T5982' },
    () => {
      const now = Date.now();
      const location = `Delete Location ${now}`;

      // As admin user, create a new location
      h.createLocation({ stringName: location });
      upkeepPages.LOCATIONS.go();
      locationHelpers.verifyLocationInList(location);

      // Login is view only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Verify user is unable to edit location
      upkeepPages.LOCATIONS.go();
      locationHelpers.selectLocationInList(location);
      locationsPages.details.deleteLocationButton.shouldNotExist();
    },
  );
};

export default voCannotDeleteLocation;
