import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const canAddFloorPlan = () => {
  it('Admin can add floor plan via floor plans tab of Location Details page', () => {
    const now = Date.now();
    const location = `Mapping Point Floor Plan ${now}`;
    const floorPlanName = 'First Floor';
    const floorPlanImage = 'floorplan.png';

    // Create location as Admin user
    h.createLocation({ stringName: location });

    // Create a floor plan as Admin user
    upkeepPages.LOCATIONS.go();
    locationHelpers.selectLocationInList(location);
    locationHelpers.addFloorPlan(location, floorPlanName, floorPlanImage);
  });
};

export default canAddFloorPlan;
