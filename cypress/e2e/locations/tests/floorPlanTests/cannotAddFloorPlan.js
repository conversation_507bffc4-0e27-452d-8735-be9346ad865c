import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as locationHelpers from '../../helpers/locationsHelpers';

const canAddFloorPlan = () => {
  it('Admin cannot add floor plan via floor plans tab of Location Details page', () => {
    const now = Date.now();
    const location = `Mapping Point Floor Plan ${now}`;

    // Create location as Admin user
    h.createLocation({ stringName: location });

    // should not be able to create floor plan
    upkeepPages.LOCATIONS.go();
    locationHelpers.selectLocationInList(location);
    locationHelpers.verifyUserCannotAddFloorPlan(location);
  });
};

export default canAddFloorPlan;
