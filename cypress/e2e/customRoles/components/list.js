import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  userRolesTab: '[href="/web/settings/sections/user-roles"]',
  roleInList: (name) => `tr:contains("${name}")`,
  optionsButton: (name) => `tr:contains("${name}") button`,
  editOption: 'button:contains("Edit")',
  deleteOption: 'button:contains("Delete")',
  rolesRows: 'table tbody tr',
  modalConfirmDeleteButton: '.card-footer button:contains("Delete")',
});

export default list;
