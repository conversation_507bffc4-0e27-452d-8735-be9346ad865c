import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as h from '../../../../helpers';
import * as customRolesHelpers from '../../helpers/customRolesHelpers';

const canViewCustomRole = () => {
  it('Can view custom roles', () => {
    Cypress.on('uncaught:exception', () => false);
    const name = faker.word.noun({ length: { max: 24 } });
    const description = 'View custom role';

    // Create custom role via API
    h.createRole({ name, description });

    upkeepPages.SETTINGS.go();

    // Verify custom role exists in Roles list
    customRolesHelpers.visitUserRolesTab();
    customRolesHelpers.verifyCustomRoleExists(name);
    customRolesHelpers.verifyRowsContainCustomRole();
  });
};

export default canViewCustomRole;
