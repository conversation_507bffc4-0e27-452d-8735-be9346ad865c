import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';
import * as customRolesHelpers from '../../helpers/customRolesHelpers';

const canCreateCustomRole = () => {
  it('Can create custom role', () => {
    Cypress.on('uncaught:exception', () => false);

    const name = faker.word.noun({ length: { max: 24 } });
    const description = 'Create custom role';

    upkeepPages.SETTINGS.go();

    // Create new custom role
    customRolesHelpers.visitUserRolesTab();
    customRolesHelpers.clickCreateNewRole();
    customRolesHelpers.fillCustomRolesInputs(name, description);

    // Verify custom role is created and in Roles list
    customRolesHelpers.verifyCustomRoleExists(name);
  });
};

export default canCreateCustomRole;
