// eslint-disable-next-line import/no-unresolved
import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as h from '../../../../helpers';
import * as customRolesHelpers from '../../helpers/customRolesHelpers';

const canDeleteCustomRole = () => {
  it('Can delete custom role', () => {
    Cypress.on('uncaught:exception', () => false);

    const name = faker.word.noun({ length: { max: 24 } });
    const description = 'Delete custom role';

    // Create custom role via API
    h.createRole({ name, description });

    upkeepPages.SETTINGS.go();

    // Verify custom role exists
    customRolesHelpers.visitUserRolesTab();
    customRolesHelpers.verifyCustomRoleExists(name);

    // Delete and verify custom role has been deleted
    customRolesHelpers.deleteCustomRole(name);
    cy.reload();
    customRolesHelpers.verifyCustomRoleIsDeleted(name);
  });
};

export default canDeleteCustomRole;
