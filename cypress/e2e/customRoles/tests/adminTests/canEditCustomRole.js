import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';

import * as h from '../../../../helpers';
import * as customRolesHelpers from '../../helpers/customRolesHelpers';

const canEditCustomRole = () => {
  it('Can edit custom role', () => {
    Cypress.on('uncaught:exception', () => false);

    const name = faker.word.noun({ length: { max: 24 } });
    const description = 'Edit custom role';
    const nameEdit = `${name}_edit`;
    const descriptionEdit = 'Edited custom role';

    // Create custom role via API
    h.createRole({ name, description });

    upkeepPages.SETTINGS.go();

    // Verify custom role exists in Roles list
    customRolesHelpers.visitUserRolesTab();
    customRolesHelpers.verifyCustomRoleExists(name);

    // Edit existing custom role
    customRolesHelpers.clickEditRole(name);
    customRolesHelpers.fillCustomRolesInputs(nameEdit, descriptionEdit);

    // Verify custom role has been edited
    customRolesHelpers.verifyCustomRoleExists(name);
  });
};

export default canEditCustomRole;
