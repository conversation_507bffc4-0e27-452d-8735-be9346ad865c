import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const testId = 'roleAdmin';
  const teamName = 'Impact';

  describe('Role Settings Admin', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
    });

    adminTests.canCreateCustomRole();
    adminTests.canDeleteCustomRole();
    adminTests.canEditCustomRole();
    adminTests.canViewCustomRole();
  });
});
