import * as customRolesPages from '../components';

export const visitUserRolesTab = () => {
  cy.url().should('include', '/web/settings/sections');

  customRolesPages.list.userRolesTab.click();
  cy.url().should('include', '/user-roles');
};

export const clickCreateNewRole = () => {
  customRolesPages.addEdit.createRoleButton.click();
};

export const fillCustomRolesInputs = (name, description) => {
  customRolesPages.addEdit.nameInput.click().clear().type(name);
  customRolesPages.addEdit.descriptionInput.click().clear().type(description);

  customRolesPages.addEdit.submitButton.scrollIntoView().click();

  cy.wait(600);
  cy.reload();
};

export const verifyCustomRoleExists = (name) => {
  customRolesPages.list.roleInList(name).shouldBeVisible();
};

export const deleteCustomRole = (name) => {
  customRolesPages.list.optionsButton(name).click({ scrollTo: true });
  customRolesPages.list.deleteOption.click();

  // confirm on delete modal
  customRolesPages.list.modalConfirmDeleteButton.click();
};

export const clickEditRole = (name) => {
  customRolesPages.list.optionsButton(name).click({ scrollTo: true });
  customRolesPages.list.editOption.click();
};

export const verifyCustomRoleIsDeleted = (name) => {
  customRolesPages.list.roleInList(name).shouldNotExist();
};

export const verifyRowsContainCustomRole = () => {
  customRolesPages.list.rolesRows.shouldHaveLengthGreaterThan(6);
};
