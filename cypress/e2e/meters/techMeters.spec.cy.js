import filterTests from '../../support/filterTests';
import { techTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('Meters Tech', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const testId = 'metertechnician';
    const now = Date.now();
    const emails = {
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
      LIMITED_TECH: `engineering-test+${testId}_limited_tech_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH', 'LIMITED_TECH'],
        'BUSINESS_PLUS',
        'My Cool Team',
        emails,
      );
    });

    techTests.techCanEditMeterTrigger(testId);
    techTests.techCreateAddMeterReading(testId);
  });
});
