import * as metersPages from '../components';

export const selectMeter = (meterName) => {
  metersPages.list.meterInRows(meterName).shouldBeVisible();
  metersPages.list.meterInRows(meterName).click();
};

export const deleteMeter = () => {
  metersPages.details.deleteMeter.shouldBeVisible();
  metersPages.details.deleteMeter.click();
  metersPages.details.confirmDeleteMeter.shouldBeVisible();
  metersPages.details.confirmDeleteMeter.click();
  cy.contains('Meter deleted').should('be.visible');
};

export const openAddMeter = () => {
  metersPages.list.addMeterButton.click();
  metersPages.addEdit.addMeterHeading.shouldBeVisible();
};

export const inputTextFields = (meterName) => {
  metersPages.addEdit.meterName.type(meterName);
  metersPages.addEdit.unitsType.type('Degress Fahrenheit');
  metersPages.addEdit.readingFrequency.clear().type(`{selectall}${10}`);
};

export const selectCategory = (category) => {
  metersPages.addEdit.categoryDropdown.click();
  metersPages.addEdit.categoryOption(category).click();
};

export const selectUser = () => {
  metersPages.addEdit.userDropdown.click();
  metersPages.addEdit.firstMenuListOption.click();
};

export const selectLocation = (locationName) => {
  metersPages.addEdit.locationDropdown.click();
  metersPages.addEdit.locationOption(locationName).click();
};

export const selectAsset = () => {
  metersPages.addEdit.assetDropdown.click();
  metersPages.addEdit.firstMenuListOption.click();
};

export const verifyMeterData = (meterName, testData = {}) => {
  metersPages.list.meterInRows(meterName).shouldBeVisible();
  metersPages.list.meterInRows(meterName).click();

  cy.contains('Due today').should('exist');
  metersPages.details.locationName.shouldContain(testData.locationName);
  metersPages.details.assetName.shouldContain(testData.assetName);
  metersPages.details.meterReadingFrequency.shouldContain('Every 10 days');
  metersPages.details.category.shouldContain(testData.category);
};

export const addMeterReading = (value) => {
  metersPages.details.addMeterReading.click({ scrollBehavior: 'bottom' });
  metersPages.details.meterReadingInput.click().type(`{selectall}${value}`);
  metersPages.details.confirmReadingButton.click();
};

export const verifyLastMeterReading = (expectedLastReading) => {
  metersPages.details.lastMeterReading.shouldContain(expectedLastReading);
};

export const verifyNextDueDate = (expectedDaysUntilDue) => {
  cy.reload();
  metersPages.details.nextReadingDetail.shouldContain(expectedDaysUntilDue);
};

export const visitHistoryTab = () => {
  metersPages.details.historyTab.shouldBeVisible();
  metersPages.details.historyTab.click();
};

export const deleteMeterReading = (reading) => {
  metersPages.details.specificMeterReadingDropdown(reading).shouldBeVisible();
  metersPages.details.specificMeterReadingDropdown(reading).click();
  metersPages.details.deleteReadingButton.shouldBeVisible();
  metersPages.details.deleteReadingButton.click();
  metersPages.details.confirmDeleteMeter.click();
  cy.contains('Meter reading deleted successfully').should('be.visible');
};

/**
 * validates meters history
 * meter history tab in details should be already open
 * @param { string } expectedHistory
 */
export const verifyMeterHistory = (expectedHistory) => {
  metersPages.details.specificMeterReading(expectedHistory).shouldBeVisible();
};

export const editMeterReading = (readingVal, newReadingVal) => {
  metersPages.details
    .specificMeterReadingDropdown(readingVal)
    .shouldBeVisible();
  metersPages.details.specificMeterReadingDropdown(readingVal).click();
  metersPages.details.editReadingButton.click();
  metersPages.details.editReadingInput.type(newReadingVal);
  metersPages.details.updateReadingButton.click();
  cy.contains('Meter reading updated successfully').should('be.visible');
};

export const editMeter = (options = {}) => {
  metersPages.details.editMeter.click();

  metersPages.addEdit.readingFrequency.clear().type(`{selectall}${7}`);
  metersPages.addEdit.readingFrequency.shouldHaveValue(7);

  metersPages.addEdit.unitsType.clear().type(options.unitsEdit);
  metersPages.addEdit.meterName.clear().type(options.meterNameEdit);

  metersPages.addEdit.editMeterButton.click();
};

export const verifyEditedMeter = (options = {}) => {
  cy.contains(options.meterNameEdit);
  cy.contains(options.unitsEdit);
  metersPages.details.meterReadingFrequency.shouldContain('Every 7 days');
};

export const openNewWorkOrderTrigger = (meterName) => {
  selectMeter(meterName);
  metersPages.details.newWorkOrder.click();
};

export const inputMeterTriggerFields = (
  woTitle = 'Work Order Title',
  triggerValue = '100',
  locationName,
) => {
  metersPages.details.whenMeterReadingIs.click();
  metersPages.details.greaterThanOption.click();
  metersPages.details.triggerValue.click().type(`{selectall}${triggerValue}`);
  metersPages.details.dueInterval.click().type(`{selectall}${2}`);
  metersPages.details.dueFrequency.click();
  metersPages.details.daysFrequencyOption.click();
  metersPages.details.woTitle.click().type(woTitle);
  metersPages.details.woDescription.click().type('Work Order Description');
  metersPages.details.assignWorkerDropdown.click();
  cy.contains('li', 'tech').click();
  metersPages.details.estimatedDuration.click().type(`{selectall}${15}`);
  metersPages.details.priorityDropdown.click();
  metersPages.details.lowPriority.click();
  metersPages.details.categoryDropdown.scrollIntoView().click();
  metersPages.details.damageOption.click();
  metersPages.details.teamDropdown.click();
  metersPages.details.firstMenuListOption.click();

  // Select Location
  if (locationName) {
    metersPages.details.locationSelector.scrollIntoView().click();
    metersPages.details.menuListOptionWithName(locationName).click();
  }
  // Select Asset
  metersPages.details.assetSelector.scrollIntoView().click();
  metersPages.details.firstMenuListOption.click();

  // Add Purchase Order
  metersPages.details.addPurchaseOrder.scrollIntoView().click();
  metersPages.details.firstMenuListOption.click();
  metersPages.details.portalBackdrop.get().last().click();

  // NOTE: additional worker dropdown doesn' close
  // metersPages.details.additionalWorkerDropdown.click();
  // metersPages.details.firstMenuListOption.click();
  // metersPages.details.portalBackdrop.get().last().click();
};

export const inputNegativeValueMeterTriggerFields = (triggerValue) => {
  const woTitle = 'Negative Value Work Order Title';
  const woDescription = 'Negative Value Work Order Description';

  metersPages.details.whenMeterReadingIs.click();
  metersPages.details.greaterThanOption.click();
  metersPages.details.triggerValue.click().type(triggerValue);
  metersPages.details.dueInterval.click().type('2');
  metersPages.details.dueFrequency.click();
  metersPages.details.daysFrequencyOption.click();
  metersPages.details.woTitle.click().type(woTitle);
  metersPages.details.woDescription.click().type(woDescription);
  metersPages.details.assignWorkerDropdown.click();
  cy.contains('_tech_').click();
};

export const submitMeterTrigger = () => {
  metersPages.details.addEditTrigger.scrollIntoView();
  metersPages.details.addEditTrigger.click({ force: true });
};

export const verifyMeterTrigger = (name = 'Work Order Title', value) => {
  metersPages.details.workOrderTriggerRow(name).shouldBeVisible();
  metersPages.details
    .workOrderTriggerRow(name)
    .shouldContain(`Greater than ${value}`);
};

export const verifyWorkOrderCreated = () => {
  cy.contains(
    'A new work order has just been created based off this new meter reading',
  );
};

export const verifyMeterForViewOnly = (meter) => {
  metersPages.details.meterNameHeading(meter).shouldBeVisible();
};

export const createWorkOrderTrigger = (
  triggerVal,
  dueInterval,
  woTitle = 'Work Order Title',
) => {
  metersPages.details.whenMeterReadingIs.click();
  metersPages.details.greaterThanOption.click();
  metersPages.details.triggerValue.click().type(`{selectall}${triggerVal}`);
  metersPages.details.dueInterval.click().type(`{selectall}${dueInterval}`);
  metersPages.details.dueFrequency.click();
  metersPages.details.daysFrequencyOption.click();
  metersPages.details.woTitle.click().type(woTitle);
  metersPages.details.addEditTrigger.click();
  cy.contains('Trigger created').should('be.visible');
};

export const deleteWorkOrderTrigger = (woTitle) => {
  metersPages.details.workOrderTriggerRow(woTitle).shouldBeVisible();
  metersPages.details.workOrderTriggerDropdown(woTitle).click();
  metersPages.details.workOrderTriggerRowDeleteButton.click();
  metersPages.details.confirmDeleteWorkOrderTriggerButton.click();
  cy.contains('Work order trigger deleted successfully').should('be.visible');
};

export const editWorkOrderTrigger = (woTitle, newTriggerVal) => {
  metersPages.details.workOrderTriggerRow(woTitle).shouldBeVisible();
  metersPages.details.workOrderTriggerDropdown(woTitle).click();
  metersPages.details.workOrderTriggerRowEditButton.click();
  metersPages.details.triggerValue.click().clear().type(newTriggerVal);
  metersPages.details.woDescription.click().type('Work Order Description');
  metersPages.details.addEditTrigger.click();
  cy.contains('Trigger updated').should('be.visible');
};

export const exportMeterHistory = () => {
  metersPages.details.detailsHeaderMenu.shouldBeVisible();
  metersPages.details.detailsHeaderMenu.click();
  metersPages.details.exportReadingsOption.click();
};
