import filterTests from '../../support/filterTests';
import { limitTechTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('Meters Limited Tech', () => {
    const testId = 'limmetertechnician';

    const assetName = 'Asset A';
    const locationName = 'Location A';
    const category = 'Thermo';

    const testData = {
      assetName,
      locationName,
      category,
      testId,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, ['TECH'], 'BUSINESS_PLUS', 'My Cool Team');
    });

    limitTechTests.limtTechCanCreateEditMeter(testId);
    limitTechTests.limtTechCanEditMeterTrigger(testData);
    limitTechTests.limtTechCanDeleteCreatedMeter(testId);
    limitTechTests.limTechcanAddNewWorkOrderTrigger(testId);
    limitTechTests.limTechCanDeleteWorkOrderTrigger(testData);
    limitTechTests.limTechCanSeeMeterReadingsHistoryTab(testId);
    limitTechTests.limTechCanNotImportMeter(testId);
  });
});
