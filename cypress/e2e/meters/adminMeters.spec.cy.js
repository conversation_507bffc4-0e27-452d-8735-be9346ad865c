import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'ui'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('Admin Meters Tests', () => {
    const testId = 'meters-admin';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
    });

    adminTests.canDeleteMeter();
    adminTests.canDeleteMeterReading();
    adminTests.canDeleteWorkOrderTrigger();
    adminTests.canEditMeterReading();
    adminTests.canEditWorkOrderTrigger();
    adminTests.canExportHistoryOfMeter();
  });
});
