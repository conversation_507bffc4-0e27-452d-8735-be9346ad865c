import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const createMeterAndReadings = () => {
  it(
    'Admin can create a meter and add readings',
    { testCaseId: 'QA-T695' },
    () => {
      const meter = `New Meter`;
      const readings = {
        1: '24',
        2: '55',
      };

      // Create meter as Admin user
      h.createMeter({ name: meter });

      // Go to meters page and add readings
      upkeepPages.METERS.go();
      metersHelpers.selectMeter(meter);
      metersHelpers.addMeterReading(readings[1]);
      metersHelpers.addMeterReading(readings[2]);
    },
  );
};

export default createMeterAndReadings;
