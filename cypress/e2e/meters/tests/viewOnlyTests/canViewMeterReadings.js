import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as metersPages from '../../components';

const canViewMeterReadings = () => {
  it(
    'View Only user can see a list of meter readings via history tab on meters details view',
    { testCaseId: 'QA-T698' },
    () => {
      const meter = `New Meter`;
      const readings = {
        1: '24',
        2: '55',
      };

      // Go to meters page as View Only user
      upkeepPages.METERS.go();
      metersPages.list.meterInRows(meter).click();
      metersPages.details.historyTab.click();
      metersHelpers.verifyMeterHistory(readings[1]);
      metersHelpers.verifyMeterHistory(readings[2]);
    },
  );
};

export default canViewMeterReadings;
