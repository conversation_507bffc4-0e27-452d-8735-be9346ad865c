import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';

const canNotImportMeter = () => {
  it('View Only user cant import meter', () => {
    const testId = 'viewOnlymeters';

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'VIEW_ONLY',
        {
          firstName: 'super',
          lastName: 'view only',
        },
        sessionToken,
      );
    });

    upkeepPages.METERS.go();
    metersPages.list.importExportIconButton.shouldNotExist();
  });
};

export default canNotImportMeter;
