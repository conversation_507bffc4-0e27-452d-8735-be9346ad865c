import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';

const canViewMeterDetails = () => {
  it('View Only user can view meter details', { testCaseId: 'QA-T695' }, () => {
    const meter = `New Meter`;

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        'QA-T695',
        'VIEW_ONLY',
        {
          firstName: 'super',
          lastName: 'view only',
        },
        sessionToken,
      );
    });

    // Go to meters page as View Only user
    upkeepPages.METERS.go();
    metersHelpers.selectMeter(meter);
    metersHelpers.verifyMeterForViewOnly(meter);
  });
};

export default canViewMeterDetails;
