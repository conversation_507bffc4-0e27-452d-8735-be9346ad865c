import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';

const canNotCreateMeter = () => {
  it('View Only user cant create meter', () => {
    const testId = 'viewOnlymeters';

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'VIEW_ONLY',
        {
          firstName: 'super',
          lastName: 'view only',
        },
        sessionToken,
      );
    });

    // Go to meters page as View Only user
    upkeepPages.METERS.go();
    metersPages.list.addMeterButton.shouldNotExist();
  });
};

export default canNotCreateMeter;
