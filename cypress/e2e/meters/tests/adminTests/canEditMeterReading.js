import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canEditMeterReading = () => {
  it(
    'Admin can edit Meter reading via History tab on Meter details view',
    { testCaseId: 'QA-T415' },
    () => {
      const now = Date.now();
      const name = `Meter ${now}`;
      const units = 'Celcius';
      const updateFrequency = 7;
      const readingVal = 10;
      const newReadingVal = 11;

      h.createMeter({ name, units, updateFrequency });

      upkeepPages.METERS.go();

      // Input initial meter reading
      metersHelpers.selectMeter(name);
      metersHelpers.addMeterReading(readingVal);

      // Go to edit meter reading
      metersHelpers.visitHistoryTab();
      metersHelpers.verifyMeterHistory(readingVal);
      metersHelpers.editMeterReading(readingVal, newReadingVal);
      metersHelpers.verifyMeterHistory(newReadingVal);
    },
  );
};

export default canEditMeterReading;
