import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canDeleteMeter = () => {
  it('Admin can delete a meter', { testCaseId: 'QA-T42' }, () => {
    const now = Date.now();
    const name = `Delete Meter ${now}`;
    const units = 'Miles';
    const updateFrequency = 7;

    h.createMeter({ name, units, updateFrequency });

    upkeepPages.METERS.go();

    // Select meter to delete
    metersHelpers.selectMeter(name);
    metersHelpers.deleteMeter();
  });
};

export default canDeleteMeter;
