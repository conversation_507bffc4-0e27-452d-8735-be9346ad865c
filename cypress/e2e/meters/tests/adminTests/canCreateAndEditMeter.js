import { upkeepPages } from '../../../../support/constants';

import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';

const canCreateAndEditMeter = (testData) => {
  it('can create and edit a meter', { testCaseId: 'QA-T415' }, () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });

    const meterName = 'Main thermometer Location A Asset A';
    const meterNameEdit = 'Edited Meter Name';
    const unitsEdit = 'Degrees Edited';

    upkeepPages.METERS.go();

    meterHelpers.openAddMeter();
    meterHelpers.inputTextFields(meterName);
    meterHelpers.selectCategory(testData.category);
    meterHelpers.selectUser();
    meterHelpers.selectLocation(testData.locationName);
    meterHelpers.selectAsset();
    metersPages.addEdit.createMeterButton.click();
    meterHelpers.verifyMeterData(meterName, testData);
    meterHelpers.editMeter({ meterNameEdit, unitsEdit });
    meterHelpers.verifyEditedMeter({ meterNameEdit, unitsEdit });
  });
};

export default canCreateAndEditMeter;
