import { upkeepPages } from '../../../../support/constants';

import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canCreateMeterTrigger = () => {
  it('can create meter trigger', { testCaseId: 'QA-T408' }, () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });
    const now = Date.now();
    const meterName = `Meter Trigger Meter ${now}`;
    const units = 'Miles';
    const woTitle = `Meter WO ${now}`;
    const triggerValue = 100;
    const woTrigger = 101;

    h.createMeter({ name: meterName, units });
    upkeepPages.METERS.go();

    meterHelpers.openNewWorkOrderTrigger(meterName);
    meterHelpers.inputMeterTriggerFields(woTitle, triggerValue);
    meterHelpers.submitMeterTrigger();

    meterHelpers.verifyMeterTrigger(woTitle, triggerValue);
    meterHelpers.addMeterReading(woTrigger);
    meterHelpers.verifyWorkOrderCreated({ meterName, woTrigger, units });
  });
};

export default canCreateMeterTrigger;
