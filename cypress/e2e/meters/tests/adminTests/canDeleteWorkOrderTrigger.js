import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canDeleteWorkOrderTrigger = () => {
  it(
    'Admin can delete Work Order trigger on Meter details view',
    { testCaseId: 'QA-T410' },
    () => {
      const now = Date.now();
      const name = `Meter ${now}`;
      const units = 'Liters';
      const updateFrequency = 7;
      const dueInterval = 2;
      const triggerVal = 10;
      const woTitle = `Meter WO ${now}`;

      h.createMeter({ name, units, updateFrequency });

      upkeepPages.METERS.go();
      metersHelpers.openNewWorkOrderTrigger(name);
      metersHelpers.createWorkOrderTrigger(triggerVal, dueInterval, woTitle);
      metersHelpers.deleteWorkOrderTrigger(woTitle);
    },
  );
};

export default canDeleteWorkOrderTrigger;
