import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canExportHistoryOfMeter = () => {
  it(
    'Admin can export History on Meter details view',
    { testCaseId: 'QA-T413' },
    () => {
      const now = Date.now();
      const name = `Meter ${now}`;
      const units = 'Meters';
      const updateFrequency = 7;
      const meterReading = 101;

      h.createMeter({ name, units, updateFrequency });

      upkeepPages.METERS.go();
      metersHelpers.selectMeter(name);
      metersHelpers.addMeterReading(meterReading);
      metersHelpers.visitHistoryTab();
      metersHelpers.verifyMeterHistory(meterReading);
      metersHelpers.exportMeterHistory();
    },
  );
};

export default canExportHistoryOfMeter;
