import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canDeleteMeterReading = () => {
  it('Admin can delete meter reading', { testCaseId: 'QA-T43' }, () => {
    const now = Date.now();
    const name = `Meter ${now}`;
    const units = 'Farenheit';
    const updateFrequency = 7;
    const meterReading = 10;

    h.createMeter({ name, units, updateFrequency });

    // Go to meters page and a meter reading
    upkeepPages.METERS.go();
    metersHelpers.selectMeter(name);
    metersHelpers.addMeterReading(meterReading);
    metersHelpers.visitHistoryTab();
    metersHelpers.deleteMeterReading(meterReading);
  });
};

export default canDeleteMeterReading;
