import { upkeepPages } from '../../../../support/constants';
import * as metersHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const canEditWorkOrderTrigger = () => {
  it(
    'Admin can edit Work Order trigger on Meter details view and then trigger new condition',
    { testCaseId: 'QA-T411' },
    () => {
      const now = Date.now();
      const name = `Meter ${now}`;
      const units = 'Liters';
      const updateFrequency = 7;
      const dueInterval = 4;
      const triggerVal = 20;
      const newTriggerVal = 10;
      const woTitle = `Meter WO ${now}`;

      h.createMeter({ name, units, updateFrequency });

      upkeepPages.METERS.go();
      metersHelpers.openNewWorkOrderTrigger(name);
      metersHelpers.createWorkOrderTrigger(triggerVal, dueInterval, woTitle);
      metersHelpers.editWorkOrderTrigger(woTitle, newTriggerVal);
      metersHelpers.addMeterReading(triggerVal);
      metersHelpers.verifyWorkOrderCreated();
    },
  );
};

export default canEditWorkOrderTrigger;
