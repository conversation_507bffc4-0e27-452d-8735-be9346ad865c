import { leftNavigation } from '../../../../support/constants';
import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limtTechCanCreateEditMeter = (testId) => {
  it(
    'limited tech can add/edit meter trigger',
    { testCaseId: 'QA-T536' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const meterName = `Main thermometer ${now}`;
      const assetName = 'Asset A';
      const locationName = 'Location A';
      const category = 'Thermo';
      const meterNameEdit = 'Edited Meter Name';
      const unitsEdit = 'Degrees Edited';

      const testData = {
        assetName,
        locationName,
        category,
      };

      h.createMeterCategory({ name: category });
      h.createChecklist();
      h.createPurchaseOrder();
      h.createMeterCategory({ name: category });

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });
      h.createLocation({ stringName: locationName }).then((loc) => {
        h.createAsset(
          { objectLocation: loc.body.result.id, Name: assetName },
          true,
        );
      });

      // tech can navigate to meters
      cy.get(leftNavigation.METERS.navSelector).click();

      meterHelpers.openAddMeter();
      meterHelpers.inputTextFields(meterName);
      meterHelpers.selectCategory(category);
      meterHelpers.selectUser();
      meterHelpers.selectLocation(locationName);
      meterHelpers.selectAsset();
      metersPages.addEdit.createMeterButton.click();

      // validate meter details
      meterHelpers.verifyMeterData(meterName, testData);

      // can edit meter trigger
      meterHelpers.editMeter({ meterNameEdit, unitsEdit });
      meterHelpers.verifyEditedMeter({ meterNameEdit, unitsEdit });
    },
  );
};

export default limtTechCanCreateEditMeter;
