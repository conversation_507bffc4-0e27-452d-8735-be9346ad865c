import { upkeepPages } from '../../../../support/constants';

import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limTechCanSeeMeterReadingsHistoryTab = (testId) => {
  it(
    'lim tech can see meter readings via history tab',
    { testCaseId: 'QA-T550' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const meterName = `Meter Trigger Meter ${now}`;
      const meterReading1 = 100;
      const meterReading2 = 95;
      const units = 'Miles';

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });
      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(meterReading1);
      meterHelpers.verifyLastMeterReading(meterReading1);
      meterHelpers.verifyNextDueDate(7, meterName);
      meterHelpers.addMeterReading(meterReading2);
      meterHelpers.verifyLastMeterReading(meterReading2);
      metersPages.details.historyTab.click();
      meterHelpers.verifyMeterHistory(meterReading1);
      meterHelpers.verifyMeterHistory(meterReading2);
    },
  );
};

export default limTechCanSeeMeterReadingsHistoryTab;
