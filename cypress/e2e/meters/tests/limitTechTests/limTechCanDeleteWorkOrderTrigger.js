import { upkeepPages } from '../../../../support/constants';

import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limTechCanDeleteWorkOrderTrigger = ({ testId, locationName }) => {
  it(
    'limit tech can delete Work Order Trigger on meter details view',
    { testCaseId: 'QA-T546' },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });
      const now = Date.now();
      const meterName = `delete meter wo trigger ${now}`;
      const units = 'Miles';
      const triggerValue = 100;
      const woTitle = `wo trigger title ${now}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });
      h.createMeter({ name: meterName, units });
      h.createLocation({ stringName: 'location name' }).then((loc) => {
        h.createAsset({
          objectLocation: loc.body.result.id,
          Name: 'assetName',
        });
      });

      upkeepPages.METERS.go();

      meterHelpers.openNewWorkOrderTrigger(meterName);
      meterHelpers.inputMeterTriggerFields(woTitle, triggerValue, locationName);
      meterHelpers.submitMeterTrigger();
      meterHelpers.verifyMeterTrigger(woTitle, triggerValue);

      // can delete work order triger
      meterHelpers.deleteWorkOrderTrigger(woTitle);
    },
  );
};

export default limTechCanDeleteWorkOrderTrigger;
