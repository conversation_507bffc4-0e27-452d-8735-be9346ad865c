import { upkeepPages } from '../../../../support/constants';

import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limTechcanAddNewWorkOrderTrigger = (testId) => {
  it(
    'limit tech can add New Work Order Trigger on meter details view',
    { testCaseId: 'QA-T544' },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });
      const now = Date.now();
      const meterName = `Meter Trigger Meter ${now}`;
      const units = 'Miles';
      const triggerValue = 100;
      const woTrigger = 101;
      const woTitle = `wo trigger title ${now}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });
      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.openNewWorkOrderTrigger(meterName);
      meterHelpers.inputMeterTriggerFields(woTitle);
      meterHelpers.submitMeterTrigger();

      meterHelpers.verifyMeterTrigger(woTitle, triggerValue);

      // can trigger a creation of Work Order when condition is met on Meter details view
      meterHelpers.addMeterReading(woTrigger);
      meterHelpers.verifyWorkOrderCreated();

      upkeepPages.WORK_ORDERS.go();
      cy.contains(woTitle).should('be.visible');
    },
  );
};

export default limTechcanAddNewWorkOrderTrigger;
