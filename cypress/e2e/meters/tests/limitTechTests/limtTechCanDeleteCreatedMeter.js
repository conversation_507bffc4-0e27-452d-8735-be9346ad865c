import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limtTechCanDeleteCreatedMeter = (testId) => {
  it(
    'limited tech can delete meter they create',
    { testCaseId: 'QA-T538' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const meterName = `Meter Trigger Meter ${now}`;
      const units = 'Miles';

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'super',
            lastName: 'limited tech',
          },
          sessionToken,
        );
      });

      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.deleteMeter(meterName);
      metersPages.list.meterInRows(meterName).shouldNotExist();
    },
  );
};

export default limtTechCanDeleteCreatedMeter;
