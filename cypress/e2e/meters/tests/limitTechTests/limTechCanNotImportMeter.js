import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';

const limTechCanNotImportMeter = (testId) => {
  it('Limited tech cannot import meters', () => {
    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'LIMITED_TECH',
        {
          firstName: 'super',
          lastName: 'lim tech',
        },
        sessionToken,
      );
    });

    upkeepPages.METERS.go();
    metersPages.list.importExportIconButton.shouldNotExist();
  });
};

export default limTechCanNotImportMeter;
