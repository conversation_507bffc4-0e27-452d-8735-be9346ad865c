import { upkeepPages } from '../../../../support/constants';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const limtTechCanEditMeterTrigger = ({
  assetName,
  locationName,
  category,
  testId,
}) => {
  it('limited tech can edit meter trigger', { testCaseId: 'QA-T537' }, () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });

    const now = Date.now();
    const meterName = `meter ${now}`;
    const meterNameEdit = 'Edited Meter Name';
    const unitsEdit = 'Degrees Edited';
    const units = 'Degress Fahrenheit.';
    const woTitle = `Meter WO ${now}`;
    const triggerValue = 100;
    const woTrigger = 101;

    h.createMeterCategory({ name: category });
    h.createPurchaseOrder();

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'LIMITED_TECH',
        {
          firstName: 'super',
          lastName: 'limited tech',
        },
        sessionToken,
      );
    });

    h.createLocation({ stringName: locationName }).then((loc) => {
      h.createAsset(
        { objectLocation: loc.body.result.id, Name: assetName },
        true,
      );
    });

    h.createMeter({ name: meterName, units });
    upkeepPages.METERS.go();

    meterHelpers.openNewWorkOrderTrigger(meterName);
    meterHelpers.inputMeterTriggerFields(woTitle, triggerValue, locationName);
    meterHelpers.submitMeterTrigger();

    meterHelpers.verifyMeterTrigger(woTitle, triggerValue);
    meterHelpers.addMeterReading(woTrigger);
    meterHelpers.verifyWorkOrderCreated();
    meterHelpers.editMeter({ meterNameEdit, unitsEdit });
    meterHelpers.verifyEditedMeter({ meterNameEdit, unitsEdit });
  });
};

export default limtTechCanEditMeterTrigger;
