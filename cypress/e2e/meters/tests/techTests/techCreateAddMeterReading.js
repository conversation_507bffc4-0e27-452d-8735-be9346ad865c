import { upkeepPages } from '../../../../support/constants';

import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCreateAddMeterReading = (testId) => {
  it(
    'tech can add meter reading and see list via history tab',
    { testCaseId: 'QA-T859' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const meterName = `Meter ${now}`;
      const units = 'Miles';
      const meterReading1 = 100;
      const meterReading2 = 95;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'Cool',
            lastName: 'Tech',
          },
          sessionToken,
        );
      });

      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(meterReading1);
      meterHelpers.verifyLastMeterReading(meterReading1);
      meterHelpers.verifyNextDueDate(7, meterName);
      meterHelpers.addMeterReading(meterReading2);
      meterHelpers.verifyLastMeterReading(meterReading2);

      metersPages.details.historyTab.click();
      meterHelpers.verifyMeterHistory(meterReading1);
      meterHelpers.verifyMeterHistory(meterReading2);
    },
  );
};

export default techCreateAddMeterReading;
