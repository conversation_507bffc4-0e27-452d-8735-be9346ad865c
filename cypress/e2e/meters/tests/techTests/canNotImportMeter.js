import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';

const canNotImportMeter = (testId) => {
  it('Tech cant import meter', () => {
    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'TECH',
        {
          firstName: 'super',
          lastName: 'tech',
        },
        sessionToken,
      );
    });

    upkeepPages.METERS.go();
    metersPages.list.importExportIconButton.shouldNotExist();
  });
};

export default canNotImportMeter;
