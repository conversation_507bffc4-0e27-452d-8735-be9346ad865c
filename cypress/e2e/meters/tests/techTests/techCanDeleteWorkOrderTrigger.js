import { upkeepPages } from '../../../../support/constants';

import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanDeleteWorkOrderTrigger = (testId) => {
  it(
    'tech can delete Work Order Trigger on meter details view',
    { testCaseId: 'QA-T863' },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });
      const now = Date.now();
      const meterName = `delete meter wo trigger ${now}`;
      const units = 'Km';
      const triggerValue = 100;
      const dueInterval = 2;
      const woTitle = `wo trigger title ${now}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'super',
            lastName: 'tech',
          },
          sessionToken,
        );
      });
      h.createMeter({ name: meterName, units });
      h.createLocation({ stringName: 'location name' }).then((loc) => {
        h.createAsset({
          objectLocation: loc.body.result.id,
          Name: 'assetName',
        });
      });

      upkeepPages.METERS.go();

      meterHelpers.openNewWorkOrderTrigger(meterName);
      meterHelpers.createWorkOrderTrigger(triggerValue, dueInterval, woTitle);
      meterHelpers.verifyMeterTrigger(woTitle, triggerValue);

      // can delete work order triger
      meterHelpers.deleteWorkOrderTrigger(woTitle);
      metersPages.details.workOrderTriggerRow(woTitle).shouldNotExist();
    },
  );
};

export default techCanDeleteWorkOrderTrigger;
