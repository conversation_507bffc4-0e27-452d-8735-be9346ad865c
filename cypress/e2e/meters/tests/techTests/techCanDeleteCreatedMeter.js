import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanDeleteCreatedMeter = (testId) => {
  it('tech can delete a meter', { testCaseId: 'QA-T855' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const meterName = `Meter ${now}`;
    const units = 'Miles';

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'TECH',
        {
          firstName: 'super',
          lastName: 'tech',
        },
        sessionToken,
      );
    });

    h.createMeter({ name: meterName, units });
    upkeepPages.METERS.go();

    meterHelpers.selectMeter(meterName);
    meterHelpers.deleteMeter(meterName);
    metersPages.list.meterInRows(meterName).shouldNotExist();
  });
};

export default techCanDeleteCreatedMeter;
