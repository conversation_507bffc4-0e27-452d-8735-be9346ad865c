import { upkeepPages } from '../../../../support/constants';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanEditMeterTrigger = (testId) => {
  it('tech can edit meter trigger', { testCaseId: 'QA-T854' }, () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });

    const now = Date.now();
    const meterName = `meter ${now}`;
    const meterNameEdit = `Edited Meter Name ${now}`;
    const unitsEdit = 'Degrees Edited';
    const units = 'Degress Fahrenheit.';
    const woTitle = `Meter WO ${now}`;
    const triggerValue = '100';
    const woTrigger = '101';

    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'TECH',
        {
          firstName: 'Cool',
          lastName: 'Tech',
        },
        sessionToken,
      );
    });

    h.createMeter({ name: meterName, units });
    upkeepPages.METERS.go();

    meterHelpers.openNewWorkOrderTrigger(meterName);
    meterHelpers.createWorkOrderTrigger(triggerValue, 7, woTitle);

    meterHelpers.verifyMeterTrigger(woTitle, triggerValue);
    meterHelpers.addMeterReading(woTrigger);
    meterHelpers.verifyWorkOrderCreated();
    meterHelpers.editMeter({ meterNameEdit, unitsEdit });
    meterHelpers.verifyEditedMeter({ meterNameEdit, unitsEdit });
  });
};

export default techCanEditMeterTrigger;
