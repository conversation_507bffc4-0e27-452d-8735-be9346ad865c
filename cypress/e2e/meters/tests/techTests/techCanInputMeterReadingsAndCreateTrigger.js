import { upkeepPages } from '../../../../support/constants';

import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanInputMeterReadingsAndCreateTrigger = (testId) => {
  it(
    'tech can trigger a creation of Work Order when condition is met on Meter details view',
    { testCaseId: 'QA-T862' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const meterName = `Meter Trigger Meter ${now}`;
      const meterReading1 = 100;
      const meterReading2 = 95;
      const woTitle = `Meter WO ${now}`;
      const triggerValue = 100;
      const units = 'Miles';
      const woTrigger = 101;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'super',
            lastName: 'tech',
          },
          sessionToken,
        );
      });
      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(meterReading1);
      meterHelpers.verifyLastMeterReading(meterReading1);
      meterHelpers.verifyNextDueDate(7, meterName);
      meterHelpers.addMeterReading(meterReading2);
      meterHelpers.verifyLastMeterReading(meterReading2);

      metersPages.details.historyTab.click();

      meterHelpers.verifyMeterHistory(meterReading1);
      meterHelpers.verifyMeterHistory(meterReading2);

      // Create meter trigger
      metersPages.details.detailsTab.click();
      metersPages.details.newWorkOrder.click();
      meterHelpers.createWorkOrderTrigger(triggerValue, 7, woTitle);

      meterHelpers.verifyMeterTrigger(woTitle, triggerValue);
      meterHelpers.addMeterReading(woTrigger);
      meterHelpers.verifyWorkOrderCreated();
    },
  );
};

export default techCanInputMeterReadingsAndCreateTrigger;
