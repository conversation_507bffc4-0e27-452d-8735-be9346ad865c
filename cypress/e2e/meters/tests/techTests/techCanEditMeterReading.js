import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanEditMeterReading = (testId) => {
  it(
    'tech can delete meter reading  via history tab',
    { testCaseId: 'QA-T869' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const meterName = `Meter ${now}`;
      const units = 'Miles';
      const woTrigger = 123;
      const editedMeterReading = 321;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'super',
            lastName: 'tech',
          },
          sessionToken,
        );
      });

      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(woTrigger);

      metersPages.details.historyTab.click();
      meterHelpers.editMeterReading(woTrigger, editedMeterReading);
      meterHelpers.verifyMeterHistory(editedMeterReading);
    },
  );
};

export default techCanEditMeterReading;
