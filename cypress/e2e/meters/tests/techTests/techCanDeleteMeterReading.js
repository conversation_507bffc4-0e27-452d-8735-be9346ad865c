import { upkeepPages } from '../../../../support/constants';
import * as metersPages from '../../components';
import * as meterHelpers from '../../helpers/metersHelpers';
import * as h from '../../../../helpers';

const techCanDeleteMeterReading = (testId) => {
  it(
    'tech can delete meter reading  via history tab',
    { testCaseId: 'QA-T868' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const meterName = `Meter ${now}`;
      const units = 'Miles';
      const woTrigger = 123;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'super',
            lastName: 'tech',
          },
          sessionToken,
        );
      });

      h.createMeter({ name: meterName, units });
      upkeepPages.METERS.go();

      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(woTrigger);

      metersPages.details.historyTab.click();
      metersPages.details.specificMeterReadingDropdown(woTrigger).click();
      metersPages.details.deleteReadingButton.click();
      metersPages.details.confirmDeleteMeter.click();
      metersPages.details.historyTab.click();

      metersPages.details
        .specificMeterReadingDropdown(woTrigger)
        .shouldNotExist();
    },
  );
};

export default techCanDeleteMeterReading;
