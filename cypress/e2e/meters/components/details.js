import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  meterNameHeading: (meter) => `h2:contains("${meter}")`,
  locationName: '[data-cy="generic.upkeepEntity.locationSection"]',
  assetName: '[data-cy="generic.upkeepEntity.assetSection"]',
  meterReadingFrequency: '[data-cy="Update Frequency"]',
  assignedTo: '[data-cy="generic.labels.assignedToSection"]',
  category: '[data-cy="Category"]',
  editMeter: '[data-cy="editMeterButton"]',
  deleteMeter: '[data-cy="deleteMeterButton"]',
  confirmDeleteMeter: '.modal button:contains("Delete")',
  newWorkOrder: '[data-cy="createTriggerButton"]',
  whenMeterReadingIs: '[data-cy="meterOptionIdColumn"] button',
  greaterThanOption: '.item-container:contains("Is greater than")',
  triggerValue: '[data-cy="notificationValue"]',
  dueInterval: '[data-cy="dueInterval"]',
  dueFrequency: '[data-cy="dueFrequencyColumn"] button',
  daysFrequencyOption: '.item-container:contains("Day(s)")',
  woTitle: 'input#title',
  woDescription: 'textarea[data-cy="note"]',
  assignWorkerDropdown: '[data-cy="workOrderMainWorkerColumn"] button',
  estimatedDuration: 'input[name="duration"]',
  priorityDropdown: '#PriorityListButton',
  lowPriority: '.menu-list-option:contains("Low")',
  categoryDropdown: '#CategoryListButton',
  damageOption: '.menu-list-option:contains("Damage")',
  additionalWorkerDropdown: '[data-cy="arrayOfSupportUsersColumn"] button',
  additionalWorkerOption: (user) => `.menu-list-option:contains("${user}")`,
  portalBackdrop: '.portal-backdrop',
  workersAndTeamsTitle: 'span:contains("Workers & Teams")',
  teamDropdown: '[data-cy="teamsColumn"] button',
  locationSelector: '[data-cy="locationColumn"] button',
  firstMenuListOption: '.menu-list-option:first()',
  menuListOptionWithName: (name) => `.menu-list-option:contains("${name}")`,
  assetSelector: '[data-cy="assetColumn"] button',
  addPurchaseOrder: '[data-cy="purchaseOrdersColumn"] button',
  addChecklist: 'button[label="Add Checklist"]',
  checklistDropdown: '#ListButton',
  checklistTitle: 'span:contains("Add Checklist")',
  confirmButton: '[data-cy="Confirm"]',
  addEditTrigger: '[data-cy="addEditMeterTrigger"]',
  addMeterReading: '[data-cy="addReadingButton"]',
  meterReadingInput: '.modal [data-cy="meterReadingInput"]',
  confirmReadingButton: '[data-cy="Add Reading"]',
  submitMeterReading: '[ng-click="submitValueCtrl.submitValue(value)"]',
  lastMeterReading: '[data-cy="Last Reading"]',
  historyTab: '[data-cy="pages.meters.tabs.history"]',
  detailsTab: '[data-cy="pages.meters.tabs.details"]',
  previousMeterReadings: 'td.col-pages.meters.history.meterReading',
  specificMeterReading: (reading) => `tr:contains("${reading}")`,
  specificMeterReadingDropdown: (reading) =>
    `tr:contains("${reading}") .table-row-button button`,
  deleteReadingButton: 'button[role="menuitem"]:contains("Delete")',
  editReadingButton: 'button[role="menuitem"]:contains("Edit")',
  editReadingInput: '.modal [data-cy="meterReadingInput"]',
  updateReadingButton: 'button[data-cy="Save Changes"]',
  workOrderTriggerRow: (name) => `tr:contains("${name}")`,
  workOrderTriggerDropdown: (name) =>
    `tr:contains("${name}") .table-row-button button`,
  workOrderTriggerRowDeleteButton: 'button[role="menuitem"]:contains("Delete")',
  workOrderTriggerRowEditButton: `button[role="menuitem"]:contains("Edit")`,
  confirmDeleteWorkOrderTriggerButton: '.modal button:contains("Delete")',
  saveWorkOrderTriggerButton: '[ng-click="EditTriggerCtrl.submit();"]',
  dateRange: '[id="export-date-range"]',
  todayDateOption: '[class="ranges"] li:contains("Today")',
  detailsHeaderMenu: '[data-cy="meterDetailActions"] [data-cy="icon-button"]',
  exportReadingsOption: '[data-cy="menu-item-Export Meter Readings"]',
  nextReadingDetail: '[data-cy="Next Reading"]',
});

export default details;
