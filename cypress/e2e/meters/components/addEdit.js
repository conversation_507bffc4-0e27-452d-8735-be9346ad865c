import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  addMeterHeading: 'h2:contains("Create Meter")',
  meterName: 'input[data-cy="name"]',
  unitsType: 'input[data-cy="units"]',
  readingFrequency: 'input[data-cy="updateFrequency"]',
  categoryDropdown: '#CategoryListButton',
  categoryOption: (option) => `.menu-list-option:contains("${option}")`,
  userDropdown: '#WorkerListButton',
  userOption: (name) => `.menu-list-option:contains("${name}")`,
  firstMenuListOption: '.menu-list-option:first()',
  locationDropdown: '#LocationListButton',
  locationOption: (option) => `.menu-list-option:contains("${option}")`,
  assetDropdown: '#AssetListButton',
  assetOption: (option) => `.menu-list-option:contains("${option}")`,
  createMeterButton: '[data-cy="AddEditMeterButton"]',
  editMeterButton: '[data-cy="AddEditMeterButton"]',
});

export default addEdit;
