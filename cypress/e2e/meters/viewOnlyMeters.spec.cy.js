import filterTests from '../../support/filterTests';
import * as viewOnlyTests from './tests/viewOnlyTests';

filterTests(['all', 'ui', 'tier2'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('View Only meters tests', () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'vo-meters';
    const emails = {
      VIEW_ONLY: `engineering-test+${testId}_view_only_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, ['VIEW_ONLY'], 'BUSINESS_PLUS', '', emails);
    });

    viewOnlyTests.createMeterAndReadings();
    viewOnlyTests.canViewMeterDetails(emails);
    viewOnlyTests.canViewMeterReadings(emails);
    viewOnlyTests.canNotCreateMeter();
    viewOnlyTests.canNotImportMeter();
  });
});
