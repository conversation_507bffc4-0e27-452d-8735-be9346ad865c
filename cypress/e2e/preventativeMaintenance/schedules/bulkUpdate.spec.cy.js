import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('PM Schedule Bulk Update', () => {
    const testId = 'pm-bulk-update';
    Cypress.on('uncaught:exception', () => {
      return false;
    });
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'super team');
    });

    pmTests.canUpdateBulkSchedules();
  });
});
