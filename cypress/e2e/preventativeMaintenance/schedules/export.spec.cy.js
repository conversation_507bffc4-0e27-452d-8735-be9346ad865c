import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'smoke'], () => {
  describe('PM schedules exports', () => {
    const testId = 'pmSchedulesExport';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'super team');
    });

    pmTests.canExportFilteredPMSchedules();
    pmTests.canDownloadPMSchedulesTemplate();
    pmTests.canExportUnfilteredPMSchedules();

    after(() => {
      cy.exec('rm -rf cypress/downloads/pmschedules-sample.csv');
      cy.exec('rm -rf cypress/downloads/upkeep-pmschedules.csv');
    });
  });
});
