import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('PM Schedules Instantiate', () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });
    const testId = 'pmSchedulesInstantiate';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'super team');
    });

    pmTests.canInstantiateRegularScheduleFulfillment();
    pmTests.canInstantiateMeterScheduleIsLessThan();
  });
});
