import filterTests from '../../../support/filterTests';

import * as pmTests from '../tests';

filterTests(['all', 'smoke', 'ui'], () => {
  describe('PM Schedule V3 Delete', () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'pm-v2-delete';

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_ADMIN', 'TECH'],
        'BUSINESS_PLUS',
        'updog',
      );
    });

    pmTests.canDeletePMSchedule();
  });
});
