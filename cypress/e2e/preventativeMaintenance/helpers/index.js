import * as h from '../../../helpers';

export * from './helpers';

/**
 * Creates prequests
 * @param {object} params {asset, checklist, location, meter }
 */
export const createPrequests = (params = {}) => {
  const { asset, checklist, location, meter } = params;
  if (asset) h.createAsset({ Name: asset }, true);
  if (checklist) h.createChecklist({ name: checklist });
  if (location) h.createLocation({ stringName: location });
  if (meter) h.createMeter({ name: meter }, true);
};

/**
 * Trigger PM using instantiate API
 * To use it in local, export CYPRESS_X_API_KEY in terminal
 * @param {string} id pm trigger id
 */
export const instantiatePmTrigger = (id) => {
  const baseUrl = Cypress.env('CYPRESS_API_URL');
  const key = Cypress.env('CYPRESS_X_API_KEY');
  const endpoint = `${baseUrl}/api/v1/preventive-maintenance/triggers/${id}/instantiate`;
  return cy.request({
    method: 'POST',
    headers: {
      'x-api-key': key,
      'Content-Type': ' application/json',
    },
    url: endpoint,
  });
};

/**
 * Trigger PM Schedule using instantiate API
 * To use it in local, export CYPRESS_X_API_KEY in terminal
 * @param {string} id schedule's fullfilment id
 */
export const instantiatePmSchedule = (id, type = 'calendar') => {
  const baseUrl = Cypress.env('CYPRESS_API_URL');
  const key = Cypress.env('CYPRESS_X_API_KEY');
  const endpoint = `${baseUrl}/api/v3/pm/fulfillments/${id}/${
    type === 'calendar' ? 'instantiate' : 'instantiate-meter'
  }`;
  return cy.request({
    method: 'POST',
    headers: {
      'x-api-key': key,
      'Content-Type': ' application/json',
    },
    url: endpoint,
  });
};

/**
 * Get most recent PM Fulfillment Id from PM Template
 * To use it in local, export CYPRESS_X_API_KEY in terminal
 * @param {string} id pm template id
 */
export const getRecentFulfillment = (id) => {
  const baseUrl = Cypress.env('CYPRESS_API_URL');
  const key = Cypress.env('CYPRESS_X_API_KEY');
  const endpoint = `${baseUrl}/api/v3/pm/${id}/schedules?includes[]=fulfillments&sortField[]=createdAt&sortOrder[]=desc&getCount=true`;

  return cy
    .request({
      method: 'GET',
      headers: {
        'x-api-key': key,
        'Content-Type': ' application/json',
      },
      url: endpoint,
    })
    .then((response) => {
      return response.body.data[0].fulfillments[0]._id;
    });
};
