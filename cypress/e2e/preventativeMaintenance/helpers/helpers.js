/* eslint-disable cypress/no-force */
import { basicCalendarSchedule, createBasicPmTemplate } from '../../../helpers';
import { getIframeBody } from '../../../helpers/iframeHelpers';
import * as pmPages from '../components';

const iFrameTag = 'https://widget.dromo.io/';

export const createCsvData = (option, testData = {}) => {
  let csv = '';

  if (option === 'rr') {
    csv =
      `PM Trigger Name,Starts on Date,This Reoccurs Every (Number),This Reoccurs Every (Days/Weeks/Years),Creation Cadence Interval,Creation Cadence Frequency,Next Occurrence Is Based On Completion (YES/NO),Work Order Title,Primary Assignee Email,Additional Assignee Emails\n` +
      `${testData.pmTriggerName},${testData.startsOnDate},${testData.reoccurNum},${testData.reoccurFrequency},${testData.creationCadenceNum},${testData.creationCadenceFrequency},NO,${testData.woTitle},${testData.primaryWorker},${testData.additionalWorker}`;
    return csv;
  }
  if (option === 'boc') {
    csv =
      `PM Trigger Name,Starts on Date,This Reoccurs Every (Number),This Reoccurs Every (Days/Weeks/Years),Creation Cadence Interval,Creation Cadence Frequency,Next Occurrence Is Based On Completion (YES/NO),Work Order Title,Primary Assignee Email,Additional Assignee Emails\n` +
      `${testData.pmTriggerName},${testData.startsOnDate},${testData.reoccurNum},${testData.reoccurFrequency},${testData.creationCadenceNum},${testData.creationCadenceFrequency},YES,${testData.woTitle},${testData.primaryWorker},${testData.additionalWorker}`;
    return csv;
  }

  if (option === 'pmV3') {
    csv =
      `ID,Name,Work Order Name,Work Order Description,Work Order Priority,Work Order Category,Requires Signature (YES/NO),Estimate Hours,Checklist ID,Archived Status (YES/NO),Create First WO Now (YES/NO), Paused (YES/NO)\n` +
      `${testData.id},${testData.pmName},${testData.woTitle},${testData.woDescription},${testData.priority},${testData.category},${testData.requiredSignature},${testData.estimatedHours},${testData.checlistIds},${testData.isArchived},${testData.createFirstWO},${testData.isPaused}`;
    return csv;
  }
};

export const createScheduleCsvData = (data) => {
  let csv = `Schedule ID,PM Template ID,Schedule Type,Starts on Date,Ends on Date,Time Zone,This Reoccurs Every (Number),This Reoccurs Every (Days/Weeks/Years),On Day(s) of week,On Day(s) of month,Creation Cadence Interval,Creation Cadence Frequency,Is Next Occurrence Based On Completion,Asset ID,Location ID,Meter ID,Meter Condition,Meter Condition Value,Meter Due Frequency,Meter Due Interval,Primary Assignee ID,Additional Assignee IDs,Team ID`;
  data.forEach(
    ({
      id = '',
      pmTemplate = '',
      type = '',
      startDate = '',
      endDate = '',
      timeZone = '',
      repeatInterval = '',
      repeatFrequency = '',
      weekdays = '',
      monthdays = '',
      cadenceInterval = '',
      cadenceFreq = '',
      isBasedOnCompletion = '',
      asset = '',
      location = '',
      meter = '',
      meterCondition = '',
      meterConditionValue = '',
      meterDueFrequency = '',
      meterDueInterval = '',
      assignee = '',
      supportUsers = '',
      team = '',
    }) => {
      csv += `\n${id},${pmTemplate},${type},${startDate},${endDate},${timeZone},${repeatInterval},${repeatFrequency},${weekdays},${monthdays},${cadenceInterval},${cadenceFreq},${isBasedOnCompletion},${asset},${location},${meter},${meterCondition},${meterConditionValue},${meterDueFrequency},${meterDueInterval},${assignee},${supportUsers},${team}`;
    },
  );
  return csv;
};

export const createSingleStepPMCsvData = (data) => {
  let csv = `Template ID,PM Name,Work Order Name,Work Order Description,Work Order Priority,Work Order Category,Work Order Requires Signature (Yes/No),Work Order Estimated Duration (Hours),Checklist ID (seperated by comma),Archived Status (Yes/No),Create First WO Now (Yes/No),Paused (Yes/No),Schedule ID,Schedule Type,Starts on Date,Ends on Date,Time Zone,This Reoccurs Every (Number),This Reoccurs Every (Days/Weeks/Months/Years),On Day(s) of week,On Day(s) of month,Creation Cadence Interval,Creation Cadence Frequency,Is Next Occurrence Based On Completion,Asset ID,Location ID,Meter ID,Meter Condition (Every/Less Than/Greater/Than/Exactly),Meter Condition Value,Meter Due Frequency,Meter Due Interval,Primary Assignee ID,Additional Assignee IDs (comma separated),Team ID`;
  data.forEach(
    ({
      // template fields
      id = '',
      pmName = '',
      woTitle = '',
      woDescription = '',
      priority = '',
      category = '',
      requiredSignature = '',
      estimatedHours = '',
      checlistIds = '',
      isArchived = '',
      createFirstWO = '',
      isPaused = '',
      // schedule fields
      scheduleId = '',
      scheduleType = '',
      startDate = '',
      endDate = '',
      timeZone = '',
      repeatInterval = '',
      repeatFrequency = '',
      weekdays = '',
      monthdays = '',
      cadenceInterval = '',
      cadenceFreq = '',
      isBasedOnCompletion = '',
      asset = '',
      location = '',
      meter = '',
      meterCondition = '',
      meterConditionValue = '',
      meterDueFrequency = '',
      meterDueInterval = '',
      assignee = '',
      supportUsers = '',
      team = '',
    }) => {
      csv += `\n${id},${pmName},${woTitle},${woDescription},${priority},${category},${requiredSignature},${estimatedHours},${checlistIds},${isArchived},${createFirstWO},${isPaused},${scheduleId},${scheduleType},${startDate},${endDate},${timeZone},${repeatInterval},${repeatFrequency},${weekdays},${monthdays},${cadenceInterval},${cadenceFreq},${isBasedOnCompletion},${asset},${location},${meter},${meterCondition},${meterConditionValue},${meterDueFrequency},${meterDueInterval},${assignee},${supportUsers},${team}`;
    },
  );
  return csv;
};

export const importPM = (file, forTemplates = false) => {
  cy.contains('Preventive Maintenance').should('be.visible');

  pmPages.list.threeDotMenu.click();
  pmPages.list.importButton.click();
  if (forTemplates) {
    pmPages.imports.dataSetDropdown.click();
    pmPages.imports.dataSetDropdown.type('PM Templates');
    pmPages.imports.dataSetPmTemplates.click();
  }
  pmPages.list[`startImportProcess${forTemplates ? 'V3' : ''}`].click();
  getIframeBody(iFrameTag)
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportCreateSucces = (forTemplates = false) => {
  cy.contains(
    `Import Complete. Created 1 and updated 0 PM ${
      !forTemplates ? 'Triggers' : 'Templates'
    }.`,
  ).should('be.visible');
};

export const importPMSchedules = (file) => {
  pmPages.details.threeDotMenu.click();
  pmPages.details.importButton.click();
  pmPages.details.startImportSchedulesProcess.click();
  getIframeBody(iFrameTag)
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportSchedulesCreateSucces = (created, updated) => {
  cy.contains(
    `Import Complete. Created ${created} and updated ${updated} PM Schedules.`,
  ).should('be.visible');
};

export const importSingleStepPM = (file) => {
  pmPages.list.threeDotMenu.click();
  pmPages.list.importButton.click();
  pmPages.list.startImportPMWithSchedules.click();
  getIframeBody(iFrameTag)
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportSingleStepPMCreateSuccess = (
  created,
  updated,
  createdSchedules,
  updatedSchedules,
) => {
  cy.contains(
    `Import Complete. Created ${created} Template(s) with ${createdSchedules} Schedule(s) and updated ${updated} Template(s) with ${updatedSchedules} Schedule(s)`,
  ).should('be.visible');
};

export const selectImportDataManually = () => {
  getIframeBody(iFrameTag).find('[data-cy="manual-entry-button"]').click();
  getIframeBody(iFrameTag).find('table').should('be.visible');
};

/**
 *
 * @param {object} data should have properties:
 * triggerName: string
 * startsOnDate: string MM/DD/YY HH:mm:ss
 * reoccurNum: string - number
 * reoccurFrequency: string - Days/Weeks/Years [day(s), week(s), month(s), year(s)]
 * workOrderTitle: string
 * creationCadenceNum: string - number
 * creationCadenceFrequency: string - Days/Weeks/Years [day(s), week(s), month(s), year(s)]
 */
export const fillManualImportData = (data, forTemplates = false) => {
  const importFieldSelector = 'tr td';

  // first field index
  const fields = {
    triggerName: 1,
    startsOnDate: 2,
    reoccurNum: 5,
  };

  const findFieldByHeaderDescription = (header, typeValue) => {
    getIframeBody(iFrameTag)
      .find('thead th')
      .each(($element, index) => {
        if ($element.text().includes(header)) {
          getIframeBody(iFrameTag)
            .find(importFieldSelector)
            .eq(index - 1)
            .type(`${typeValue}{enter}`, { delay: 500 });
          return false;
        }
      });
  };

  const scrollIframeHorizontally = (amount) => {
    getIframeBody(iFrameTag).find('.wtHolder').first().scrollTo(amount, '0%');
  };

  if (!forTemplates) {
    getIframeBody(iFrameTag)
      .find(importFieldSelector)
      .eq(fields.triggerName)
      .type(`${data.triggerName}{enter}`, { delay: 250 });
    getIframeBody(iFrameTag)
      .find(importFieldSelector)
      .eq(fields.startsOnDate)
      .type(`${data.startsOnDate}{enter}`);

    scrollIframeHorizontally('15%');
    findFieldByHeaderDescription(
      'This Reoccurs Every (Number)',
      data.reoccurNum,
    );
    findFieldByHeaderDescription(
      'This Reoccurs Every (Days/Weeks/Years)',
      data.reoccurFrequency,
    );

    scrollIframeHorizontally('35%');
    findFieldByHeaderDescription(
      'Creation Cadence Interval',
      data.creationCadenceNum,
    );

    findFieldByHeaderDescription(
      'Creation Cadence Frequency',
      data.creationCadenceFrequency,
    );

    scrollIframeHorizontally('50%');
    findFieldByHeaderDescription('Work Order Title', data.workOrderTitle);
    getIframeBody(iFrameTag)
      .find('thead th')
      .each(($element, index) => {
        if (
          $element
            .text()
            .includes('Next Occurrence Is Based On Completion (YES/NO)')
        ) {
          getIframeBody(iFrameTag)
            .find(importFieldSelector)
            .eq(index - 1)
            .dblclick();
          return false;
        }
      });
  } else {
    findFieldByHeaderDescription('Name', data.pmName);
    findFieldByHeaderDescription('Work Order Name', data.woTitle);
    findFieldByHeaderDescription('Work Order Description', data.woDescription);
    findFieldByHeaderDescription('Work Order Priority', 'High');
  }

  getIframeBody(iFrameTag).find('[data-cy="finish-button"]').wait(3500).click();
  getIframeBody(iFrameTag)
    .find('[data-cy="data-review-alert-primary-button"]')
    .wait(3500)
    .click();
};

/**
 * Export PM filtered view
 */
export const exportFilteredView = () => {
  pmPages.list.threeDotMenu.click();
  pmPages.list.exportFilteredViewButton.click();
  cy.wait(1500);
};

export const exportSchedulesFilteredView = () => {
  pmPages.details.threeDotMenu.click();
  pmPages.details.exportFilteredViewButton.click();
  cy.wait(1500);
};

export const exportPMWithSchedules = () => {
  pmPages.list.threeDotMenu.click();
  pmPages.list.exportPMWithSchedulesButton.click();
  cy.wait(1500);
};

export const filterPmByLowPriority = () => {
  pmPages.list.priorityFilterButton.click();
  pmPages.list.lowPriority.click();
  pmPages.list.saveButton.get().last().click();
};

export const filterPmByMediumPriority = () => {
  pmPages.list.priorityFilterButton.click();
  pmPages.list.mediumPriority.click();
  pmPages.list.saveButton.get().last().click();
};

export const filterPmByHighPriority = () => {
  pmPages.list.priorityFilterButton.click();
  pmPages.list.highPriority.click();
  pmPages.list.saveButton.get().last().click();
};

/**
 * assign asset to given pm trigger
 * @param {string} pmName - pm to assign asset
 * @param {*} assetName - asset to be assigned to pm
 * @param {*} isV2 - if testing new pm ui
 */
export const assignAssetToPm = (pmName, assetName, isV2) => {
  pmPages.list.pmTriggerRow(pmName).click();
  if (isV2) {
    pmPages.details.schedulesTab.click();
    pmPages.addEdit.shceduleRowMenuItem('Every 1 day').click();
    pmPages.addEdit.editScheduleMenu.click();

    pmPages.addEdit.modalRecordsTab.scrollIntoView().click();

    pmPages.addEdit.modalAssetDropdownButton.click();
    pmPages.addEdit.listItem(assetName).scrollIntoView().click();

    pmPages.addEdit.doneButton.click();
  } else {
    pmPages.addEdit.editbutton.click();
    pmPages.addEdit.assignAssetButton.click();
    pmPages.addEdit.assignAssetSearchInput.type(assetName);
    pmPages.addEdit.firstAssetOptionContains(assetName).click();
    pmPages.addEdit.confirmAssetSelectionButton.click();
    pmPages.addEdit.saveButton.click();
  }
};

/**
 * Filter PM triggers by given file name
 * @param {string} file name
 */
export const filterPmByFile = (name) => {
  pmPages.list.filters.click();
  pmPages.list.addFilterButton.click();
  pmPages.list.filterByFilesOption.click();
  cy.wait(600);
  pmPages.list.fileFilterSearchBox.click();
  pmPages.list.menuSearchList.click();
  pmPages.list.menuSearchList.type(name);
  pmPages.list.assetListItem(name).click();
  pmPages.list.filtersHeader.click({ force: true });
  pmPages.list.applyFilterButton.click();
};

/**
 * Filter PM triggers by given file name
 * @param {string} team name
 */
export const filterPmByTeam = (team) => {
  // Verify there is more than 1 PM Trigger in list before filtering
  pmPages.list.pmRow.shouldHaveLengthGreaterThan(2);

  // Filter PM Triggers by Team
  pmPages.list.filters.click();
  pmPages.list.addFilterButton.click();
  pmPages.list.filterByTeamOption.click();
  pmPages.list.filterByTeamDropdown.click();
  pmPages.list.teamInList(team).click();
  pmPages.list.filtersHeader.click({ force: true });
  pmPages.addEdit.portalBackdrop.get().last().click();
  pmPages.list.applyFilterButton.click();
};

/**
 * Filter PM triggers by given file name
 * @param {string} pmWithTeam trigger with a team name
 * @param {string} pmWithoutTeam trigger without a team name
 */
export const verifyPmFilteredByTeam = (pmWithTeam, pmWithoutTeam) => {
  pmPages.list.pmTriggerRow(pmWithTeam).shouldBeVisible();
  pmPages.list.pmTriggerRow(pmWithoutTeam).shouldNotExist();
};

export const filterPmByLocation = (locationName, isV2 = false) => {
  // Verify there is more than 1 PM Trigger in list before filtering
  pmPages.list.pmRow.shouldHaveLengthGreaterThan(2);

  // Filter PM Triggers by Location
  pmPages.list.locationQuickFilter.click();
  if (!isV2) {
    pmPages.list.locationItemInList(locationName).click();
  } else {
    // We're now using same table structure in location picker
    cy.get('.picker').within(() => {
      pmPages.list.locationOptionItem(locationName).click();
    });
  }
  pmPages.list.quickFilterSaveButton.click();
};

export const selectChecklistWith = (name) => {
  pmPages.details.editButton.click();
  pmPages.addEdit.addChecklistsButton.select(name);
  pmPages.addEdit.confirmChecklistsButton.click();
  pmPages.addEdit.updateTasks.click();
  pmPages.addEdit.saveButton.click();
};

/**
 * Filter PM triggers by given assignee name
 * @param {string} name assignee name
 */
export const filterPmByAssignee = (name) => {
  pmPages.list.filterByAssigneeButton.click();
  pmPages.list.assigneeMenuSearchList.type(name);
  cy.contains('span', name).click({ force: true });
  pmPages.list.saveButton.click();
};

/**
 * Filter PM triggers by category
 * @param {string} name category
 */
export const filterPmByCategory = (name) => {
  pmPages.list.filters.click();
  pmPages.list.addFilterButton.click();
  pmPages.list.filterByCategoryption.click();
  pmPages.list.filterByCategoryDropdown.click();
  pmPages.list.assetListItem(name).click();
  pmPages.list.filtersHeader.click({ force: true });
  pmPages.list.applyFilterButton.click();
};

/**
 * Filter PM triggers by given file name
 * @param {string} additional worker name
 */
export const filterPmByAdditionalWorker = (name) => {
  // Verify that there is initially more than 1 PM Trigger in list before filtering
  pmPages.list.pmRow.shouldHaveLengthGreaterThan(2);

  // Filter PM Trigger by Additional Worker
  pmPages.list.filters.click();
  pmPages.list.addFilterButton.click();
  pmPages.list.filterByAdditionalWorkerOption.click();
  pmPages.list.filterByAdditionalWorkerDropdown.click();
  pmPages.list.additionalWorkerInList(name).click();
  pmPages.list.filtersHeader.click({ force: true });
  pmPages.list.applyFilterButton.click({ force: true });
  pmPages.list.applyFilterButton.dblclick();
};

/**
 * Filter PM triggers by given file name
 * @param {string} pm trigger with additional worker
 * @param {string} pm trigger without additional worker
 */
export const verifyPmFilteredByAdditionalWorker = (
  addiitonalWorkerPm,
  basicPm,
) => {
  pmPages.list.pmTriggerName(addiitonalWorkerPm).shouldExist();
  pmPages.list.pmTriggerName(basicPm).shouldNotExist();
};

export const verifyPmFiltered = (pmExist, pmNotExist) => {
  pmPages.list.pmTriggerRow(pmExist).shouldBeVisible();
  pmPages.list.pmTriggerRow(pmNotExist).shouldNotExist();
};

/**
 * selects file from saved files
 * opens modal and selects file
 * @param {string} name file name
 */
export const selectFileFromModal = (name) => {
  pmPages.addEdit.addFromSavedFilesButton.scrollIntoView().click();
  pmPages.addEdit.fileNameCheckbox(name).click();
};

export const selectImageAddEdit = (imageFile) => {
  pmPages.addEdit.dropzoneInputs.get().first().selectFile(imageFile, {
    force: true,
  });

  pmPages.addEdit.dropzoneInputs.get().last().selectFile(imageFile, {
    force: true,
  });
};

export const selectFileAddEdit = (fileName) => {
  pmPages.addEdit.addFromSavedFilesBtn
    .get()
    .scrollIntoView()
    .click({ force: true });
  pmPages.addEdit.selectFileItem(fileName).click();
  pmPages.addEdit.addFileButton.click();
};

/**
 * it scrolls records table in new PM UI > add/edit > records tab
 * @param {number} x horizontal scroll in pixels
 */
export const scrollRecordsTable = (x = 0) => {
  cy.get('[aria-label="scrollable content"]').scrollTo(x, 0);
};

/**
 * It will enter provided value to the PM's Records Tab.
 * @param {string} selector date input name
 * @param {Date} date desired date to be entered
 */
export const enterDateInPMRecords = (selector, date) => {
  cy.get(`[name^="${selector}"]`).click({ force: true });
  cy.get(`[name^="${selector}"]`).eq(1).clear();
  cy.get(`[name^="${selector}"]`).eq(1).type(date);
  pmPages.addEdit.portalBackdrop.get().last().click({ force: true });
};

export const filterSchedulesByAsset = (assetName) => {
  pmPages.details.filters.click();
  pmPages.details.addFilterButton.click();
  pmPages.details.filterByAssetOption.click();
  pmPages.details.filterByAssetDropdown.click();
  pmPages.details.listItemCheckbox(assetName).shouldBeVisible();
  pmPages.details.listItemCheckbox(assetName).click();
  cy.get('.card-header').click({ force: true });
  cy.wait(100);
  pmPages.details.applyFilterButton.click();
  // Check if element exists before clicking it
  // cy.get('.card-header .close-portal').then(($el) => {
  //   if ($el.length) {
  //     cy.wrap($el).click();
  //   }
  // });
};

export const filterSchedulesByLocation = (locName) => {
  pmPages.details.filters.click();
  pmPages.details.addFilterButton.click();
  pmPages.details.filterByLocationOption.click();
  pmPages.details.filterByLocationDropdown.click();
  pmPages.details.listItemCheckbox(locName).click();
  cy.get('.card-header').click({ force: true });
  cy.wait(1000);
  pmPages.details.applyFilterButton.click();
};

// Verify a PM exists in PMs list
export const selectPmInRow = (pmName) => {
  pmPages.list.pmTriggerRow(pmName).shouldBeVisible();
  pmPages.list.pmTriggerRow(pmName).click();
};

const meterSchedules = {
  every: 'Every',
  exactly: 'Equal to',
  everyLessThan: 'Less than',
  everyGreaterThan: 'Greater than',
};

const calendarSchedules = {
  daily: 'everyNDay',
  weekly: 'everyNWeek',
  monthly: 'everyNMonth',
  yearly: 'everyNYear',
};

/**
 * Defines and verifies Meter Schedule
 * @param {string} meterType define meter type [every/exactly/everyLessThan/everyGreaterThan]
 * @param {num} meterVal value for meter to fulfill
 */
export const verifyPmScheduleMeter = (meterType, meterVal) => {
  pmPages.details.scheduleMeter(meterType).shouldBeVisible();
  pmPages.details
    .scheduleMeter(meterType)
    .shouldContain(`${meterSchedules[meterType]} ${meterVal}`);
};

/**
 * Defines and verifies Calendar Schedule
 * @param {string} schedule define schedule type [daily/weekly/monthly/yearly]
 */
export const verifyPmCalendarSchedule = (schedule) => {
  pmPages.details
    .calendarSchedule(calendarSchedules[schedule])
    .shouldBeVisible();
};

export const visitWoTab = () => {
  pmPages.details.workOrderTab.click();
};

export const verifyWoInWoTab = (woName, length = 1) => {
  visitWoTab();
  pmPages.details.woTitleInList(woName).shouldHaveLength(length);
};

export const addSchedulesToPM = () => {
  pmPages.addEdit.addAssetButton.click();
};

/**  PM Schedule Bulk Update * */
export const preparePMForBulkUpdate = (pmName) => {
  const startDate = new Date(new Date().setDate(new Date().getDate() + 3));

  const schedules = [
    basicCalendarSchedule(startDate),
    basicCalendarSchedule(startDate),
    basicCalendarSchedule(startDate, {
      repeatInterval: 2,
    }),
    basicCalendarSchedule(startDate, {
      repeatInterval: 3,
    }),
  ];
  return createBasicPmTemplate({
    name: pmName,
    mainDescription: pmName,
    schedules,
  });
};
