import filterTests from '../../../support/filterTests';

import * as pmTests from '../tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const teamName = 'updog';

  describe('PM Admin pause/resume', () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'pm-v2-pause-resume';

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_ADMIN', 'TECH'],
        'BUSINESS_PLUS',
        teamName,
      );
    });

    pmTests.canPauseTrigger();
    pmTests.canResumeTrigger();
  });
});

filterTests(['all', 'tier2', 'ui'], () => {
  describe('PM Admin Edit', () => {
    Cypress.on('uncaught:exception', () => false);
    const teamName = 'updog';
    const fileName = 'pm-create.png';
    const imageFile = 'cypress/fixtures/cat.jpeg';
    const testId = 'pm-v2-create-edit';

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_ADMIN', 'TECH'],
        'BUSINESS_PLUS',
        teamName,
      );
    });

    const testArgs = {
      teamName,
      fileName,
      imageFile,
    };

    pmTests.canEditBocTrigger();
    pmTests.canEditCalendarSchedule();
    pmTests.canAddCalendarSchedule();
    pmTests.canEditMeterSchedule();
    pmTests.canEditPMTemplate(testArgs);
  });
});
