import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'smoke'], () => {
  describe('PM single step exports', () => {
    const testId = 'pmSingleStepExport';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'super team');
    });

    pmTests.canExportFilteredPM();
    pmTests.canDownloadPMSample();
    pmTests.canExportUnfilteredPM();
  });
});
