import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const teamName = 'updog';
  const fileName = 'pm-create.png';
  const imageFile = 'cypress/fixtures/cat.jpeg';

  describe('PM Create Flow', () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'pmcreate';

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['LIMITED_ADMIN', 'TECH'],
        'BUSINESS_PLUS',
        teamName,
      );
    });

    const testArgs = {
      teamName,
      fileName,
      imageFile,
    };
    pmTests.canCreatePMWithAllDetails(testArgs);
    pmTests.canCreatePMWithCalendarSchedules(testArgs);
    pmTests.canCreatePMWithCalendarAndMeterSchedules();
  });
});
