import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe(
    'PM Single step Imports',
    { featureFlags: { pmSingleStepImport: true } },

    () => {
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const now = Date.now();
      const testId = 'pm-import';
      const emails = {
        ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
        TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
      };

      beforeEach(() => {
        cy.createOrLoginAdmin(
          testId,
          ['ADMIN', 'TECH'],
          'BUSINESS_PLUS',
          'super team',
          emails,
        );
      });
      after(() => {
        cy.exec('rm -rf cypress/fixtures/pm-single-step-import.csv');
      });

      pmTests.canImportPM();
    },
  );
});
