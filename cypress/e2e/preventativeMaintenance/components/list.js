import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  createTriggerButton: 'button:contains("Create PM")',
  optionNoneOrOne: '[data-cy="None or one Asset"]',
  optionMultiple: '[data-cy="Multiple Assets"]',
  pmRow: 'tr',
  pmTriggerRows: (title) => `td:contains("${title}")`,
  pmTriggerRow: (title) => `tr > :has(span[data-cy="${title}"])`,
  pmAssetRow: (asset) =>
    `tr:contains("${asset}") [class*="cell-container col-generic.upkeepEntity.asset"]`,
  pmTriggerName: (title) => `.cell-container:has([data-cy="${title}"])`,
  // pm2TriggerRowContains: (name, description) =>
  //   `[data-cy="${name}"]`,
  pmTriggerRowContains: (name, description) =>
    `tr:contains("${name}") td:contains("${description}"), [data-cy="${name}"]`,
  selectPMTrigger: (pmName) => `tr:contains(${pmName})`,
  nextDueDate: '[data-cy="next-due-date-paused"]',
  dropdownMenuButton: '[data-cy="dropdownMenuBtn"]>button:last()',
  importButton: '[data-cy="menu-item-Import/Export"]',
  exportFilteredViewButton: '[data-cy="menu-item-export-templates"]',
  exportPMWithSchedulesButton:
    '[data-cy="menu-item-export-templates-with-schedules"]',
  startImportProcess: '[data-cy="StartPMTriggerImportProcessButton"]',
  startImportProcessV3: '[data-cy="StartPMTemplateImportProcessButton"]',
  startImportPMWithSchedules:
    '[data-cy="StartPreventiveMaintenanceImportProcessButton"]',
  threeDotMenu: '[data-cy="icon-button"]',
  priorityFilterButton: 'button:contains("Priority")',
  lowPriority: 'li:contains("Low")',
  mediumPriority: 'li:contains("Medium")',
  highPriority: 'li:contains("High")',
  saveButton: '[data-cy="Save"]',
  filters: 'button:contains("Filters")',
  addFilterButton: '.modal button:contains("Add Filter")',
  filterByAssigneeButton: 'button:contains("Assigned To")',
  filterByAssetOption: '.item-container:contains("Asset")',
  filterByFilesOption: '.item-container:contains("Files")',
  filterByAdditionalWorkerOption: '[data-cy="Additional Workers"]',
  assetFilterInput: '#AssetListButton',
  menuSearchList: '.dropDown [data-cy="menu-list-search"]',
  assigneeMenuSearchList: '[data-cy="Assigned ToPickerSearch"]',
  assetListItem: (name) => `.dropDown [data-cy="${name}"]`,
  applyFilterButton: '.modal button:contains("Apply")',
  resetFilterButton: '[data-cy="components.templates.FilterBar.reset"]',
  fileFilterSearchBox: '[id="FilesListButton"]',
  filtersHeader: '.card-header:contains("Filters")',
  searchBar: '[data-cy="search-bar"]',
  filterByTeamOption: 'li[data-cy="Team"]',
  filterByTeamDropdown: 'button#TeamListButton',
  filterByCategoryption: '[data-cy="Category"]',
  filterByCategoryDropdown: '[id="CategoryListButton"]',
  filterByAdditionalWorkerDropdown: '[id="Additional WorkersListButton"]',
  teamInList: (name) => `li[data-cy="${name}"]`,
  locationQuickFilter: 'button:contains("Location")',
  locationItemInList: (name) => `ul>li:contains("${name}")`,
  locationOptionItem: (name) => `td:contains("${name}")`,
  quickFilterSaveButton: 'button[data-cy="Save"]:visible()',
  additionalWorkerInList: (name) => `li[data-cy="${name}"]`,
  scheduleDataCell: (count) => `span[data-cy="${count}"]`,
});

export default list;
