import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  editDetailsButton: 'button:contains("Edit Details")',
  saveDetailsButton: 'button:contains("Save Changes")',
  editScheduleMenu: '[data-cy="edit-schedule"] button',
  deleteScheduleMenu: '[data-cy="delete-schedule"] button',
  detailsTab: '[data-cy="generic.labels.details"]',
  schedulesTab: '[data-cy="generic.labels.schedules"]',
  recordsTab: '[data-cy="generic.labels.records"]',
  modalRecordsTab: '[data-cy="Record Data"]',
  createPMButton: 'button:contains("Create PM")',
  pmTitleInput: '[data-cy="name"]',
  woTitleInput: '[data-cy="mainDescription"]',
  usePMTitleCheckbox: '[data-cy="usePMTitle"]',
  pmDescriptionInput: '[data-cy="description"]',
  createFirstWOCheckbox: '[data-cy="createFirstWO"]',
  priorityDropdownButton: '#PriorityListButton',
  categoryDropdownButton: '#CategoryListButton',
  modalAssetDropdownButton: '#AssetListButton',
  modalLocationDropdownButton: '#LocationListButton',
  modalAssignedToDropdown: '#AssigneeListButton',
  durationInput: '[data-cy="duration"]',
  requiresSignatureToggle: '[data-cy="requiresSignature"]',
  dropzoneInputs: '[data-cy="dropzone"] input',
  addFromSavedFilesBtn: 'button:contains("Add from Saved Files")',
  selectFileItem: (name) => `.modal-checklist-item:contains("${name}")`,
  addFileButton: 'button:contains("Add Files")',
  addPartsButton: 'button:contains("Add Parts")',
  addPartButton: '[data-cy="part-select-modal"] button:contains("Add")',
  partRowItem: (name) => `td:contains("${name}")`,
  addTasksButton: 'button:contains("Add Tasks")',
  addChecklistButton: 'button:contains("Add Checklist")',
  checklistTaskNameInputs: (name) => `.expanded:contains("${name}") input`,
  checklistAddTasksButton: (name) =>
    `section:contains("${name}") button:contains("Add Tasks")`,
  checklistTaskDeleteButtons: (name) =>
    `section:contains("${name}") button:contains("Delete")`,
  checklistModalDropdownButton: '#ListButton',
  checklistModalConfirmButton: '[data-cy="Confirm"]',
  portalBackdrop: '.portal-backdrop',
  dropdownListSearchInput: '[data-cy="menu-list-search"]',
  listItem: (item) => `li[data-cy*="${item}"]`,
  weekdayButton: (item) => `[data-cy="${item}"]`,
  addScheduleButton: 'button:contains("Add Schedule")',
  scheduleType: (data = 'calendar') =>
    `[data-cy="pages.preventiveMaintenance.labels.${data}"]`,
  addRecordButton: 'button:contains("Add Row")',
  doneButton: '[data-cy="Done"]',
  scheduleTypeOption: (type) => `input#${type}`,
  repeatTypeDropdownButton: '[data-cy="repeatTypeColumn"]',
  repeatIntervalInput: '[data-cy="repeatInterval"]',
  repeatFreqDropdownButton: '[data-cy="repeatFrequencyColumn"]',
  monthdaysDropdownButton: '[data-cy="monthdaysColumn"]',
  bySetPositionDropdownButton: '[data-cy="bySetPositionColumn"]',
  weekdaysDropdownButton: '[data-cy="weekdaysColumn"]',
  cadenceIntervalInput: '[data-cy="cadenceInterval"]',
  cadenceFreqManualDropdownButton: '[data-cy="cadenceFreqManualColumn"]',
  cadenceFreqWeekdayDropdownButton: '[data-cy="cadenceFreqWeekdayColumn"]',
  bulkSelectAssetsButton: 'button:contains("Bulk Select Assets")',
  assetDropdownButton: (index) => `[data-cy="asset-${index}Column"]`,
  locationDropdownButton: (index) => `[data-cy="location-${index}Column"]`,
  timezoneDropdownButton: (index) => `[data-cy="timezone-${index}Column"]`,
  assignToDropdownButton: (index) => `[data-cy="assignee-${index}Column"]`,
  additionalWorkersDropdownButton: (index) =>
    `[data-cy="additionalWorkers-${index}Column"]`,
  teamDropdownButton: (index) => `[data-cy="team-${index}Column"]`,
  applyCalendarCheckbox: (index) => `[data-cy="applyCalendar-${index}"]`,
  applyMeterCheckbox: (index) => `[data-cy="applyMeter-${index}"]`,
  meterDropdownButton: (index) => `[data-cy="meter0-${index}Column"]`,
  startDateInput: (index) => `[name="startDate-${index}"]`,
  endDateInput: (index) => `[name="endDate-${index}"]`,
  selectCalendarOption: 'input#calendar',
  selectMeterOption: 'input#meter',
  selectCalendarMeterOption: 'input#both',
  meterConditionButton: '[data-cy="meterConditionColumn"]',
  meterConditionValueInput: '[data-cy="meterConditionValue"]',
  meterDueIntervalInput: '[data-cy="meterDueInterval"]',
  meterDueFreqButton: '[data-cy="meterDueFrequencyColumn"]',
  onDayOption: 'input#monthByBeginningEnd',
  onTheOption: 'input#monthByWeekday',
  cadenceManualOption: 'input#manual',
  cadenceWeekdayOption: 'input#weekday',
  selectAssetFromModal: (name) => `tr:contains("${name}")`,
  chooseAssetsButton: 'button:contains("Choose Assets")',
  shceduleRowMenuItem: (name) =>
    `tr:contains("${name}") [data-cy="icon-button"]`,
  selectShceduleRow: (name) =>
    `tr:contains("${name}") [data-cy^="cellSelector-"]`,
  scheduleFreqEveryNMonthsText: (name) =>
    `td [data-cy="pages.preventiveMaintenance.frequencies.everyNMonths"]:contains("${name}")`,
  scheduleFreqOnDayText: (name) =>
    `td [data-cy="pages.preventiveMaintenance.frequencies.onDay"]:contains("${name}")`,
  scheduleMeterFreqExactly: (name) =>
    `td [data-cy="pages.preventiveMaintenance.meter.exactly"]:contains("${name}")`,
  addWODetailsBtn: 'button:contains("Add Work Order Details")',
  woModalAddBtn: '[data-cy="Add Work Order Details"]',
  editPMTitleBtn: '[data-cy="editPMTitle"]',
  savePMTitleBtn: 'button:contains("Save")',
  addSchedulesButton: 'button:contains("Add Schedules")',
  addAssetButton: 'button:contains("Add Asset")',
  selectCalendarMeterOptionV4: 'label:contains("Calendar OR meter readings")',
  selectCalendarOptionV4: 'label:contains("Calendar")',
  confirmBtn: 'button:contains("Confirm")',
  bulkUpdateSchedulesBtn: '[data-cy="bulk-update-schedules"]',
  bulkUpdateSchedulesAssigneeBtn: '[data-cy="bulk-update-assignee"]',
  bulkUpdateSchedulesPrimaryWorkerBtn:
    '[data-cy="bulk-update-primary-workers"]',
  assigneeWorkersDropdown: '[data-cy="Primary WorkerListButton"]',
  bulkUpdateSchedulesAddBtn: '[data-cy="bulk-update-add-schedules"]',
  bulkUpdateSchedulesUpdateBtn: '[data-cy="confirm-update"]',
  bulkUpdateScheduleExistingSchedule: (name) => `[data-cy="${name}"]`,
  bulkUpdateSelectCalendarOption: '[data-cy="schedule-option-calendar"]',
  confirmPauseButton: '[data-cy="Pause"]',
  confirmResumeButton: '[data-cy="Resume"]',
});

export default addEdit;
