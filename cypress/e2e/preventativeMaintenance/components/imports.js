import { configureSelectorProxy } from '../../../helpers';

const imports = configureSelectorProxy({
  dataSetDropdown: '[data-cy="Data Set"] input',
  dataSetPmTemplates: 'li:contains("PM Templates")',
  dataSetPMSchedules: 'li:contains("PM Schedules")',
  dataSetPM: 'li:contains("Preventive Maintenance"):nth-of-type(2)',
  downloadTemplate: 'a:contains("Download Template")',
  exportCurrentPMSchedules: 'a:contains("Export Current PM Schedules")',
  exportCurrentPmTemplates: 'a:contains("Export Current PM Templates")',
  seeExamplesAndTutorials: 'a:contains("See Examples & Tutorials")',
});

export default imports;
