import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  historyTab: '[data-cy="HistoryTab"]',
  detailsTab: '[data-cy="generic.labels.details"]',
  schedulesTab: '[data-cy="generic.labels.assetsLocations"]',
  workOrderTab: '[data-cy="generic.upkeepEntity.workorders"]',
  actionDropdown: '[data-cy="ActionDropdown"]',
  AssetDataTab: '[data-cy="Asset-specific DataTab"]',
  tasksList: '[data-cy="tasksList"]',
  task: (name) => `[data-cy="${name}"]`,
  editButton: '[data-cy="editPmTriggerButton"]',
  pauseResumeButton: '[data-cy="PauseResumeButton"]',
  backButton: '[data-cy="backButton"]',
  photosCountTag: '[data-cy="countTag"]',
  threeDotMenu: '[data-cy="ActionDropdown"]',
  exportFilteredViewButton: 'button:contains("Export Filtered View")',
  importButton: 'button:contains("Import/Export")',
  startImportSchedulesProcess: '[data-cy="StartPMScheduleImportProcessButton"]',
  getFirstScheduleId: 'tbody td',
  filters: '[data-cy="FilterBar-filters-button"]',
  addFilterButton: '.modal button:contains("Add Filter")',
  filterByAssetOption: '.item-container:contains("Asset")',
  filterByAssetDropdown: '#AssetListButton',
  filterByLocationOption: '.item-container:contains("Location")',
  filterByLocationDropdown: '#LocationListButton',
  listItem: (name) => `li[data-cy="${name}"]`,
  listItemCheckbox: (name) => `li[data-cy="${name}"] .checkbox`,
  filtersHeader: '.card-header:contains("Filters")',
  applyFilterButton: '[data-cy="Apply"]',
  confirmDeleteButton: '[data-cy="Delete"]',
  scheduleMeter: (meterType) =>
    `[data-cy="pages.preventiveMaintenance.meter.${meterType}"]`,
  calendarSchedule: (schedule) =>
    `[data-cy*="pages.preventiveMaintenance.frequencies.${schedule}"]`,
  woTitleInList: (woName) => `td[class*="workOrderTitle"]:contains(${woName})`,
});

export default details;
