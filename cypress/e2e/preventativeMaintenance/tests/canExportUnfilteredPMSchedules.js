import { DateTime } from 'luxon';
import * as pmTriggerPages from '../components';
import { exportSchedulesFilteredView } from '../helpers';
import { createMeter, createBasicPmTemplate } from '../../../helpers';
import { upkeepPages, DOWNLOAD_LOCATION } from '../../../support/constants';

const canExportUnfilteredPMSchedules = () => {
  it(
    'can export pm schedules unfiltered view',
    { testCaseId: 'QA-T6391' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const startDate = DateTime.now().plus({ days: 1 }).toUTC();
      const timeZone = 'UTC';
      const meter1Name = `meter1 ${now}`;
      const pmName = `pmExport${now}`;
      const downloadedFile = `${DOWNLOAD_LOCATION}/upkeep-pmschedules.csv`;

      createMeter({ name: meter1Name }, true).then(({ body }) => {
        const meter = body.result;
        createBasicPmTemplate({
          name: pmName,
          schedules: [
            {
              cadenceFreq: 'DAILY',
              cadenceInterval: 1,
              cadenceType: 'manual',
              isBasedOnCompletion: false,
              repeatFrequency: 'DAILY',
              repeatInterval: 1,
              scheduleType: 'EVERY_N_DAYS',
              startDate,
              timeZone,
              triggerType: 'calendar',
            },
            {
              meter: meter.id,
              meterCondition: 'every',
              meterConditionValue: 3,
              meterDueFrequency: 'hours',
              meterDueInterval: 2,
              startDate,
              timeZone,
              triggerType: 'meter',
            },
          ],
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerName(pmName).click();

      exportSchedulesFilteredView();
      cy.readFile(downloadedFile).then((content) => {
        expect(content).to.contain(
          DateTime.fromISO(startDate).toFormat('LL/dd/yyyy'),
        );
        expect(content).to.contain(timeZone);
        expect(content).to.contain(meter1Name);
      });
    },
  );
};

export default canExportUnfilteredPMSchedules;
