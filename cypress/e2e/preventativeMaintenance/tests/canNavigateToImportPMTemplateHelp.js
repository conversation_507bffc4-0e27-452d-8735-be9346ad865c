import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canNavigateToImportPMTemplateHelp = () => {
  it(
    "can navigate to how to import pm template's help page",
    { testCaseId: 'QA-T5443' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.threeDotMenu.click();
      pmTriggerPages.list.importButton.click();
      pmTriggerPages.imports.dataSetDropdown.click();
      pmTriggerPages.imports.dataSetDropdown.type('PM Templates');
      pmTriggerPages.imports.dataSetPmTemplates.click();
      pmTriggerPages.imports.seeExamplesAndTutorials.should(
        'not.have.attr',
        'href',
        '#undefined',
      );
    },
  );
};

export default canNavigateToImportPMTemplateHelp;
