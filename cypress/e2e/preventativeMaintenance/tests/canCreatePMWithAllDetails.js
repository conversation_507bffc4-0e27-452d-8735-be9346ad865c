import * as pmTriggerPages from '../components';
import { createPart, uploadFile } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import { createPrequests } from '../helpers';

const canCreatePMWithAllDetails = ({ fileName, imageFile }) => {
  it(
    'Can create PM with all details',
    {
      testCaseId: 'QA-T6670',
    },
    () => {
      const now = Date.now();
      const pmName = `${now}-name`;
      const pmWOTitle = `${now}-title`;
      const pmDesc = 'PM Description regular repeat';
      const locationName = `location ${now}`;
      const checklistName = `checklist ${now}`;
      const duration = 30;
      const priority = 'High';
      const category = 'Damage';
      const part1Name = 'part 01';
      const part2Name = 'part 02';
      createPrequests({
        checklist: checklistName,
        location: locationName,
      });
      createPart(
        {
          partName: part1Name,
        },
        true,
      );
      createPart(
        {
          partName: part2Name,
        },
        true,
      );
      uploadFile({ originalName: fileName });
      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();

      pmTriggerPages.addEdit.addWODetailsBtn.click();

      pmTriggerPages.addEdit.woTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.pmDescriptionInput.type(pmDesc);
      pmTriggerPages.addEdit.createFirstWOCheckbox.click();
      pmTriggerPages.addEdit.priorityDropdownButton.click();
      pmTriggerPages.addEdit.listItem(priority).click();
      pmTriggerPages.addEdit.categoryDropdownButton.click();
      pmTriggerPages.addEdit.dropdownListSearchInput.type(category);
      pmTriggerPages.addEdit.listItem(category).click();
      pmTriggerPages.addEdit.durationInput.type(duration);
      pmTriggerPages.addEdit.requiresSignatureToggle.click();

      pmTriggerPages.addEdit.dropzoneInputs
        .get()
        .first()
        .selectFile(imageFile, {
          force: true,
        });

      pmTriggerPages.addEdit.dropzoneInputs.get().last().selectFile(imageFile, {
        force: true,
      });

      pmTriggerPages.addEdit.addFromSavedFilesBtn.click();
      pmTriggerPages.addEdit.selectFileItem(fileName).click();
      pmTriggerPages.addEdit.addFileButton.click();

      pmTriggerPages.addEdit.addPartsButton.click({
        scrollBehavior: 'bottom',
      });
      pmTriggerPages.addEdit.partRowItem(part1Name).click();
      pmTriggerPages.addEdit.partRowItem(part2Name).click();
      pmTriggerPages.addEdit.addPartButton.click();

      pmTriggerPages.addEdit.addTasksButton.click({
        scrollBehavior: 'bottom',
      });
      pmTriggerPages.addEdit
        .checklistTaskNameInputs('Other Tasks')
        .get()
        .eq(0)
        .type(' update', {
          scrollBehavior: 'bottom',
        });
      pmTriggerPages.addEdit
        .checklistAddTasksButton('Other Tasks')
        .click({ scrollBehavior: 'bottom' });
      pmTriggerPages.addEdit
        .checklistTaskNameInputs('Other Tasks')
        .get()
        .eq(1)
        .type(' 02', {
          scrollBehavior: 'bottom',
        });
      pmTriggerPages.addEdit
        .checklistTaskNameInputs('Other Tasks')
        .get()
        .eq(1)
        .blur();
      cy.wait(300); // Cypress executes too quickly, triggering the event before processing. I added a delay to handle the event correctly.
      pmTriggerPages.addEdit.woModalAddBtn.click();
      pmTriggerPages.addEdit.editPMTitleBtn.click();
      pmTriggerPages.addEdit.pmTitleInput.type(pmName);
      pmTriggerPages.addEdit.savePMTitleBtn.click();

      pmTriggerPages.addEdit.createPMButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      pmTriggerPages.details.detailsTab.click();
      pmTriggerPages.details.photosCountTag.should('have.text', '1/1');
      cy.contains(pmName);
      cy.contains(pmDesc);
      cy.contains(category);
      cy.contains(duration);
      cy.contains(priority);
      cy.contains(part1Name);
      cy.contains(part2Name);
      cy.contains('Files');
      cy.contains('Misc');
      cy.contains('PM created');
      cy.contains('New Task 02').should('exist');
    },
  );
};

export default canCreatePMWithAllDetails;
