import { createBasicPmTemplate } from '../../../helpers';
import { leftNavigation, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import { instantiatePmSchedule } from '../helpers';

const canPersisFilterSearch = () => {
  it(
    'filter search can persist on pm list view',
    {
      testCaseId: 'QA-T5802',
    },
    () => {
      const now = Date.now();
      const pmName = `PM asset filters-${now}`;

      const mainDescription = `pm work order name ${now}`;
      const pmTriggerToCreate = 3;

      for (let i = 0; i < pmTriggerToCreate; i++) {
        const pm = `${i} ${pmName}`;

        createBasicPmTemplate({
          mainDescription,
          priority: i,
          createFirstWO: true,
          name: pm,
        }).then(({ body }) => {
          // * instantiating pm schedule to generate work order
          const pmData = body.result;
          const [schedule] = pmData.schedules;
          const [firstFulfillment] = schedule.fulfillments;
          instantiatePmSchedule(firstFulfillment._id);
        });
      }

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);

      // search filter
      const pmToSearch = `1 ${pmName}`;
      pmTriggerPages.list.searchBar.type(pmToSearch);
      pmTriggerPages.list.pmTriggerRows(pmToSearch).shouldBeVisible();
      pmTriggerPages.list.pmTriggerRows(pmName).shouldHaveLength(1);

      // navigate to another page and navigate back to PM page
      cy.get(leftNavigation.WORK_ORDERS.navSelector).click();
      cy.contains(mainDescription).should('be.visible');
      cy.get(leftNavigation.PREVENTIVE_MAINTENANCE.navSelector).click();

      // all filters searches will persist on page
      pmTriggerPages.list.pmTriggerRows(pmName).shouldHaveLength(1);
      pmTriggerPages.list.pmTriggerRows(pmToSearch).shouldBeVisible();
    },
  );
};

export default canPersisFilterSearch;
