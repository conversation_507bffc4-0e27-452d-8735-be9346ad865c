import * as pmTriggerPages from '../components';
import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';

const canCreateMultiAssetBocTrigger = () => {
  it(
    'Can create Multi Asset on Completion PM trigger by as expected',
    { testCaseId: 'QA-T6295' },
    () => {
      const now = Date.now();
      const assetName = `asset-xyz ${now}`;
      const pmName = `PM Multi asset on completion-${now}`;
      const pmWOTitle = `PM Multi asset on completion details-${now}`;
      const pmDesc = 'PM Multi asset on completion desc';

      h.createAsset({ Name: assetName }, true);
      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.list.optionMultiple.click();

      pmTriggerPages.addEdit.isNextOccurrenceBasedOnCompletionButton.select(
        'After Completion',
      );

      pmTriggerPages.addEdit.triggerNameInput.type(pmName);
      pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

      pmTriggerPages.addEdit.assignAssetButton.click();
      pmTriggerPages.addEdit.chooseAssetsHeaderButton.select(assetName);
      pmTriggerPages.addEdit.chooseAssetsFooterButton.click();
      pmTriggerPages.addEdit.manageAssetsDoneButton.click();

      pmTriggerPages.addEdit.createButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 1 day');

      pmTriggerPages.details.detailsTab.click();
      cy.contains(pmName);
      cy.contains(pmDesc);
    },
  );
};

export default canCreateMultiAssetBocTrigger;
