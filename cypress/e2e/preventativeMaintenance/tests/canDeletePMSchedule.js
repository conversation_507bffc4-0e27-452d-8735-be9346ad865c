import { basicCalendarSchedule, createBasicPmTemplate } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmPages from '../components';

const canDeletePMSchedule = () => {
  it(
    'should be able to delete pm schedule',
    {
      testCaseId: 'QA-T6304',
    },
    () => {
      const now = Date.now();
      const startDate = new Date(new Date().setDate(new Date().getDate() + 3));
      const pmName = `PM-Delete-${now}`;
      const woName = `WO-${now}`;
      const schedules = [
        basicCalendarSchedule(startDate),
        basicCalendarSchedule(startDate, { repeatInterval: 2 }),
      ];

      createBasicPmTemplate({
        name: pmName,
        mainDescription: woName,
        schedules,
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmPages.list.pmTriggerRows(pmName).shouldHaveLength(1);
      pmPages.list.pmTriggerRow(pmName).click();
      pmPages.details.schedulesTab.click();

      pmPages.addEdit.shceduleRowMenuItem('Every 2 days').shouldHaveLength(1);
      pmPages.addEdit.shceduleRowMenuItem('Every 1 day').click();
      pmPages.addEdit.deleteScheduleMenu.click();
      pmPages.details.confirmDeleteButton.click();
      cy.wait(300);
      pmPages.addEdit.shceduleRowMenuItem('Every 1 day').shouldHaveLength(0);
      pmPages.details.backButton.click();
    },
  );
};

export default canDeletePMSchedule;
