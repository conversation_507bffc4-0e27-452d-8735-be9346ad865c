import moment from 'moment';
import timezone from 'moment-timezone';
import * as h from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import { createPrequests } from '../helpers';

const createPMTrigger = ({ pmName, pmWOTitle, startDate, timezoneName }) => {
  createBasicPmTemplate({
    mainDescription: pmWOTitle,
    priority: 0,
    name: pmName,
    createFirstWO: true,
    schedules: [
      {
        cadenceFreq: 'DAILY',
        cadenceInterval: 1,
        cadenceType: 'manual',
        isBasedOnCompletion: false,
        repeatFrequency: 'DAILY',
        repeatInterval: 1,
        scheduleType: 'EVERY_N_DAYS',
        startDate: timezone(startDate)
          .tz(timezoneName, true)
          .utc()
          .format(moment.HTML5_FMT.DATETIME_LOCAL_SECONDS),
        timeZone: timezoneName,
        triggerType: 'calendar',
      },
    ],
  });
};

const fillPMDetails = ({
  pmName,
  partName,
  checklistName,
  locationName,
  fileName,
  imageFile,
}) => {
  createPrequests({
    checklist: checklistName,
    location: locationName,
  });
  h.createPart(
    {
      partName,
    },
    true,
  );

  pmTriggerPages.addEdit.detailsTab.click();
  pmTriggerPages.addEdit.editDetailsButton.click();

  pmTriggerPages.addEdit.pmTitleInput.clear();
  pmTriggerPages.addEdit.pmTitleInput.type(`EDIT!! ${pmName}`);
  pmTriggerPages.addEdit.woTitleInput.type('Edited WOs name');
  pmTriggerPages.addEdit.createFirstWOCheckbox.shouldBe('checked');
  pmTriggerPages.addEdit.createFirstWOCheckbox.shouldBeDisabled();
  cy.contains(
    'Create first work order cannot be edited after work order has been created',
  ).should('exist');

  pmTriggerPages.addEdit.priorityDropdownButton.click();
  pmTriggerPages.addEdit.listItem('Medium').click();

  pmTriggerPages.addEdit.categoryDropdownButton.click();
  pmTriggerPages.addEdit.dropdownListSearchInput.type('Damage');
  pmTriggerPages.addEdit.listItem('Damage').click();
  pmTriggerPages.addEdit.durationInput.type(1);
  pmTriggerPages.addEdit.requiresSignatureToggle.click();

  pmTriggerPages.addEdit.dropzoneInputs.get().first().selectFile(imageFile, {
    force: true,
  });

  pmTriggerPages.addEdit.dropzoneInputs.get().last().selectFile(imageFile, {
    force: true,
  });

  pmTriggerPages.addEdit.addFromSavedFilesBtn.click();
  pmTriggerPages.addEdit.selectFileItem(fileName).click();
  pmTriggerPages.addEdit.addFileButton.click();

  pmTriggerPages.addEdit.addPartsButton.scrollIntoView().click();
  pmTriggerPages.addEdit.partRowItem(partName).click();
  pmTriggerPages.addEdit.addPartButton.click();

  pmTriggerPages.addEdit.addTasksButton.click({
    scrollBehavior: 'bottom',
  });
  pmTriggerPages.addEdit
    .checklistTaskNameInputs('Misc')
    .get()
    .eq(0)
    .type(' update', {
      scrollBehavior: 'bottom',
    });
  pmTriggerPages.addEdit
    .checklistAddTasksButton('Misc')
    .click({ scrollBehavior: 'bottom' });
  pmTriggerPages.addEdit
    .checklistTaskNameInputs('Misc')
    .get({
      scrollBehavior: 'bottom',
    })
    .eq(1)
    .type(' 02', {
      scrollBehavior: 'bottom',
    });

  pmTriggerPages.addEdit.saveDetailsButton.click();
  cy.contains('PM updated').should('exist');
};

const verifyPMDetails = ({ pmName, partName, currentTime }) => {
  const time = currentTime.toLocaleString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  cy.contains(`EDIT!! ${pmName}`).should('exist');
  cy.get('.title-dropdown').contains('Medium').should('exist');
  cy.contains('Damage').should('exist');
  cy.contains('1 hours').should('exist');
  cy.get(`[data-cy="${partName}"]`).should('exist');

  cy.contains('Files');
  cy.contains('Misc');
  cy.contains('New Task update');
  cy.contains(`Today at ${time}`).should('exist');
};

const canEditPMTemplate = ({ fileName, imageFile }) => {
  it(
    'Can edit PM trigger',
    {
      testCaseId: 'QA-T6304',
    },
    () => {
      const now = Date.now();
      const pmName = `pm-${now}-name`;
      const pmWOTitle = `pm-wo-${now}-title`;
      const startDate = new Date(new Date().setDate(new Date().getDate() + 1));
      const partName = `part ${now}`;
      const locationName = `location ${now}`;
      const checklistName = `checklist ${now}`;
      const timezoneName = 'Asia/Kolkata';
      const currentTime = new Date();

      createPMTrigger({
        pmName,
        pmWOTitle,
        startDate,
        timezoneName,
      });

      h.uploadFile({ originalName: fileName });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).click();
      pmTriggerPages.details.schedulesTab.click();
      cy.url().should('contain', '/schedules');
      cy.contains('Every 1 day').should('exist');

      fillPMDetails({
        pmName,
        partName,
        checklistName,
        fileName,
        imageFile,
        locationName,
      });

      verifyPMDetails({
        pmName,
        partName,
        currentTime,
      });
    },
  );
};

export default canEditPMTemplate;
