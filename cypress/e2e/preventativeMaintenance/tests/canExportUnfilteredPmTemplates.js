import { createMeter } from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import { exportFilteredView } from '../helpers';

const canExportUnfilteredPMTemplates = () => {
  it(
    'can export unfiltered pm templates view',
    { testCaseId: 'QA-T1214' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const meter1Name = `meter1 ${now}`;
      const pmName = `pmExport${now}`;
      const pmName2 = `pm2Export${now}`;
      const downloadedPmFile = `${DOWNLOAD_LOCATION}/upkeep-pmtemplates.csv`;

      createMeter({ name: meter1Name }, true).then(({ body }) => {
        const meter = body.result;
        const schedules = [
          {
            meter: meter.id,
            meterCondition: 'every',
            meterConditionValue: 1,
            meterDueInterval: 1,
            meterDueFrequency: 'days',
            timeZone: 'Asia/Kolkata',
            startDate: new Date().toISOString(),
            triggerType: 'meter',
          },
        ];
        createBasicPmTemplate({
          name: pmName,
          priority: 2, // Medium
          schedules,
        });
        createBasicPmTemplate({
          name: pmName2,
          priority: 1, // Low
          schedules,
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerRow(pmName2).shouldBeVisible();

      exportFilteredView();
      cy.readFile(downloadedPmFile).then((content) => {
        expect(content).to.contain(pmName);
        expect(content).to.contain(pmName2);
      });
    },
  );
};

export default canExportUnfilteredPMTemplates;
