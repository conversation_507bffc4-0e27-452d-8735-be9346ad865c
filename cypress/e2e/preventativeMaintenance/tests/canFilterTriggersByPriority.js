import { createBasicPmTemplate } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import * as pmHelpers from '../helpers';

const canFilterTriggersByPriority = () => {
  it(
    'should filter pm templates by priority',
    {
      testCaseId: 'QA-T5785',
    },
    () => {
      const now = Date.now();
      const pmName = `PM asset filters-${now}`;
      const mainDescription = `pm work order name ${now}`;
      const pmTriggerToCreate = 4;

      for (let i = 0; i < pmTriggerToCreate; i++) {
        const pm = `${i} ${pmName}`;
        createBasicPmTemplate({
          mainDescription,
          priority: i,
          createFirstWO: false,
          name: pm,
        });
      }

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      // filter by priority
      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);

      pmTriggerPages.list
        .pmTriggerRowContains(`0 ${pmName}`, 'none')
        .shouldExist();
      pmTriggerPages.list
        .pmTriggerRowContains(`1 ${pmName}`, 'low')
        .shouldExist();
      pmTriggerPages.list
        .pmTriggerRowContains(`2 ${pmName}`, 'medium')
        .shouldExist();
      pmTriggerPages.list
        .pmTriggerRowContains(`3 ${pmName}`, 'high')
        .shouldExist();

      pmHelpers.filterPmByLowPriority();
      pmTriggerPages.list.pmTriggerRows(pmName).shouldHaveLength(1);
      pmTriggerPages.list.pmTriggerRowContains(pmName, 'low').shouldExist();

      pmHelpers.filterPmByMediumPriority();
      pmTriggerPages.list.pmTriggerRowContains(pmName, 'medium').shouldExist();

      pmHelpers.filterPmByHighPriority();
      pmTriggerPages.list.pmTriggerRowContains(pmName, 'high').shouldExist();
      pmTriggerPages.list.pmTriggerRowContains(pmName, 'none').shouldNotExist();

      // reset filters
      pmTriggerPages.list.resetFilterButton.click();
      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);
    },
  );
};

export default canFilterTriggersByPriority;
