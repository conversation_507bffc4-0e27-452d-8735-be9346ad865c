import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import * as pmHelpers from '../helpers';

const canFilterTriggerByCategory = () => {
  it(
    'should filter pm templates by category',
    {
      testCaseId: 'QA-T5800',
    },
    () => {
      const now = Date.now();
      const pmCategoryBaseName = `PM with Team ${now}`;
      const description = 'Filter preventive maintenance trigger by Category';
      const damage = 'Damage';
      const electrical = 'Electrical';
      const meterReading = 'Meter Reading';
      const categories = [damage, electrical, meterReading];

      categories.forEach((category) => {
        h.createBasicPmTemplate({
          name: `${pmCategoryBaseName} ${category}`,
          mainDescription: description,
          category,
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list
        .pmTriggerRows(pmCategoryBaseName)
        .shouldHaveLength(categories.length);

      // filter by category: Damage
      pmHelpers.filterPmByCategory(damage);
      pmTriggerPages.list
        .pmTriggerRows(`${pmCategoryBaseName} ${damage}`)
        .shouldBeVisible();
      pmTriggerPages.list
        .pmTriggerRows(`${pmCategoryBaseName} ${electrical}`)
        .shouldNotExist();

      // reset filters
      pmTriggerPages.list.resetFilterButton.click();
      pmTriggerPages.list
        .pmTriggerRows(pmCategoryBaseName)
        .shouldHaveLength(categories.length);

      // filter by category: Electrical
      pmHelpers.filterPmByCategory(electrical);
      pmTriggerPages.list
        .pmTriggerRows(`${pmCategoryBaseName} ${electrical}`)
        .shouldBeVisible();
      pmTriggerPages.list
        .pmTriggerRows(`${pmCategoryBaseName} ${meterReading}`)
        .shouldNotExist();
      pmTriggerPages.list
        .pmTriggerRows(`${pmCategoryBaseName} ${damage}`)
        .shouldNotExist();
    },
  );
};

export default canFilterTriggerByCategory;
