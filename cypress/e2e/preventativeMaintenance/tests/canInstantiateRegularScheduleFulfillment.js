import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as woPages from '../../workOrders/components';
import * as pmPages from '../components';
import * as pmHelpers from '../helpers';

const canInstantiateRegularScheduleFulfillment = () => {
  it(
    'Verify Repeat Regular PM trigger Work Orders',
    {
      testCaseId: 'QA-T6365',
    },
    () => {
      const now = Date.now();
      const pmName = `pm trigger test ${now}`;
      const mainDescription = `wo triggered ${now}`;

      h.createBasicPmTemplate({
        name: pmName,
        createFirstWO: false,
        mainDescription,
      }).then((pm) => {
        const [schedule] = pm.body.result.schedules || [];
        const [fulfillment] = schedule.fulfillments || [];
        pmHelpers.instantiatePmSchedule(fulfillment._id);
        cy.wait(300);
        cy.reload();
        woPages.list.workOrderByName(mainDescription).shouldHaveLength(1);
        cy.intercept('GET', `/api/v3/pm/${pm.body.result._id}/**`).as(
          'getPMSchedule',
        );

        upkeepPages.PREVENTIVE_MAINTENANCE.go();
        pmPages.list.pmTriggerRow(pmName).click();
        cy.wait('@getPMSchedule').then((intercept) => {
          // accessing the response body
          const [latestSchedule] = intercept.response.body.data;
          const [latestFulfillment] = latestSchedule.fulfillments || [];
          pmHelpers.instantiatePmSchedule(latestFulfillment._id);
          cy.wait(300);
          upkeepPages.WORK_ORDERS.go();
          cy.reload();
          woPages.list.workOrderByName(mainDescription).shouldHaveLength(2);
        });
      });
    },
  );
};

export default canInstantiateRegularScheduleFulfillment;
