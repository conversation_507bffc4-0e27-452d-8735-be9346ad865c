import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canResumeTrigger = () => {
  it(
    'Can resume a paused Regular PM trigger',
    { testCaseId: 'QA-T6302' },
    () => {
      const now = Date.now();
      const pmName = `pm resume test ${now}`;

      createBasicPmTemplate({
        name: pmName,
        mainDescription: pmName,
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerName(pmName).click();
      // pasue first
      pmTriggerPages.details.actionDropdown.click();
      pmTriggerPages.details.pauseResumeButton.click();
      pmTriggerPages.addEdit.confirmPauseButton.click();
      // resume
      pmTriggerPages.details.actionDropdown.click();
      pmTriggerPages.details.pauseResumeButton.click();
      pmTriggerPages.addEdit.confirmResumeButton.click();
      pmTriggerPages.details.backButton.click();
      cy.reload();
      pmTriggerPages.list
        .selectPMTrigger(pmName)
        .get()
        .within(() => {
          pmTriggerPages.list.scheduleDataCell('1').click({ force: true });
        });
      pmTriggerPages.list.nextDueDate.get().should('not.exist');
    },
  );
};

export default canResumeTrigger;
