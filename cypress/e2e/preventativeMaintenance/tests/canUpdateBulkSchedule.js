import { createNewUser } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmPages from '../components';
import { preparePMForBulkUpdate } from '../helpers';

const canUpdateBulkSchedules = () => {
  it('Can bulk update the schedules with existing schedule', () => {
    const now = Date.now();
    const pmName = `pm schedule bulk test ${now}`;
    preparePMForBulkUpdate(pmName);
    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.contains(pmName);
    pmPages.list.pmTriggerName(pmName).click();

    pmPages.details.schedulesTab.click();
    pmPages.addEdit.shceduleRowMenuItem('Every 1 day').shouldHaveLength(2);
    pmPages.addEdit.shceduleRowMenuItem('Every 2 days').shouldHaveLength(1);
    //  select the schedules to update
    pmPages.addEdit.selectShceduleRow('Every 2 days').click();
    pmPages.addEdit.selectShceduleRow('Every 3 days').click();
    cy.contains('Update Schedule');
    pmPages.addEdit.bulkUpdateSchedulesBtn.click();
    pmPages.addEdit.bulkUpdateSchedulesAddBtn.should('be.visible');

    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 1 day')
      .shouldHaveLength(1);
    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 2 days')
      .shouldHaveLength(1);
    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 3 days')
      .shouldHaveLength(1);
    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 1 day')
      .click();
    pmPages.addEdit.bulkUpdateSchedulesUpdateBtn.click({ force: true });
    cy.wait(100);
    pmPages.addEdit.shceduleRowMenuItem('Every 1 day').shouldHaveLength(4);
    pmPages.addEdit.shceduleRowMenuItem('Every 2 days').shouldHaveLength(0);
    pmPages.addEdit.shceduleRowMenuItem('Every 3 days').shouldHaveLength(0);

    pmPages.addEdit.selectShceduleRow('Every 1 day').click();
    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 2 days')
      .shouldHaveLength(0);
    pmPages.addEdit
      .bulkUpdateScheduleExistingSchedule('Due every 3 days')
      .shouldHaveLength(0);
  });

  it('Can bulk update the schedules with new schedule', () => {
    const now = Date.now();
    const pmName = `pm schedule bulk test ${now}`;
    preparePMForBulkUpdate(pmName);
    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.contains(pmName);
    pmPages.list.pmTriggerName(pmName).click();

    pmPages.details.schedulesTab.click();
    pmPages.addEdit.shceduleRowMenuItem('Every 1 day').shouldHaveLength(2);
    pmPages.addEdit.shceduleRowMenuItem('Every 2 days').shouldHaveLength(1);
    //  select the schedules to update
    pmPages.addEdit.selectShceduleRow('Every 2 days').click();
    pmPages.addEdit.selectShceduleRow('Every 3 days').click();
    cy.contains('Update Schedule');
    pmPages.addEdit.bulkUpdateSchedulesBtn.click();
    pmPages.addEdit.bulkUpdateSchedulesAddBtn.should('be.visible');
    pmPages.addEdit.bulkUpdateSchedulesAddBtn.click();

    // typing 2 will make the text 21 beacuse of min=1 attribute
    pmPages.addEdit.repeatIntervalInput.type(`{selectall}${21}`);
    pmPages.addEdit.repeatFreqDropdownButton.click();
    pmPages.addEdit.listItem('Month(s)').click();

    pmPages.addEdit.monthdaysDropdownButton.click();
    pmPages.addEdit.listItem('3').click();

    pmPages.addEdit.cadenceIntervalInput.type(`{selectall}${2}`);
    pmPages.addEdit.doneButton.click();

    cy.wait(100);
    pmPages.addEdit.bulkUpdateSchedulesUpdateBtn.click({ force: true });

    pmPages.addEdit
      .scheduleFreqEveryNMonthsText('Every 21 months')
      .shouldHaveLength(2);
  });

  it('Can bulk update the schedules with Assign to', () => {
    const now = Date.now();
    const pmName = `pm schedule bulk test ${now}`;
    const data = {
      firstName: `Test`,
      lastName: `User ${now}`,
      jobTitle: 'Tester',
      hourlyRate: '45',
      phoneNumber: '(*************',
      email: `engineering-test_admin_${now}@dummy.com`,
    };
    createNewUser(data);
    preparePMForBulkUpdate(pmName);

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.contains(pmName);
    pmPages.list.pmTriggerName(pmName).click();
    pmPages.details.schedulesTab.click();
    pmPages.addEdit.shceduleRowMenuItem('Every 1 day').shouldHaveLength(2);

    pmPages.addEdit.selectShceduleRow('Every 2 days').click();
    pmPages.addEdit.selectShceduleRow('Every 3 days').click();
    cy.contains('Update Assignee');
    pmPages.addEdit.bulkUpdateSchedulesAssigneeBtn.click();
    pmPages.addEdit.bulkUpdateSchedulesPrimaryWorkerBtn.click();
    pmPages.addEdit.assigneeWorkersDropdown.click();
    pmPages.addEdit.listItem(`${data.firstName} ${data.lastName}`).click();
    pmPages.addEdit.doneButton.click();

    cy.contains('Update 2 Schedules?');
    pmPages.addEdit.bulkUpdateSchedulesUpdateBtn.click({ force: true });

    cy.wait(100);
    cy.contains(`${data.firstName} ${data.lastName}`);
  });
};

export default canUpdateBulkSchedules;
