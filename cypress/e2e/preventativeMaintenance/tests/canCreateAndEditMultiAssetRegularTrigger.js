import { createAsset } from '../../../helpers';
import * as pmTriggerPages from '../components';

const canCreateAndEditMultiAssetRegularTrigger = () => {
  it(
    'Can create and edit Multi Asset Regular PM trigger',
    { testCaseId: 'QA-T6292' },
    () => {
      const now = Date.now();
      const pmName = `PM Multi asset on Regular-${now}`;
      const pmWOTitle = `PM Multi asset on Regular details-${now}`;
      const pmDesc = 'PM Multi asset on Regular desc';
      const assetName = `asset1 ${now}`;
      const secondAssetName = `Asset 2 ${now}`;
      createAsset({ Name: assetName }, true);
      createAsset({ Name: secondAssetName }, true);

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.list.optionMultiple.click();

      pmTriggerPages.addEdit.triggerNameInput.type(pmName);
      pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

      pmTriggerPages.addEdit.assignAssetButton.click();
      pmTriggerPages.addEdit.chooseAssetsHeaderButton.select(assetName);
      pmTriggerPages.addEdit.chooseAssetsFooterButton.click();
      pmTriggerPages.addEdit.manageAssetsDoneButton.click();

      pmTriggerPages.addEdit.createButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.url().should('contain', '/schedules');
      cy.contains('Every 1 day');

      pmTriggerPages.details.detailsTab.click();
      pmTriggerPages.details.editButton.click();
      pmTriggerPages.addEdit.assignAssetButton.click();
      pmTriggerPages.addEdit.chooseAssetsHeaderButton.select(secondAssetName);
      pmTriggerPages.addEdit.chooseAssetsFooterButton.click();
      pmTriggerPages.addEdit.manageAssetsDoneButton.click();
      pmTriggerPages.addEdit.saveButton.click();
      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.url().should('contain', '/schedules');
    },
  );
};

export default canCreateAndEditMultiAssetRegularTrigger;
