import * as h from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import { addSchedulesToPM } from '../helpers';

const canAddCalendarSchedule = () => {
  it(
    'can edit pm and add calendar schedule',
    {
      testCaseId: 'QA-T6304',
    },
    () => {
      const now = Date.now();
      const assetName = `pm-asset-${now}`;
      const locationName = `pm-location-${now}`;
      const pmName = `${now}-name`;
      const pmWOTitle = `${now}-title`;

      h.createAsset({ Name: assetName }, true);
      h.createLocation({ stringName: locationName }, true);
      createBasicPmTemplate({
        mainDescription: pmWOTitle,
        priority: 0,
        name: pmName,
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).click();

      pmTriggerPages.details.schedulesTab.click();
      cy.url().should('contain', '/schedules');
      cy.contains('Every 1 day').should('exist');

      // Clicks on Add Asset or Add schedules
      addSchedulesToPM();

      // Clicks on add schedules adding calendar schedule
      pmTriggerPages.addEdit.addScheduleButton.click();

      // Clicking on calendar schedule schedule Type method takes meter and calendarMeter or meterReadings as well default is calendar
      pmTriggerPages.addEdit.scheduleType().click();

      // typing 2 will make the text 21 beacuse of min=1 attribute
      pmTriggerPages.addEdit.repeatIntervalInput.type(`{selectall}${21}`);
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem('Month(s)').click();

      pmTriggerPages.addEdit.monthdaysDropdownButton.click();
      pmTriggerPages.addEdit.listItem('3').click();

      pmTriggerPages.addEdit.cadenceIntervalInput.type(`{selectall}${2}`);
      pmTriggerPages.addEdit.doneButton.click();

      // Adding Asset
      pmTriggerPages.addEdit.modalAssetDropdownButton.click();
      pmTriggerPages.addEdit.listItem(assetName).scrollIntoView().click();

      cy.wait(100);

      // Adding Location
      pmTriggerPages.addEdit.modalLocationDropdownButton.click();
      pmTriggerPages.addEdit.listItem(locationName).scrollIntoView().click();

      // save the changes
      pmTriggerPages.addEdit.saveDetailsButton.click();

      pmTriggerPages.addEdit
        .scheduleFreqEveryNMonthsText('Every 21 months')
        .should('exist');
      pmTriggerPages.addEdit.scheduleFreqOnDayText('On day 3').should('exist');
      cy.contains(assetName).should('exist');
      cy.contains(locationName).should('exist');
    },
  );
};

export default canAddCalendarSchedule;
