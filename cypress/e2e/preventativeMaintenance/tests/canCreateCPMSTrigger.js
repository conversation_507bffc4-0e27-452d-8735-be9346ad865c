import * as pmTriggerPages from '../components';
import * as h from '../../../helpers';

const canCreateCPMSTrigger = () => {
  it('Can create CPMS trigger', { testCaseId: 'QA-T6303' }, () => {
    const now = Date.now();
    const meterName = `pm-meter-create ${now}`;
    const pmName = `${now}-name`;
    const pmWOTitle = `${now}-title`;
    const pmDesc = 'PM Description asset on completion desc';
    h.createMeter({ name: meterName }, true);

    pmTriggerPages.list.createTriggerButton.click();
    pmTriggerPages.list.optionNoneOrOne.click();

    pmTriggerPages.addEdit.editTimeBasedTriggerButton.click();
    pmTriggerPages.addEdit.editTimeDoneButton.click();
    pmTriggerPages.addEdit.scheduleText.shouldContain(
      'Time:Due every 1 day, created 1 day before due date',
    );
    pmTriggerPages.addEdit.editMeterBasedTriggerButton.click();
    pmTriggerPages.addEdit.selectMeterDropdown.select(meterName);
    pmTriggerPages.addEdit.editUsageDoneButton.click();
    pmTriggerPages.addEdit.usageText.shouldContain(
      `Usage:Created when meter ${meterName} reaches every 1 Miles, due 1 day after creation`,
    );

    pmTriggerPages.addEdit.triggerNameInput.type(pmName);
    pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
    pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

    pmTriggerPages.addEdit.createButton.click();

    pmTriggerPages.list.pmTriggerName(pmName).click();
    cy.url().should('contain', '/schedules');
    cy.contains('Every 1 day');
    cy.contains('Every 1 Miles');
  });
};

export default canCreateCPMSTrigger;
