import * as pmTriggerPages from '../components';
import { createMeter, createBasicPMTrigger } from '../../../helpers';

const canEditMeterBasedTrigger = () => {
  it('Can edit meter based trigger', { testCaseId: 'QA-T6291' }, () => {
    const now = Date.now();
    const meter1Name = `meter1 ${now}`;
    const meter2Name = `meter2 ${now}`;
    const pmName = `pm ${now}`;

    createMeter({ name: meter1Name }, true).then(({ body }) => {
      const meter = body.result;
      createBasicPMTrigger({
        name: pmName,
        meter: meter.id,
        meterCondition: 'every',
        meterConditionValue: 1,
        meterDueInterval: 1,
        meterDueFrequency: 'days',
      });
    });
    createMeter({ name: meter2Name }, true);
    cy.reload();
    pmTriggerPages.list.selectPMTrigger(pmName).shouldBeVisible();
    pmTriggerPages.list.pmTriggerRow(pmName).click({ force: true });
    pmTriggerPages.details.detailsTab.click();
    pmTriggerPages.details.editButton.click();

    pmTriggerPages.addEdit.editMeterTrigger.click();
    pmTriggerPages.addEdit.editUsageDoneButton.click();
    pmTriggerPages.addEdit.editMeterTrigger.click();
    pmTriggerPages.addEdit.selectMeterDropdownInput.type(meter2Name);
    cy.wait(1500);
    pmTriggerPages.addEdit.selectMeterDropdown.select(meter2Name);
    pmTriggerPages.addEdit.selectMeterConditionDropdown.select(
      'Is greater than',
    );
    pmTriggerPages.addEdit.meterInterval.type(20);
    pmTriggerPages.addEdit.meterDueInterval.type(2);
    pmTriggerPages.addEdit.meterDueFrequency.select('Week(s)');

    pmTriggerPages.addEdit.editUsageDoneButton.click();
    pmTriggerPages.addEdit.saveButton.click();

    cy.contains('Trigger updated successfully');
  });
};

export default canEditMeterBasedTrigger;
