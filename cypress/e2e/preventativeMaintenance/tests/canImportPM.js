import { DateTime } from 'luxon';
import { createAsset, createMeter } from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { startDromoImport } from '../../../helpers/dromoImportHelpers';
import { upkeepPages } from '../../../support/constants';
import * as pmPages from '../components';
import * as pmHelpers from '../helpers';

const canImportPM = () => {
  const csvFile = 'pm-single-step-import.csv';
  const now = Date.now();
  const pmName = `PM Template ${now}`;
  const woTitle = `PM Template WO ${now}`;
  const woDescription = `PM Template WO Description ${now}`;
  const priority = 1;
  const sDate = DateTime.now().plus({ days: 1 }).toISO();
  const startDate = DateTime.fromISO(sDate).toFormat('LL/dd/yyyy hh:mm:ss');
  const timeZone = 'Asia/Kolkata';
  const meterName = `meter1 ${now}`;
  const assetName = `asset1 ${now}`;

  it(
    'should create pm with schedules via import',
    {
      testCaseId: 'QA-T5444',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      createAsset({ Name: assetName }, true).then(({ body: assetBody }) => {
        createMeter({ name: meterName }, true).then(({ body: meterBody }) => {
          const testData = [
            {
              pmName,
              woTitle,
              woDescription,
              priority,
              scheduleType: 'calendar',
              cadenceFreq: 'DAILY',
              cadenceInterval: 1,
              cadenceType: 'manual',
              isBasedOnCompletion: false,
              repeatFrequency: 'DAILY',
              repeatInterval: 1,
              startDate,
              timeZone,
              asset: assetBody.results.id,
              dueTime: '9:00',
              triggerTime: '14:00',
            },
            {
              pmName,
              woTitle,
              woDescription,
              priority,
              scheduleType: 'meter',
              // fomatting date to test the date format without leading zero
              startDate: DateTime.fromISO(sDate).toFormat('L/d/yyyy'),
              timeZone,
              meter: meterBody.result.id,
              meterCondition: 'Every',
              meterConditionValue: 3,
              meterDueFrequency: 'Hours',
              meterDueInterval: 2,
            },
          ];

          // Create csv
          const csvData = pmHelpers.createSingleStepPMCsvData(testData);
          cy.writeFile(
            `cypress/fixtures/${csvFile}`,
            csvData.replaceAll('undefined', ''),
          );

          // Begin import
          upkeepPages.PREVENTIVE_MAINTENANCE.go();
          pmHelpers.importSingleStepPM(csvFile);
          startDromoImport();
          pmHelpers.verifyImportSingleStepPMCreateSuccess(1, 0, 2, 0);
        });
      });
    },
  );

  it('should update pm with schedules via import', () => {
    Cypress.on('uncaught:exception', () => false);

    createBasicPmTemplate().then(({ body }) => {
      const testData = [
        {
          id: body.result._id,
          pmName: `EDIT!! ${pmName}`,
          woTitle,
          woDescription,
          priority,
        },
      ];

      // Create csv
      const csvData = pmHelpers.createSingleStepPMCsvData(testData);
      cy.writeFile(
        `cypress/fixtures/${csvFile}`,
        csvData.replaceAll('undefined', ''),
      );
    });

    // Begin import
    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmHelpers.importSingleStepPM(csvFile);
    startDromoImport();
    pmHelpers.verifyImportSingleStepPMCreateSuccess(0, 1, 0, 0);

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.reload();
    pmPages.list.pmTriggerRow(`EDIT!! ${pmName}`).shouldBeVisible();
  });
};

export default canImportPM;
