import { DateTime } from 'luxon';
import { createMeter } from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import { exportPMWithSchedules } from '../helpers';

const canExportUnfilteredPM = () => {
  it(
    'can export unfilted pm',
    {
      testCaseId: 'QA-T1214',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const meter1Name = `meter1 ${now}`;
      const pmName = `pmExport${now}`;
      const pmName2 = `pm2Export${now}`;
      const startDate = DateTime.now().plus({ days: 1 }).toUTC();
      const timeZone = 'UTC';
      const downloadedPmFile = `${DOWNLOAD_LOCATION}/upkeep-preventive-maintenances.csv`;

      createMeter({ name: meter1Name }, true).then(({ body }) => {
        const meter = body.result;
        createBasicPmTemplate({
          name: pmName,
          priority: 2,
          schedules: [
            {
              meter: meter.id,
              meterCondition: 'every',
              meterConditionValue: 1,
              meterDueInterval: 1,
              meterDueFrequency: 'days',
              timeZone: 'Asia/Kolkata',
              startDate: new Date().toISOString(),
              triggerType: 'meter',
            },
          ],
        });
        createBasicPmTemplate({
          name: pmName2,
          priority: 1,
          schedules: [
            {
              cadenceFreq: 'DAILY',
              cadenceInterval: 1,
              cadenceType: 'manual',
              isBasedOnCompletion: false,
              repeatFrequency: 'DAILY',
              repeatInterval: 1,
              scheduleType: 'EVERY_N_DAYS',
              startDate,
              timeZone,
              triggerType: 'calendar',
            },
          ],
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerRow(pmName2).shouldBeVisible();

      exportPMWithSchedules();
      cy.readFile(downloadedPmFile).then((content) => {
        expect(content).to.contain(pmName);
        expect(content).to.contain(pmName2);
        expect(content).to.contain('calendar');
        expect(content).to.contain('meter');
        expect(content).to.contain('every');
      });
    },
  );
};

export default canExportUnfilteredPM;
