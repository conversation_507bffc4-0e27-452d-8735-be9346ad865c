import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmHelpers from '../helpers';

const canFilterTriggersByTeam = () => {
  it(
    'should filter pm templates by team',
    {
      testCaseId: 'QA-T5799',
    },
    () => {
      const now = Date.now();
      const teamName = `Team ${now}`;
      const pmNameWithTeam = `PM with Team ${now}`;
      const pmNameWithoutTeam = `PM without Team ${now}`;
      const description = 'Filter preventive maintenance trigger by Team';

      // Get user/session token
      const userToken = JSON.parse(window.localStorage.getItem('currentUser'));
      const { sessionToken } = userToken;

      // Creates team and adds teamId to a PM Trigger
      cy.createTeam(teamName, sessionToken).then((res) => {
        const teamId = res.result.id;

        // Create 2 PM Triggers;
        // 1 with team, 1 without team
        h.createBasicPmTemplate({
          name: pmNameWithTeam,
          mainDescription: description,
          schedules: [h.basicCalendarSchedule(undefined, { team: teamId })],
        });

        h.createBasicPmTemplate({ name: pmNameWithoutTeam });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmHelpers.filterPmByTeam(teamName);
      pmHelpers.verifyPmFilteredByTeam(pmNameWithTeam, pmNameWithoutTeam);
    },
  );
};

export default canFilterTriggersByTeam;
