import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canPauseTrigger = () => {
  it('Can pause a Regular PM trigger', { testCaseId: 'QA-T6301' }, () => {
    const now = Date.now();
    const pmName = `pm pause test ${now}`;
    createBasicPmTemplate({
      name: pmName,
      mainDescription: pmName,
    });

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmTriggerPages.list.pmTriggerName(pmName).click();
    pmTriggerPages.details.actionDropdown.click();
    pmTriggerPages.details.pauseResumeButton.click();
    pmTriggerPages.addEdit.confirmPauseButton.click();
    pmTriggerPages.details.backButton.click();
    cy.reload();
    pmTriggerPages.list
      .selectPMTrigger(pmName)
      .get()
      .within(() => {
        pmTriggerPages.list.scheduleDataCell('1').click({ force: true });
      });
    pmTriggerPages.list.nextDueDate.get().contains('Paused');
  });
};

export default canPauseTrigger;
