import { createBasicPmTemplate, uploadFile } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import * as pmHelpers from '../helpers';
import { filterPmByFile } from '../helpers';

const canFilterTriggersByFiles = () => {
  it(
    'should filter pm templates by files',
    {
      testCaseId: 'QA-T5801',
    },
    () => {
      const now = Date.now();
      const pmName = `PM file filters-${now}`;
      const woName = `WO file filters-${now}`;
      const pmTriggerToCreate = 2;
      const testFiles = ['test-file.txt', 'example.json'];

      for (let i = 0; i < pmTriggerToCreate; i++) {
        const pm = `${i} ${pmName}`;
        const file = testFiles[i];
        uploadFile({ originalName: file });

        createBasicPmTemplate({
          name: pm,
          mainDescription: woName,
        });
        upkeepPages.PREVENTIVE_MAINTENANCE.go();
        if (i > 0) cy.reload();

        // assign file to pm
        pmTriggerPages.list.pmTriggerRow(pm).click();
        pmTriggerPages.addEdit.editDetailsButton.click();

        pmHelpers.selectFileAddEdit(file);
        pmTriggerPages.addEdit.saveDetailsButton.click();
      }

      // filter by file
      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      cy.reload();
      const file1 = testFiles[0];
      const pm1 = `0 ${pmName}`;
      const pm2 = `1 ${pmName}`;
      pmTriggerPages.list.pmTriggerRows(pmName).shouldHaveLength(2);
      filterPmByFile(file1);
      cy.wait(1000);
      pmTriggerPages.list.pmTriggerRow(pm1).shouldHaveLength(1);
      pmTriggerPages.list.pmTriggerRow(pm2).shouldNotExist();
      pmTriggerPages.list.pmTriggerRow(pm1).click();
      pmTriggerPages.details.detailsTab.click();
      cy.contains('Files');
      cy.contains(file1);

      pmTriggerPages.details.backButton.click();
      pmTriggerPages.list.resetFilterButton.click();

      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);
    },
  );
};

export default canFilterTriggersByFiles;
