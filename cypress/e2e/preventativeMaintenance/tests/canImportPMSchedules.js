import { DateTime } from 'luxon';
import { upkeepPages } from '../../../support/constants';
import { startDromoImport } from '../../../helpers/dromoImportHelpers';
import * as pmHelpers from '../helpers';
import * as pmPages from '../components';
import {
  createAsset,
  createBasicPmTemplate,
  createLocation,
  createMeter,
} from '../../../helpers';

const canImportPMSchedules = () => {
  it(
    'should create pm schedules via import',
    { testCaseId: 'QA-T6385' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const csvFile = 'pmschedules-import.csv';
      const now = Date.now();
      const sDate = DateTime.now().plus({ days: 1 }).toISO();
      const startDate = DateTime.fromISO(sDate).toFormat('LL/dd/yyyy hh:mm:ss');
      const timeZone = 'Asia/Kolkata';
      const meterName = `meter1 ${now}`;
      const assetName = `asset1 ${now}`;
      const pmName = `pmSchedulesImport${now}`;

      createLocation().then(({ body: locationBody }) => {
        createAsset({ Name: assetName }, true).then(({ body: assetBody }) => {
          createMeter({ name: meterName }, true).then(({ body: meterBody }) => {
            createBasicPmTemplate({
              name: pmName,
              schedules: [
                {
                  cadenceFreq: 'DAILY',
                  cadenceInterval: 1,
                  cadenceType: 'manual',
                  isBasedOnCompletion: false,
                  repeatFrequency: 'DAILY',
                  repeatInterval: 1,
                  scheduleType: 'EVERY_N_DAYS',
                  startDate,
                  timeZone,
                  triggerType: 'calendar',
                },
              ],
            }).then(({ body: pmBody }) => {
              // Create csv
              const csvData = pmHelpers.createScheduleCsvData([
                {
                  pmTemplate: pmBody.result._id,
                  type: 'Calendar',
                  startDate,
                  timeZone,
                  repeatInterval: 1,
                  repeatFrequency: 'Week(s)',
                  cadenceInterval: 2,
                  cadenceFreq: 'Day(s)',
                  isBasedOnCompletion: 'YES',
                  asset: assetBody.results.id,
                },
                {
                  pmTemplate: pmBody.result._id,
                  type: 'Calendar',
                  startDate,
                  timeZone,
                  repeatInterval: 1,
                  repeatFrequency: 'Month(s) by position of month',
                  weekdays: 'MO',
                  monthdays: 'First',
                  cadenceInterval: 2,
                  cadenceFreq: 'Day(s)',
                  isBasedOnCompletion: 'NO',
                },
                {
                  pmTemplate: pmBody.result._id,
                  type: 'Meter',
                  startDate,
                  timeZone,
                  meter: meterBody.result.id,
                  meterCondition: 'Every',
                  meterConditionValue: 3,
                  meterDueFrequency: 'Hours',
                  meterDueInterval: 2,
                  asset: assetBody.results.id,
                  location: locationBody.result.id,
                },
              ]);
              cy.writeFile(`cypress/fixtures/${csvFile}`, csvData);

              // Begin import
              upkeepPages.PREVENTIVE_MAINTENANCE.go();

              pmPages.list.pmTriggerName(pmName).click();
              pmHelpers.importPMSchedules(csvFile);
              startDromoImport(true);
              pmHelpers.verifyImportSchedulesCreateSucces(3, 0);
            });
          });
        });
      });
    },
  );

  it(
    'should update a pm schedule via import',
    { testCaseId: 'QA-T6386' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const csvFile = 'pmschedules-import.csv';
      const now = Date.now();
      const sDate = DateTime.now().plus({ days: 1 }).toISO();
      const startDate = DateTime.fromISO(sDate).toFormat('LL/dd/yyyy hh:mm:ss');
      const timeZone = 'Asia/Kolkata';
      const assetName = `asset1 ${now}`;
      const pmName = `pmSchedulesImport${now}`;

      createAsset({ Name: assetName }, true).then(({ body: assetBody }) => {
        createBasicPmTemplate({
          name: pmName,
          schedules: [
            {
              cadenceFreq: 'DAILY',
              cadenceInterval: 1,
              cadenceType: 'manual',
              isBasedOnCompletion: false,
              repeatFrequency: 'DAILY',
              repeatInterval: 1,
              scheduleType: 'EVERY_N_DAYS',
              startDate,
              timeZone,
              triggerType: 'calendar',
            },
          ],
        }).then(({ body: pmBody }) => {
          upkeepPages.PREVENTIVE_MAINTENANCE.go();
          pmPages.list.pmTriggerName(pmName).click();
          // Create csv
          const csvData = pmHelpers.createScheduleCsvData([
            {
              id: pmBody.result?.schedules[0]?._id,
              type: 'Calendar',
              startDate,
              timeZone,
              repeatInterval: 1,
              repeatFrequency: 'Month(s)',
              cadenceInterval: 3,
              cadenceFreq: 'Week(s)',
              asset: assetBody.results.id,
            },
            {
              pmTemplate: pmBody.result._id,
              type: 'Calendar',
              startDate,
              timeZone,
              repeatInterval: 3,
              repeatFrequency: 'Week(s) by day of week',
              weekdays: '"TU,TH"',
              cadenceInterval: 1,
              cadenceFreq: 'Week(s)',
              isBasedOnCompletion: 'NO',
            },
          ]);
          cy.writeFile(`cypress/fixtures/${csvFile}`, csvData);
          pmHelpers.importPMSchedules(csvFile);
          startDromoImport(true);
          pmHelpers.verifyImportSchedulesCreateSucces(1, 1);
        });
      });
    },
  );
};

export default canImportPMSchedules;
