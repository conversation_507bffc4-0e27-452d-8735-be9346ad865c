import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmHelpers from '../helpers';

const canFilterTriggersByLocation = () => {
  it(
    'should filter pm templates by location',
    {
      testCaseId: 'QA-T5784',
    },
    () => {
      const now = Date.now();
      const pmWithLocation = `PM LOCATION A ${now}`;
      const pmNoLocation = `PM NO LOCATION ${now}`;
      const locationName = `Location ${now}`;

      // Create 2 different PM Triggers;
      // 1 with location, 1 without location
      h.createLocation({ stringName: locationName }).then((res) => {
        const locationId = res.body.result.id;

        // Create basic PM trigger with location
        h.createBasicPmTemplate({
          name: pmWithLocation,
          mainDescription: pmWithLocation,
          schedules: [
            h.basicCalendarSchedule(undefined, { location: locationId }),
          ],
        });

        // Create basic PM trigger without location
        h.createBasicPmTemplate({
          name: pmNoLocation,
          mainDescription: pmNoLocation,
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmHelpers.filterPmByLocation(locationName, true);
      pmHelpers.verifyPmFiltered(pmWithLocation, pmNoLocation);
    },
  );
};

export default canFilterTriggersByLocation;
