import * as pmTriggerPages from '../components';
import { createPrequests } from '../helpers';
import { uploadFile } from '../../../helpers';
import * as pmHelpers from '../helpers';

const canCreateRegularScheduledTrigger = ({ teamName, fileName }) => {
  it(
    'Can create PM Trigger based on Regular Repeat Instant',
    { testCaseId: 'QA-T6308' },
    () => {
      const now = Date.now();
      const assetName = `asset ${now}`;
      const checklistName = `checklist ${now}`;
      const locationName = `location ${now}`;
      const pmDesc = 'PM Description regular repeat';
      const pmName = `${now}-name`;
      const pmWOTitle = `${now}-title`;
      createPrequests({
        asset: assetName,
        checklist: checklistName,
        location: locationName,
      });
      uploadFile({ originalName: fileName });

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.list.optionNoneOrOne.click();

      pmTriggerPages.addEdit.editTimeBasedTriggerButton.click();
      pmTriggerPages.addEdit.repeatTypeDropdown.select('Regularly');
      pmTriggerPages.addEdit.createFirstWorkOrderImmediately.click();
      pmTriggerPages.addEdit.editTimeDoneButton.click();
      pmTriggerPages.addEdit.scheduleText.shouldContain(
        'Time:Due every 1 day, created 1 day before due date',
      );

      pmTriggerPages.addEdit.triggerNameInput.type(pmName);
      pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

      pmTriggerPages.addEdit.assignAssetButton.click();
      pmTriggerPages.addEdit.assignAssetSearchInput.type(assetName);
      pmTriggerPages.addEdit.assignAssetItem(assetName).click();
      pmTriggerPages.addEdit.confirmAssetSelectionButton.click();

      pmTriggerPages.addEdit.assignLocationButton.select(locationName, {
        scrollBehavior: 'bottom',
      });
      pmTriggerPages.addEdit.confirmLocationSelectionButton.click();

      pmTriggerPages.addEdit.assignedToDropdown.select('Technician');
      pmTriggerPages.addEdit.additionalWorkersDropdown.select(
        'Limited Administrator',
      );

      pmTriggerPages.addEdit.teamDropdown.nestedSelect([
        '.react-select__menu',
        teamName,
      ]);

      pmTriggerPages.addEdit.addChecklistsButton.select(checklistName);
      pmTriggerPages.addEdit.confirmChecklistsButton.click();
      pmTriggerPages.addEdit.confirmAddTasksButton.click();

      pmHelpers.selectFileFromModal(fileName);
      pmTriggerPages.addEdit.confirmFilesButton.click();

      pmTriggerPages.addEdit.createButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 1 day');

      pmTriggerPages.details.detailsTab.click();
      cy.contains(pmName);
      cy.contains(pmDesc);
      cy.contains('Activity');
    },
  );

  it(
    'Can create PM Trigger based on Regular Repeat for Future date',
    { testCaseId: 'QA-T6309' },
    () => {
      const now = Date.now();
      const assetName = `asset ${now}`;
      const checklistName = `checklist ${now}`;
      const locationName = `location ${now}`;
      const pmDesc = 'PM Description regular repeat for future date';
      const pmName = `${now}-name`;
      const pmWOTitle = `${now}-title`;
      createPrequests({
        asset: assetName,
        checklist: checklistName,
        location: locationName,
      });
      uploadFile({ originalName: fileName });

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.list.optionNoneOrOne.click();

      pmTriggerPages.addEdit.editTimeBasedTriggerButton.click();
      pmTriggerPages.addEdit.repeatTypeDropdown.select('Regularly');
      pmTriggerPages.addEdit.editTimeDoneButton.click();
      pmTriggerPages.addEdit.scheduleText.shouldContain(
        'Time:Due every 1 day, created 1 day before due date',
      );

      pmTriggerPages.addEdit.triggerNameInput.type(pmName);
      pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

      pmTriggerPages.addEdit.assignAssetButton.click();
      pmTriggerPages.addEdit.assignAssetSearchInput.type(assetName);
      pmTriggerPages.addEdit.assignAssetItem(assetName).click();
      pmTriggerPages.addEdit.confirmAssetSelectionButton.click();

      pmTriggerPages.addEdit.assignLocationButton.select(locationName);
      pmTriggerPages.addEdit.confirmLocationSelectionButton.click();

      pmTriggerPages.addEdit.assignedToDropdown.select('Technician');
      pmTriggerPages.addEdit.additionalWorkersDropdown.select(
        'Limited Administrator',
      );

      pmTriggerPages.addEdit.teamDropdown.nestedSelect([
        '.react-select__menu',
        teamName,
      ]);

      pmTriggerPages.addEdit.addChecklistsButton.select(checklistName);
      pmTriggerPages.addEdit.confirmChecklistsButton.click();
      pmTriggerPages.addEdit.confirmAddTasksButton.click();

      pmHelpers.selectFileFromModal(fileName);
      pmTriggerPages.addEdit.confirmFilesButton.click();

      pmTriggerPages.addEdit.createButton.click();

      pmTriggerPages.addEdit.scheduleText.shouldContain(
        'Due every 1 day, created 1 day before due date',
      );
      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 1 day');

      pmTriggerPages.details.detailsTab.click();
      cy.contains(pmName);
      cy.contains(pmDesc);
      cy.contains('Activity');
    },
  );
};

export default canCreateRegularScheduledTrigger;
