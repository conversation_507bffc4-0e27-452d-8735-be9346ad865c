import * as pmTriggerPages from '../components';
import { createMeter } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as meterHelpers from '../../meters/helpers/metersHelpers';

const canCreateMeterBasedTrigger = () => {
  it(
    'Can create meter based trigger',
    {
      testCaseId: 'QA-T6290',
      featureFlags: { pmHealthPhase1: true },
    },
    () => {
      pmTriggerPages.list.createTriggerButton.shouldBeVisible();

      const now = Date.now();
      const pmName = `name-${now}`;
      const pmWOTitle = `title-${now}`;
      const meterName = `meter-${now}`;
      createMeter({ name: meterName }, true);

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.list.optionNoneOrOne.click();

      pmTriggerPages.addEdit.triggerNameInput.type(pmName);
      pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
      pmTriggerPages.addEdit.editMeterBasedTriggerButton.click();
      pmTriggerPages.addEdit.selectMeterDropdown.select(meterName);
      pmTriggerPages.addEdit.selectMeterConditionDropdown.select(
        'Reaches every',
      );
      pmTriggerPages.addEdit.meterInterval.type(10);
      pmTriggerPages.addEdit.meterDueInterval.type(1);
      pmTriggerPages.addEdit.meterDueFrequency.select('Day(s)');

      pmTriggerPages.addEdit.editUsageDoneButton.click();
      pmTriggerPages.addEdit.createButton.click();
      cy.contains('Trigger created successfully');

      pmTriggerPages.list.selectPMTrigger(pmName).shouldContain('-');
      upkeepPages.METERS.go();
      cy.contains(meterName).click();
      meterHelpers.addMeterReading(100);
      meterHelpers.verifyLastMeterReading(100);
    },
  );
};

export default canCreateMeterBasedTrigger;
