import { DateTime } from 'luxon';
import * as pmTriggerPages from '../components';
import { upkeepPages, DOWNLOAD_LOCATION } from '../../../support/constants';
import { createBasicPmTemplate } from '../../../helpers';

const canDownloadPMSchedulesTemplate = () => {
  const timeZone = 'UTC';
  it(
    'can download PM schedule template file',
    { testCaseId: 'QA-T6388' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const downloadedTemplate = `${DOWNLOAD_LOCATION}/pmschedules-sample.csv`;
      const now = Date.now();
      const startDate = DateTime.now().plus({ days: 1 }).toUTC();
      const pmName = `download template ${now}`;

      createBasicPmTemplate({
        name: pmName,
        schedules: [
          {
            cadenceFreq: 'DAILY',
            cadenceInterval: 1,
            cadenceType: 'manual',
            isBasedOnCompletion: false,
            repeatFrequency: 'DAILY',
            repeatInterval: 1,
            scheduleType: 'EVERY_N_DAYS',
            startDate,
            timeZone,
            triggerType: 'calendar',
          },
        ],
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerName(pmName).click();
      pmTriggerPages.details.threeDotMenu.click();
      pmTriggerPages.details.importButton.click();
      pmTriggerPages.imports.dataSetDropdown.click();
      pmTriggerPages.imports.dataSetPMSchedules.click();
      pmTriggerPages.imports.downloadTemplate.click();

      cy.readFile(downloadedTemplate).then((content) => {
        expect(content).to.contain('Calendar');
        expect(content).to.contain('Meter');
      });
    },
  );

  it('can export current pm schedule file', { testCaseId: 'QA-T6389' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const startDate = DateTime.now().plus({ days: 1 }).toUTC();
    const pmName = `current pm schedules ${now}`;
    const currentPMSchedulesFile = `${DOWNLOAD_LOCATION}/upkeep-pmschedules.csv`;

    createBasicPmTemplate({
      name: pmName,
      schedules: [
        {
          cadenceFreq: 'DAILY',
          cadenceInterval: 1,
          cadenceType: 'manual',
          isBasedOnCompletion: false,
          repeatFrequency: 'DAILY',
          repeatInterval: 1,
          scheduleType: 'EVERY_N_DAYS',
          startDate,
          timeZone,
          triggerType: 'calendar',
        },
      ],
    });

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
    pmTriggerPages.list.pmTriggerName(pmName).click();

    pmTriggerPages.details.threeDotMenu.click();
    pmTriggerPages.details.importButton.click();
    pmTriggerPages.imports.dataSetDropdown.click();
    pmTriggerPages.imports.dataSetPMSchedules.click();
    pmTriggerPages.imports.exportCurrentPMSchedules.click();
    cy.wait(1500); // prod takes longer to download the file
    cy.readFile(currentPMSchedulesFile).then((content) => {
      expect(content).to.contain(pmName);
      expect(content).to.contain(
        DateTime.fromISO(startDate).toFormat('LL/dd/yyyy'),
      );
      expect(content).to.contain(timeZone);
    });
  });

  it(
    'can navigate to how to import pm schedule help page',
    { testCaseId: 'QA-T6390' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const startDate = DateTime.now().plus({ days: 1 }).toUTC();
      const pmName = `current pm schedules ${now}`;

      createBasicPmTemplate({
        name: pmName,
        schedules: [
          {
            cadenceFreq: 'DAILY',
            cadenceInterval: 1,
            cadenceType: 'manual',
            isBasedOnCompletion: false,
            repeatFrequency: 'DAILY',
            repeatInterval: 1,
            scheduleType: 'EVERY_N_DAYS',
            startDate,
            timeZone,
            triggerType: 'calendar',
          },
        ],
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerName(pmName).click();
      pmTriggerPages.details.threeDotMenu.click();
      pmTriggerPages.details.importButton.click();
      pmTriggerPages.imports.dataSetDropdown.click();
      pmTriggerPages.imports.dataSetPMSchedules.click();
      pmTriggerPages.imports.seeExamplesAndTutorials.should(
        'not.have.attr',
        'href',
        '#undefined',
      );
    },
  );
};

export default canDownloadPMSchedulesTemplate;
