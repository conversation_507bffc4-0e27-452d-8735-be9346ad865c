import * as pmTriggerPages from '../components';
import * as h from '../../../helpers';
import { uploadFile } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import { createPrequests } from '../helpers';
import * as pmHelpers from '../helpers';

const canCreateBocTrigger = ({ fileName, teamName }) => {
  it('Can create BOC trigger', { testCaseId: 'QA-T6294' }, () => {
    const now = Date.now();
    const assetName = `asset ${now}`;
    const checklistName = `checklist ${now}`;
    const locationName = `location ${now}`;
    const meterName = `pm-meter-ab ${now}`;
    const pmName = `${now}-name`;
    const pmDesc = 'PM Description asset on completion desc';
    const pmWOTitle = `${now}-title`;

    const preRequests = {
      asset: assetName,
      checklist: checklistName,
      location: locationName,
      meter: meterName,
    };

    createPrequests(preRequests);
    uploadFile({ originalName: fileName });
    upkeepPages.PREVENTIVE_MAINTENANCE.go();

    h.createBasicPMTrigger({
      name: pmName,
      mainDescription: pmName,
    });

    pmTriggerPages.list.createTriggerButton.click();
    pmTriggerPages.list.optionNoneOrOne.click();

    pmTriggerPages.addEdit.editTimeBasedTriggerButton.click();
    pmTriggerPages.addEdit.repeatTypeDropdown.select('After Completion');
    pmTriggerPages.addEdit.createFirstWorkOrderImmediately.click();
    pmTriggerPages.addEdit.editTimeDoneButton.click();
    pmTriggerPages.addEdit.scheduleText.shouldContain(
      'Time:Due 1 day after previous completion',
    );

    pmTriggerPages.addEdit.triggerNameInput.type(pmName);
    pmTriggerPages.addEdit.workOrderTitleInput.type(pmWOTitle);
    pmTriggerPages.addEdit.triggerDescriptionInput.type(pmDesc);

    pmTriggerPages.addEdit.assignAssetButton.select(assetName);
    pmTriggerPages.addEdit.confirmAssetSelectionButton.click();

    pmTriggerPages.addEdit.assignLocationButton.select(locationName);
    pmTriggerPages.addEdit.confirmLocationSelectionButton.click();

    pmTriggerPages.addEdit.assignedToDropdown.select('Technician');
    pmTriggerPages.addEdit.additionalWorkersDropdown.select(
      'Limited Administrator',
    );

    pmTriggerPages.addEdit.teamDropdown.nestedSelect([
      '.react-select__menu',
      teamName,
    ]);

    pmTriggerPages.addEdit.addChecklistsButton.select(checklistName);
    pmTriggerPages.addEdit.confirmChecklistsButton.click();
    pmTriggerPages.addEdit.confirmAddTasksButton.click();

    pmHelpers.selectFileFromModal(fileName);
    pmTriggerPages.addEdit.confirmFilesButton.click();

    pmTriggerPages.addEdit.createButton.click();

    pmTriggerPages.list.pmTriggerName(pmName).click();

    pmTriggerPages.details.detailsTab.click();
    cy.contains(pmName);
    cy.contains(pmDesc);
    cy.contains('Activity');
  });
};

export default canCreateBocTrigger;
