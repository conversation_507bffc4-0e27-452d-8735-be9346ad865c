import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canDownloadPMTemplateSample = () => {
  it(
    'can download PM templates sample file',
    { testCaseId: 'QA-T5441' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const downloadedTemplate = `${DOWNLOAD_LOCATION}/pmtemplates-sample.csv`;

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.threeDotMenu.click();
      pmTriggerPages.list.importButton.click();
      pmTriggerPages.imports.dataSetDropdown.click();
      pmTriggerPages.imports.dataSetDropdown.type('PM Templates');
      pmTriggerPages.imports.dataSetPmTemplates.click();
      pmTriggerPages.imports.downloadTemplate.click();

      cy.readFile(downloadedTemplate).then((content) => {
        expect(content).to.contain('Sample PM Template 1');
        expect(content).to.contain('Example work order description');
      });
    },
  );
};

export default canDownloadPMTemplateSample;
