import moment from 'moment';
import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as meterHelpers from '../../meters/helpers/metersHelpers';
import * as woPages from '../../workOrders/components';
import * as pmPages from '../components';
import * as pmHelpers from '../helpers';

const canInstantiateMeterScheduleIsLessThan = () => {
  it(
    'Verify Work Order is created for less than meter',
    {
      testCaseId: 'QA-T6367',
    },
    () => {
      const now = Date.now();
      const pmName = `pm trigger test ${now}`;
      const meterName = `meter1 ${now}`;
      const units = 'gallons';
      const triggerValue = 1;
      const mainDescription = `wo trigger for meter less than ${now}`;
      cy.contains('Work Orders').should('be.visible');
      h.createAsset(
        {
          Name: `test asset ${now}`,
          availabilityTrackingOn: true,
        },
        true,
      ).then((asset) => {
        h.createMeter(
          {
            name: meterName,
            updateFrequency: 2,
            units,
            assetAssigned: asset.body.results.id,
          },
          true,
        ).then(({ body }) => {
          const meter = body.result;
          upkeepPages.METERS.go();
          meterHelpers.selectMeter(meterName);
          meterHelpers.addMeterReading(10);

          h.createBasicPmTemplate({
            createFirstWO: false,
            name: pmName,
            mainDescription,
            schedules: [
              {
                meter: meter.id,
                meterCondition: 'everyLessThan',
                meterConditionValue: 5,
                meterDueInterval: 5,
                meterDueFrequency: 'days',
                timeZone: 'UTC',
                triggerType: 'meter',
                startDate: moment().add(1, 'm').toDate(),
              },
            ],
          });
          upkeepPages.PREVENTIVE_MAINTENANCE.go();
          cy.contains(pmName).should('exist');
        });
      });

      upkeepPages.METERS.go();
      meterHelpers.selectMeter(meterName);
      meterHelpers.addMeterReading(triggerValue);

      cy.intercept('GET', `/api/v3/pm/*/schedules?**`).as('getPMSchedule');
      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmPages.list.pmTriggerRow(pmName).click();
      cy.wait('@getPMSchedule').then((intercept) => {
        // accessing the response body
        const [latestSchedule] = intercept.response.body.data;
        const [latestFulfillment] = latestSchedule.fulfillments || [];
        pmHelpers.instantiatePmSchedule(latestFulfillment._id, 'meter');
        cy.wait(300);
        upkeepPages.WORK_ORDERS.go();
        cy.reload();
        woPages.list
          .workOrderByName(mainDescription)
          .shouldHaveLengthGreaterThan(0);
      });
    },
  );
};

export default canInstantiateMeterScheduleIsLessThan;
