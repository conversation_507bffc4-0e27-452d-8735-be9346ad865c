import { DateTime } from 'luxon';
import * as pmTriggerPages from '../components';
import { upkeepPages } from '../../../support/constants';
import { createAsset, createLocation, createMeter } from '../../../helpers';
import { enterDateInPMRecords, scrollRecordsTable } from '../helpers';

const canCreatePMWithCalendarAndMeterSchedules = () => {
  it(
    'Can create PM with meter and calendar schedules',
    {
      testCaseId: 'QA-T6671',
    },
    () => {
      const now = Date.now();
      const pmName = `${now}-name`;
      const pmDesc = 'PM Description regular repeat';
      const asset1Name = `${now}-asset-1`;
      const asset2Name = `${now}-asset-2`;
      const location1Name = `location ${now}-1`;
      const location2Name = `location ${now}-2`;
      const timezone = 'Africa/Asmara';
      const repeatInterval = 10;
      const repeatFreq = 'Month(s)';
      const cadenceInterval = 5;
      const cadenceFreq = 'Day(s)';
      const monthdays = new Date().getDate() === 12 ? 13 : 12;
      const meterName = `meter-${now}`;
      const meterCondition = 'Is exactly';
      const meterConditionValue = 5;
      const meterDueInterval = 2;
      const meterDueFreq = 'Month(s)';
      const startDate = DateTime.now().plus({ days: 10 }).toFormat('MM/dd/yy');
      const endDate = `${DateTime.now()
        .plus({ days: 50 })
        .toFormat('MM/dd/yy')} 12:30 PM`;
      createAsset({ Name: asset1Name }, true);
      createAsset({ Name: asset2Name }, true);
      createLocation({ stringName: location1Name });
      createLocation({ stringName: location2Name });
      createMeter({ name: meterName }, true);

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.addEdit.addWODetailsBtn.click();

      pmTriggerPages.addEdit.woTitleInput.type(pmName);
      pmTriggerPages.addEdit.pmDescriptionInput.type(pmDesc);
      pmTriggerPages.addEdit.woModalAddBtn.click();

      pmTriggerPages.addEdit.addScheduleButton.click();
      pmTriggerPages.addEdit.selectCalendarMeterOptionV4.click();

      pmTriggerPages.addEdit.repeatIntervalInput.type(
        `{selectall}${repeatInterval}`,
      );
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem(repeatFreq).click();
      pmTriggerPages.addEdit.monthdaysDropdownButton.scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(monthdays).scrollIntoView().click();
      pmTriggerPages.addEdit.cadenceIntervalInput.type(
        `{selectall}${cadenceInterval}`,
      );
      pmTriggerPages.addEdit.cadenceFreqManualDropdownButton.click();
      pmTriggerPages.addEdit.listItem(cadenceFreq).click();

      pmTriggerPages.addEdit.meterConditionButton.click();
      pmTriggerPages.addEdit.listItem(meterCondition).click();
      pmTriggerPages.addEdit.meterConditionValueInput.type(
        `{selectall}${meterConditionValue}`,
      );
      pmTriggerPages.addEdit.meterDueIntervalInput.type(
        `{selectall}${meterDueInterval}`,
      );
      pmTriggerPages.addEdit.meterDueFreqButton.click();
      pmTriggerPages.addEdit.listItem(meterDueFreq).click();

      cy.wait(1250);
      pmTriggerPages.addEdit.doneButton.click();

      cy.contains(
        `Due every ${repeatInterval} months on day ${monthdays} or every ${meterConditionValue} units`,
      );
      cy.contains(`created ${cadenceInterval} days before due date`);

      pmTriggerPages.addEdit.bulkSelectAssetsButton.click();
      pmTriggerPages.addEdit
        .selectAssetFromModal(asset1Name)
        .scrollIntoView()
        .click();
      pmTriggerPages.addEdit
        .selectAssetFromModal(asset2Name)
        .scrollIntoView()
        .click();
      pmTriggerPages.addEdit.confirmBtn.click();

      pmTriggerPages.addEdit.locationDropdownButton(0).click();
      pmTriggerPages.addEdit.listItem(location1Name).scrollIntoView().click();
      pmTriggerPages.addEdit.locationDropdownButton(1).click();
      pmTriggerPages.addEdit.listItem(location2Name).scrollIntoView().click();

      pmTriggerPages.addEdit.applyMeterCheckbox(0).click();
      pmTriggerPages.addEdit.applyCalendarCheckbox(1).click();

      pmTriggerPages.addEdit.meterDropdownButton(1).click();
      pmTriggerPages.addEdit.listItem(meterName).scrollIntoView().click();

      scrollRecordsTable(0);
      cy.wait(100);

      enterDateInPMRecords('startDate-0', startDate);
      enterDateInPMRecords('endDate-0', endDate);

      scrollRecordsTable(0);
      cy.wait(100);

      enterDateInPMRecords('startDate-1', startDate);
      enterDateInPMRecords('endDate-1', endDate);

      pmTriggerPages.addEdit.timezoneDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();
      pmTriggerPages.addEdit.timezoneDropdownButton(1).click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();

      pmTriggerPages.addEdit.assignToDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem('Tester').click();
      pmTriggerPages.addEdit.assignToDropdownButton(1).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem('Tester').click();

      pmTriggerPages.addEdit.createPMButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 10 months');
      cy.contains(`On day ${monthdays}`);
      cy.contains('Equal to 5 Miles');
      cy.contains('Tester');
      cy.contains(asset1Name);
      cy.contains(asset2Name);
      cy.contains(startDate.split(' ')[0]);
      cy.contains(endDate.split(' ')[0]);
    },
  );
};

export default canCreatePMWithCalendarAndMeterSchedules;
