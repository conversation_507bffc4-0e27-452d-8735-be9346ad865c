import { DateTime } from 'luxon';
import { createAsset, createLocation } from '../../../helpers';
import * as pmTriggerPages from '../components';
import { upkeepPages } from '../../../support/constants';
import { enterDateInPMRecords, scrollRecordsTable } from '../helpers';

const canCreatePMWithCalendarSchedules = ({ teamName }) => {
  const now = Date.now();
  const asset1Name = `${now}-asset-1`;
  const asset2Name = `${now}-asset-2`;
  const location1Name = `location ${now}-1`;
  const location2Name = `location ${now}-2`;
  const timezone = 'Africa/Asmara';
  const startDate = DateTime.now().plus({ days: 10 }).toFormat('MM/dd/yy');
  const endDate = `${DateTime.now()
    .plus({ days: 50 })
    .toFormat('MM/dd/yy')} 12:30 PM`;

  it(
    'should create a PM with a calendar schedule',
    {
      testCaseId: 'QA-T6672',
    },
    () => {
      createAsset({ Name: asset1Name }, true);
      createAsset({ Name: asset2Name }, true);
      createLocation({ stringName: location1Name });
      createLocation({ stringName: location2Name });
      const pmName = `${now}-name`;
      const pmDesc = 'PM Description regular repeat';
      const repeatInterval = 10;
      const repeatFreq = 'Month(s)';
      const cadenceInterval = 5;
      const cadenceFreq = 'Day(s)';
      const monthdays = new Date().getDate() === 12 ? 13 : 12;

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.addEdit.addWODetailsBtn.click();

      pmTriggerPages.addEdit.woTitleInput.type(pmName);
      pmTriggerPages.addEdit.pmDescriptionInput.type(pmDesc);
      pmTriggerPages.addEdit.woModalAddBtn.click();

      pmTriggerPages.addEdit.addScheduleButton.click();
      pmTriggerPages.addEdit.selectCalendarOptionV4.click();

      pmTriggerPages.addEdit.repeatIntervalInput.type(
        `{selectall}${repeatInterval}`,
      );
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem(repeatFreq).click();
      pmTriggerPages.addEdit.monthdaysDropdownButton.scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(monthdays).scrollIntoView().click();
      pmTriggerPages.addEdit.cadenceIntervalInput.type(
        `{selectall}${cadenceInterval}`,
      );
      pmTriggerPages.addEdit.cadenceFreqManualDropdownButton.click();
      pmTriggerPages.addEdit.listItem(cadenceFreq).click();

      cy.wait(1000);
      pmTriggerPages.addEdit.doneButton.click();

      cy.contains(`Due every ${repeatInterval} months on day ${monthdays}`);
      cy.contains(`created ${cadenceInterval} days before due date`);

      pmTriggerPages.addEdit.assetDropdownButton(0).click();
      pmTriggerPages.addEdit.listItem(asset1Name).scrollIntoView().click();

      pmTriggerPages.addEdit.locationDropdownButton(0).click();
      pmTriggerPages.addEdit.listItem(location1Name).scrollIntoView().click();

      scrollRecordsTable(0);
      cy.wait(100);

      enterDateInPMRecords('startDate-0', startDate);
      enterDateInPMRecords('endDate-0', endDate);

      pmTriggerPages.addEdit.timezoneDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();

      pmTriggerPages.addEdit.assignToDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem('Unassigned').click();

      pmTriggerPages.addEdit
        .additionalWorkersDropdownButton(0)
        .scrollIntoView()
        .click();
      pmTriggerPages.addEdit.listItem('Unassigned').click();
      pmTriggerPages.addEdit.listItem('engineering-test+').click();
      cy.get('body').trigger('mousemove', 10, 10);
      pmTriggerPages.addEdit.portalBackdrop.get().last().click();

      pmTriggerPages.addEdit.teamDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(teamName).click();

      pmTriggerPages.addEdit.createPMButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 10 months');
      cy.contains(`On day ${monthdays}`);
      cy.contains(asset1Name);
      cy.contains(startDate.split(' ')[0]);
      cy.contains(endDate.split(' ')[0]);
    },
  );

  it(
    'should create a PM with a calendar schedule without assets',
    {
      testCaseId: 'QA-T6673',
    },
    () => {
      const pmName = `${now}-without-asset`;
      const pmDesc = 'PM Description without assets';
      const repeatInterval = 10;
      const repeatFreq = 'Month(s)';
      const cadenceInterval = 5;
      const cadenceFreq = 'Day(s)';
      const monthdays = new Date().getDate() === 12 ? 13 : 12;

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.addEdit.addWODetailsBtn.click();

      pmTriggerPages.addEdit.woTitleInput.type(pmName);
      pmTriggerPages.addEdit.pmDescriptionInput.type(pmDesc);
      pmTriggerPages.addEdit.woModalAddBtn.click();

      pmTriggerPages.addEdit.addScheduleButton.click();
      pmTriggerPages.addEdit.selectCalendarOptionV4.click();

      pmTriggerPages.addEdit.repeatIntervalInput.type(
        `{selectall}${repeatInterval}`,
      );
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem(repeatFreq).click();
      pmTriggerPages.addEdit.monthdaysDropdownButton.scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(monthdays).scrollIntoView().click();
      pmTriggerPages.addEdit.cadenceIntervalInput.type(
        `{selectall}${cadenceInterval}`,
      );
      pmTriggerPages.addEdit.cadenceFreqManualDropdownButton.click();
      pmTriggerPages.addEdit.listItem(cadenceFreq).click();

      pmTriggerPages.addEdit.doneButton.click();

      cy.contains(`Due every ${repeatInterval} months on day ${monthdays}`);
      cy.contains(`created ${cadenceInterval} days before due date`);

      scrollRecordsTable(0);
      cy.wait(100);

      enterDateInPMRecords('startDate-0', startDate);
      enterDateInPMRecords('endDate-0', endDate);

      pmTriggerPages.addEdit.timezoneDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();

      pmTriggerPages.addEdit.createPMButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 10 months');
      cy.contains(`On day ${monthdays}`);
      cy.contains(startDate.split(' ')[0]);
      cy.contains(endDate.split(' ')[0]);
    },
  );

  it(
    'should create a PM with a calendar schedule with duplicate schedules',
    {
      testCaseId: 'QA-T6674',
    },
    () => {
      const pmName = `${now}-duplicate-schedules`;
      const pmDesc = 'PM Description duplicate schedules configured';
      const repeatInterval = 10;
      const repeatFreq = 'Month(s)';
      const cadenceInterval = 5;
      const cadenceFreq = 'Day(s)';
      const monthdays = new Date().getDate() === 12 ? 13 : 12;

      upkeepPages.PREVENTIVE_MAINTENANCE.go();

      pmTriggerPages.list.createTriggerButton.click();
      pmTriggerPages.addEdit.addWODetailsBtn.click();

      pmTriggerPages.addEdit.woTitleInput.type(pmName);
      pmTriggerPages.addEdit.pmDescriptionInput.type(pmDesc);
      pmTriggerPages.addEdit.woModalAddBtn.click();

      pmTriggerPages.addEdit.addScheduleButton.click();
      pmTriggerPages.addEdit.selectCalendarOptionV4.click();

      pmTriggerPages.addEdit.repeatIntervalInput.type(
        `{selectall}${repeatInterval}`,
      );
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem(repeatFreq).click();
      pmTriggerPages.addEdit.monthdaysDropdownButton.scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(monthdays).scrollIntoView().click();
      pmTriggerPages.addEdit.cadenceIntervalInput.type(
        `{selectall}${cadenceInterval}`,
      );
      pmTriggerPages.addEdit.cadenceFreqManualDropdownButton.click();
      pmTriggerPages.addEdit.listItem(cadenceFreq).click();

      pmTriggerPages.addEdit.doneButton.click();

      cy.contains(`Due every ${repeatInterval} months on day ${monthdays}`);
      cy.contains(`created ${cadenceInterval} days before due date`);

      pmTriggerPages.addEdit.addRecordButton.click();

      pmTriggerPages.addEdit.assetDropdownButton(0).click();
      pmTriggerPages.addEdit.listItem(asset1Name).scrollIntoView().click();

      pmTriggerPages.addEdit.assetDropdownButton(1).click();
      pmTriggerPages.addEdit.listItem(asset1Name).scrollIntoView().click();

      pmTriggerPages.addEdit.locationDropdownButton(0).click();
      pmTriggerPages.addEdit.listItem(location1Name).scrollIntoView().click();

      pmTriggerPages.addEdit.locationDropdownButton(1).click();
      pmTriggerPages.addEdit.listItem(location1Name).scrollIntoView().click();

      enterDateInPMRecords('startDate-0', startDate);

      enterDateInPMRecords('endDate-0', endDate);
      enterDateInPMRecords('endDate-1', endDate);

      pmTriggerPages.addEdit.timezoneDropdownButton(0).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();

      pmTriggerPages.addEdit.timezoneDropdownButton(1).scrollIntoView().click();
      pmTriggerPages.addEdit.listItem(timezone).scrollIntoView().click();
      enterDateInPMRecords('startDate-1', startDate);

      pmTriggerPages.addEdit.createPMButton.click();
      pmTriggerPages.addEdit.doneButton.click();

      pmTriggerPages.list.pmTriggerName(pmName).click();
      cy.contains('Every 10 months');
      cy.contains(`On day ${monthdays}`);
      cy.contains(asset1Name);
      cy.contains(startDate.split(' ')[0]);
      cy.contains(endDate.split(' ')[0]);
    },
  );
};

export default canCreatePMWithCalendarSchedules;
