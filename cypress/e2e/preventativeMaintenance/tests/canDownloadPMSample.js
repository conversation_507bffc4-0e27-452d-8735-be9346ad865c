import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canDownloadPMSample = () => {
  it(
    'can download PM sample file',
    {
      testCaseId: 'QA-T5441',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const downloadedTemplate = `${DOWNLOAD_LOCATION}/preventive-maintenance-sample.csv`;

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.threeDotMenu.click();
      pmTriggerPages.list.importButton.click();
      pmTriggerPages.imports.dataSetDropdown.click();
      pmTriggerPages.imports.dataSetDropdown.type('Preventive Maintenance');
      pmTriggerPages.imports.dataSetPM.click();
      pmTriggerPages.imports.downloadTemplate.click();

      cy.readFile(downloadedTemplate).then((content) => {
        expect(content).to.contain('Sample PM Name');
        expect(content).to.contain('Example Description');
        expect(content).to.contain('Calendar');
        expect(content).to.contain('Meter');
      });
    },
  );
};

export default canDownloadPMSample;
