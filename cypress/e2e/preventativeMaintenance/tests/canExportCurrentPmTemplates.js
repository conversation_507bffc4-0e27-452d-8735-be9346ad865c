import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canExportCurrentPMTemplates = () => {
  it('can export current pm templates file', { testCaseId: 'QA-T5442' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const currentPmTriggerFile = `${DOWNLOAD_LOCATION}/upkeep-pmtemplates.csv`;

    const now = Date.now();
    const mainDescription = `pm work order name ${now}`;
    const pmName = `current pm filter-${now}`;
    const start = new Date(new Date().setDate(new Date().getDate() + 1));

    createBasicPmTemplate({
      mainDescription,
      priority: 0,
      createFirstWO: false,
      name: pmName,
      schedules: [
        {
          isBasedOnCompletion: true,
          cadenceFreq: 'DAILY',
          cadenceInterval: 1,
          cadenceType: 'manual',
          repeatFrequency: 'DAILY',
          repeatInterval: 1,
          scheduleType: 'EVERY_N_DAYS',
          timeZone: 'Asia/Kolkata',
          startDate: start,
          triggerType: 'calendar',
        },
      ],
    });

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();

    pmTriggerPages.list.threeDotMenu.click();
    pmTriggerPages.list.importButton.click();
    pmTriggerPages.imports.dataSetDropdown.click();
    pmTriggerPages.imports.dataSetDropdown.type('PM Templates');
    pmTriggerPages.imports.dataSetPmTemplates.click();
    pmTriggerPages.imports.exportCurrentPmTemplates.click();
    cy.wait(1500); // prod takes longer to download the file
    cy.readFile(currentPmTriggerFile).then((content) => {
      cy.log(content);
      expect(content).to.contain(pmName);
    });
  });
};

export default canExportCurrentPMTemplates;
