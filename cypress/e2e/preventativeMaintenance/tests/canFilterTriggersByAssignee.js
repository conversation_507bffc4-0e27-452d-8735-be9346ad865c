import { createBasicPmTemplate } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import * as pmHelpers from '../helpers';

const canFilterTriggersByAssignee = () => {
  it(
    'should filter pm templates by assignee',
    {
      testCaseId: 'QA-T5783',
    },
    () => {
      const now = Date.now();
      const pmName = `PM assignee filters-${now}`;
      const team = 'team rocket';
      const testUid = 'pm-assignee-filter';
      const mainDescription = `pm work order name ${now}`;
      const pmTriggerToCreate = 2;
      const assignees = {
        tech: 'tester technichan',
        limitedTech: 'Max Power',
        noAssignee: 'No Assignee(s)',
      };
      const assigneesList = [assignees.tech, assignees.limitedTech];

      cy.createOrLoginAdmin(testUid, [], 'BUSINESS_PLUS', team);
      cy.window().then((adminSession) => {
        const sessionToken = adminSession.localStorage.authToken;
        cy.switchUser(
          testUid,
          'TECH',
          {
            firstName: assignees.tech,
            lastName: 'Tech',
          },
          sessionToken,
        );
        cy.switchUser(
          testUid,
          'LIMITED_TECH',
          {
            firstName: assignees.limitedTech,
            lastName: 'LIMITED',
          },
          sessionToken,
        );
      });

      for (let i = 0; i < pmTriggerToCreate; i++) {
        const pm = `${i} ${pmName}`;
        createBasicPmTemplate({
          name: pm,
          priority: i,
          mainDescription,
        });
        upkeepPages.PREVENTIVE_MAINTENANCE.go();
        pmTriggerPages.list.pmTriggerRow(pm).click();
        // schedule tab
        cy.url().should('contain', '/schedules');
        cy.contains('Every 1 day').should('exist');

        pmTriggerPages.addEdit.shceduleRowMenuItem('Every 1 day').click();
        pmTriggerPages.addEdit.editScheduleMenu.click();

        pmTriggerPages.addEdit.modalRecordsTab.scrollIntoView().click();

        pmTriggerPages.addEdit.modalAssignedToDropdown.scrollIntoView().click();
        pmTriggerPages.addEdit
          .listItem(assigneesList[i])
          .scrollIntoView()
          .click();
        cy.wait(500);
        pmTriggerPages.addEdit.doneButton.click();
        cy.reload();
      }

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);

      // filter by assignee
      const pm1 = `0 ${pmName}`;
      const pm2 = `1 ${pmName}`;
      pmHelpers.filterPmByAssignee(assignees.tech);
      pmTriggerPages.list.pmTriggerRow(pm1).shouldBeVisible();
      pmTriggerPages.list.pmTriggerRow(pm2).shouldNotExist();

      // reset filters
      pmTriggerPages.list.resetFilterButton.click();
      pmTriggerPages.list
        .pmTriggerRows(pmName)
        .shouldHaveLength(pmTriggerToCreate);
    },
  );
};

export default canFilterTriggersByAssignee;
