import { createAsset, createBasicPmTemplate } from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import * as pmHelpers from '../helpers';

const canFilterTriggersByAssets = () => {
  it(
    'shoud filter pm templates by assets',
    {
      testCaseId: 'QA-T5797',
    },
    () => {
      const now = Date.now();
      const pmName = `PM asset filters-${now}`;

      const assetName = `asset1 ${now}`;
      const mainDescription = `pm work order name ${now}`;
      const pmTriggerToCreate = 2;

      for (let i = 0; i < pmTriggerToCreate; i++) {
        const asset = `${i} ${assetName}`;
        const pm = `${i} ${pmName}`;
        createAsset({ Name: asset }, true);
        createBasicPmTemplate({
          mainDescription,
          priority: 0,
          createFirstWO: false,
          name: pm,
        });
        upkeepPages.PREVENTIVE_MAINTENANCE.go();
        cy.reload();

        // assign asset to PM
        pmHelpers.assignAssetToPm(pm, asset, true);
      }

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      cy.reload();

      // filter by asset
      pmTriggerPages.list.filters.click();
      pmTriggerPages.list.addFilterButton.click();
      pmTriggerPages.list.filterByAssetOption.click();
      pmTriggerPages.list.assetFilterInput.click();
      const asset1 = `0 ${assetName}`;
      const pm1 = `0 ${pmName}`;
      const asset2 = `1 ${assetName}`;
      pmTriggerPages.list.menuSearchList.click();
      cy.wait(600);
      pmTriggerPages.list.menuSearchList.type(assetName);
      pmTriggerPages.list.assetListItem(asset1).shouldBeVisible();
      pmTriggerPages.list.assetListItem(asset2).shouldBeVisible();
      pmTriggerPages.list.assetListItem(asset1).click();

      pmTriggerPages.list.applyFilterButton.click({ force: true });

      pmTriggerPages.list.pmTriggerRowContains(pm1).shouldHaveLength(1);
    },
  );
};

export default canFilterTriggersByAssets;
