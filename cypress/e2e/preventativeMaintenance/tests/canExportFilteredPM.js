import { createMeter } from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { DOWNLOAD_LOCATION, upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';
import {
  exportPMWithSchedules,
  filterPmByLowPriority,
  filterPmByMediumPriority,
} from '../helpers';

const canExportFilteredPM = () => {
  it(
    'can export filtered pm single step',
    {
      testCaseId: 'QA-T1214',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const formattedDate = `${String(new Date().getMonth() + 1).padStart(
        2,
        '0',
      )}/${String(new Date().getDate()).padStart(
        2,
        '0',
      )}/${new Date().getFullYear()}`;
      const meter1Name = `meter1 ${now}`;
      const pmName = `pmExport${now}`;
      const pmName2 = `pm2Export${now}`;
      const downloadedPmFile = `${DOWNLOAD_LOCATION}/upkeep-preventive-maintenances.csv`;

      createMeter({ name: meter1Name }, true).then(({ body }) => {
        const meter = body.result;
        const schedules = [
          {
            meter: meter.id,
            meterCondition: 'every',
            meterConditionValue: 1,
            meterDueInterval: 1,
            meterDueFrequency: 'days',
            timeZone: 'Asia/Kolkata',
            startDate: new Date().toISOString(),
            triggerType: 'meter',
            dueTime: '10:00',
            triggerTime: '09:00',
          },
        ];
        createBasicPmTemplate({
          name: pmName,
          priority: 2, // Medium
          schedules,
        });
        createBasicPmTemplate({
          name: pmName2,
          priority: 1, // Low
          schedules,
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerRow(pmName2).shouldBeVisible();

      // filter by low priority and export
      filterPmByLowPriority();
      cy.contains(now).should('have.length', 1);

      exportPMWithSchedules();
      cy.readFile(downloadedPmFile).then((content) => {
        expect(content).to.not.contain(pmName);
        expect(content).to.contain(pmName2);
        expect(content).to.contain('every');
        expect(content).to.contain('Asia/Kolkata');
        expect(content).to.contain('09:00');
        expect(content).to.contain(formattedDate); // ensuring start date is formatted correctly
        expect(content).to.contain('10:00');
      });

      filterPmByMediumPriority();
      cy.get(`td:contains('${now}')`).should('have.length', 2);

      exportPMWithSchedules();
      cy.readFile(downloadedPmFile).then((content) => {
        expect(content).to.contain(pmName);
        expect(content).to.contain(pmName2);
        expect(content).to.contain('every');
        expect(content).to.contain('Asia/Kolkata');
        expect(content).to.contain('09:00');
        expect(content).to.contain('10:00');
      });
    },
  );
};

export default canExportFilteredPM;
