import moment from 'moment';
import timezone from 'moment-timezone';

import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canEditBocTrigger = () => {
  it(
    'Edits an already existing BOC trigger',
    {
      testCaseId: 'QA-T6293',
    },
    () => {
      const now = new Date();
      const pmTitle = `pm BOC edit pm case ${now}`;
      const start = new Date(new Date().setDate(new Date().getDate() + 1));
      const until = new Date(new Date().setMonth(new Date().getMonth() + 6));

      createBasicPmTemplate({
        mainDescription: 'pm work order name',
        priority: 0,
        createFirstWO: true,
        name: pmTitle,
        schedules: [
          {
            cadenceFreq: 'DAILY',
            cadenceInterval: 1,
            cadenceType: 'manual',
            endDate: timezone(until)
              .tz('Asia/Calcutta', true)
              .utc()
              .format(moment.HTML5_FMT.DATETIME_LOCAL_SECONDS),
            isBasedOnCompletion: true,
            repeatFrequency: 'DAILY',
            repeatInterval: 1,
            scheduleType: 'EVERY_N_DAYS',
            startDate: timezone(start)
              .tz('Asia/Calcutta', true)
              .utc()
              .format(moment.HTML5_FMT.DATETIME_LOCAL_SECONDS),
            timeZone: 'Asia/Kolkata',
            triggerType: 'calendar',
          },
        ],
        tasks: [
          {
            type: 'Task',
            name: 'Task 1',
          },
        ],
      });
      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmTitle).click();
      pmTriggerPages.addEdit.detailsTab.click();
      pmTriggerPages.addEdit.editDetailsButton.click();
      pmTriggerPages.addEdit.pmTitleInput.type(`EDIT!! ${pmTitle}`);
      pmTriggerPages.addEdit.woTitleInput.type('edited wo name');

      pmTriggerPages.addEdit.saveDetailsButton.click();
      cy.contains('PM updated').should('exist');
      cy.url().should('contain', 'web/preventive-maintenance');
      cy.url().should('not.include', '/edit');
      cy.url().should('contain', '/details');
      cy.contains(`EDIT!! ${pmTitle}`);
      pmTriggerPages.details.schedulesTab.click();

      pmTriggerPages.addEdit.shceduleRowMenuItem('Every 1 day').click();
      pmTriggerPages.addEdit.editScheduleMenu.click();

      cy.contains('Edit Record').should('exist');
      pmTriggerPages.addEdit.repeatIntervalInput.type(`{selectall}${11}`);
      pmTriggerPages.addEdit.repeatFreqDropdownButton.click();
      pmTriggerPages.addEdit.listItem('Week(s)').click();
      pmTriggerPages.addEdit.listItem('Week(s)').should('not.be.visible');
      pmTriggerPages.addEdit.doneButton.should('be.visible');
      pmTriggerPages.addEdit.doneButton.click();

      cy.contains('Every 11 weeks').should('exist');
      pmTriggerPages.details.detailsTab.click();
      cy.url().should('contain', '/details');
      cy.contains('EDIT!!').should('exist');

      // * WOs are not being created instantly, so this assertion is not possible
      // pmTriggerPages.details.workOrderTab.click();
      // cy.contains('edited wo name').should('exist');
    },
  );
};

export default canEditBocTrigger;
