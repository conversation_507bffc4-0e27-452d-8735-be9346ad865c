import moment from 'moment';
import timezone from 'moment-timezone';
import * as h from '../../../helpers';
import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { upkeepPages } from '../../../support/constants';
import * as pmTriggerPages from '../components';

const canEditMeterSchedule = () => {
  it(
    'Can edit meter schedule trigger',
    {
      testCaseId: 'QA-T6304',
    },
    () => {
      const now = Date.now();
      const assetName = `pm-asset calendar-schedule ${now}`;
      const locationName = `pm-location calendar-schedule ${now}`;
      const pmName = `${now}-name`;
      const pmWOTitle = `${now}-title`;
      const meterName = `meter ${now}`;
      const start = new Date(new Date().setDate(new Date().getDate() + 1));

      h.createAsset({ Name: assetName }, true);
      h.createLocation({ stringName: locationName }, true);
      h.createMeter({ name: meterName, units: 'Miles' }, true).then(
        ({ body }) => {
          const meter = body.result;

          createBasicPmTemplate({
            mainDescription: pmWOTitle,
            priority: 0,
            name: pmName,
            selectedScheduleType: 'meter',
            schedules: [
              {
                meterCondition: 'every',
                meterConditionValue: 1,
                meterDueFrequency: 'days',
                meterDueInterval: 1,
                meter: meter.id,
                startDate: timezone(start)
                  .tz('Asia/Calcutta', true)
                  .utc()
                  .format(moment.HTML5_FMT.DATETIME_LOCAL_SECONDS),
                timeZone: 'Asia/Kolkata',
                triggerType: 'meter',
              },
            ],
          });
        },
      );

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).click();
      pmTriggerPages.details.schedulesTab.click();
      cy.url().should('contain', '/schedules');
      cy.contains('Every 1 Miles').should('exist');
      pmTriggerPages.addEdit.shceduleRowMenuItem('Every 1 Miles').click();
      pmTriggerPages.addEdit.editScheduleMenu.click();

      pmTriggerPages.addEdit.meterConditionButton.click();
      pmTriggerPages.addEdit.listItem('Is exactly').click();

      pmTriggerPages.addEdit.meterDueIntervalInput.type('2');
      pmTriggerPages.addEdit.meterDueFreqButton.click();
      pmTriggerPages.addEdit.listItem('Hour(s)').click();

      pmTriggerPages.addEdit.doneButton.click();

      pmTriggerPages.addEdit
        .scheduleMeterFreqExactly('Equal to 1 Miles')
        .should('exist');
    },
  );
};

export default canEditMeterSchedule;
