import { DateTime } from 'luxon';
import * as pmTriggerPages from '../components';
import {
  exportSchedulesFilteredView,
  filterSchedulesByAsset,
  filterSchedulesByLocation,
} from '../helpers';
import {
  createMeter,
  createBasicPmTemplate,
  createAsset,
  createLocation,
} from '../../../helpers';
import { upkeepPages, DOWNLOAD_LOCATION } from '../../../support/constants';

const canExportFilteredPMSchedules = () => {
  it(
    'can export pm schedules filtered view',
    { testCaseId: 'QA-T6387' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const startDate = DateTime.now().plus({ days: 1 }).toUTC();
      const timeZone = 'Asia/Kolkata';
      const meter1Name = `meter1 ${now}`;
      const asset1Name = `asset1 ${now}`;
      const locName = `loc1 ${now}`;
      const pmName = `pmSchedulesExport${now}`;
      const downloadedPmFile = `${DOWNLOAD_LOCATION}/upkeep-pmschedules.csv`;

      createAsset({ Name: asset1Name }, true).then(({ body: assetBody }) => {
        createLocation({ stringName: locName }).then(({ body: locBody }) => {
          createMeter({ name: meter1Name }, true).then(({ body }) => {
            createBasicPmTemplate({
              name: pmName,
              schedules: [
                {
                  location: locBody.result.id,
                  cadenceFreq: 'DAILY',
                  cadenceInterval: 1,
                  cadenceType: 'manual',
                  isBasedOnCompletion: false,
                  repeatFrequency: 'DAILY',
                  repeatInterval: 1,
                  scheduleType: 'EVERY_N_DAYS',
                  startDate,
                  timeZone,
                  triggerType: 'calendar',
                },
                {
                  asset: assetBody.results.id,
                  location: locBody.result.id,
                  cadenceFreq: 'DAILY',
                  cadenceInterval: 1,
                  cadenceType: 'manual',
                  isBasedOnCompletion: false,
                  repeatFrequency: 'DAILY',
                  repeatInterval: 1,
                  scheduleType: 'EVERY_N_DAYS',
                  startDate,
                  timeZone,
                  triggerType: 'calendar',
                },
                {
                  meter: body.result.id,
                  meterCondition: 'every',
                  meterConditionValue: 3,
                  meterDueFrequency: 'hours',
                  meterDueInterval: 2,
                  startDate,
                  timeZone,
                  triggerType: 'meter',
                },
              ],
            });
          });
        });
      });

      upkeepPages.PREVENTIVE_MAINTENANCE.go();
      pmTriggerPages.list.pmTriggerRow(pmName).shouldBeVisible();
      pmTriggerPages.list.pmTriggerName(pmName).click();

      filterSchedulesByAsset(asset1Name);
      cy.wait(500);
      filterSchedulesByLocation(locName);
      cy.wait(100);

      exportSchedulesFilteredView();
      cy.readFile(downloadedPmFile).then((content) => {
        expect(content).to.contain('calendar');
        expect(content).to.contain(timeZone);
        expect(content).to.contain(asset1Name);
        expect(content).to.contain(locName);
      });
    },
  );
};

export default canExportFilteredPMSchedules;
