import { createBasicPmTemplate } from '../../../helpers/createHelpers/createPmTemplate';
import { startDromoImport } from '../../../helpers/dromoImportHelpers';
import { upkeepPages } from '../../../support/constants';
import * as pmPages from '../components';
import * as pmHelpers from '../helpers';

const canImportPmTemplates = () => {
  const csvFile = 'pm-template-import.csv';
  const now = Date.now();
  const pmName = `PM Template ${now}`;
  const woTitle = `PM Template WO ${now}`;
  const woDescription = `PM Template WO Description ${now}`;
  const priority = 1; // Low;

  const testData = {
    pmName,
    woTitle,
    woDescription,
    priority,
  };

  it('should create pm template via import', { testCaseId: 'QA-T5444' }, () => {
    Cypress.on('uncaught:exception', () => false);

    // Create csv
    const csvData = pmHelpers.createCsvData('pmV3', testData);
    cy.writeFile(
      `cypress/fixtures/${csvFile}`,
      csvData.replaceAll('undefined', ''),
    );

    // Begin import
    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmHelpers.importPM(csvFile, true);
    startDromoImport();
    pmHelpers.verifyImportCreateSucces(true);
  });

  it('should update pm template via import', () => {
    Cypress.on('uncaught:exception', () => false);

    createBasicPmTemplate().then(({ body }) => {
      const editData = {
        ...testData,
      };
      const createdPM = body.result;
      editData.pmName = `EDIT!! ${testData.pmName}`;
      editData.id = createdPM._id;

      // Create csv
      const csvData = pmHelpers.createCsvData('pmV3', editData);
      cy.writeFile(
        `cypress/fixtures/${csvFile}`,
        csvData.replaceAll('undefined', ''),
      );
    });

    // Begin import
    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    pmHelpers.importPM(csvFile, true);
    startDromoImport();
    cy.contains(
      `Import Complete. Created 0 and updated 1 PM Templates.`,
    ).should('be.visible');

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.reload();
    pmPages.list.pmTriggerRow(`EDIT!! ${testData.pmName}`).shouldBeVisible();
  });

  it('should create a pm manual import', { testCaseId: 'QA-T5446' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const data = {
      pmName: `manual import ${now}`,
      woTitle: `manual PM WO ${now}`,
      woDescription: `manual PM WO description ${now}`,
    };

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.contains('Preventive Maintenance').should('be.visible');

    pmPages.list.threeDotMenu.click();
    pmPages.list.importButton.click();
    pmPages.imports.dataSetDropdown.click();
    pmPages.imports.dataSetDropdown.type('PM Templates');
    pmPages.imports.dataSetPmTemplates.click();
    pmPages.list.startImportProcessV3.click();

    pmHelpers.selectImportDataManually();
    pmHelpers.fillManualImportData(data, true);

    cy.contains(
      'Import Complete. Created 1 and updated 0 PM Templates.',
    ).should('be.visible');

    upkeepPages.PREVENTIVE_MAINTENANCE.go();
    cy.reload();
    pmPages.list.pmTriggerRow(data.pmName).shouldBeVisible();
  });
};

export default canImportPmTemplates;
