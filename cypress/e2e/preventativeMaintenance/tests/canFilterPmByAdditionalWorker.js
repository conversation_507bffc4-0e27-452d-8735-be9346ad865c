import * as h from '../../../helpers';
import { upkeepPages } from '../../../support/constants';
import * as pmPages from '../components';
import * as pmHelpers from '../helpers';

const canFilterPmByAdditionalWorker = () => {
  it(
    'should filter pm templates by additional worker',
    {
      testCaseId: 'QA-T5798',
    },
    () => {
      const now = Date.now();
      const testId = 'pm-additional-worker';
      const pmForLimAdmin = `PM FOR LIMITED ADMIN ${now}`;
      const pmForTech = `PM FOR TECH ${now}`;
      const basicPmName = `BASIC PM ${now}`;
      const limitedAdmin = {
        firstName: 'Limited',
        lastName: 'Admin',
      };
      const tech = {
        firstName: 'Tech',
        lastName: 'Technician',
      };

      const pmList = [pmForLimAdmin, pmForTech];
      const workers = [limitedAdmin, tech];

      // Onboard Limited_Admin and Tech user
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
      cy.window().then((adminSession) => {
        const sessionToken = adminSession.localStorage.authToken;
        cy.switchUser(testId, 'LIMITED_ADMIN', limitedAdmin, sessionToken);
        cy.switchUser(testId, 'TECH', tech, sessionToken);
      });

      // Create a basic PM without additional workers
      h.createBasicPmTemplate({ name: basicPmName });

      // Get user ids and use them to assign as additional workers to a basic PM
      for (let i = 0; i < workers.length; i++) {
        h.getUserByName(workers[i].firstName, workers[i].lastName).then(
          (res) => {
            const userId = res.body.results[0].id;

            // create a new PM Trigger
            h.createBasicPmTemplate({
              name: pmList[i],
              schedules: [
                h.basicCalendarSchedule(undefined, {
                  supportUsers: [userId],
                }),
              ],
            });

            upkeepPages.PREVENTIVE_MAINTENANCE.go();

            pmHelpers.filterPmByAdditionalWorker(
              `${workers[i].firstName} ${workers[i].lastName}`,
            );
            pmHelpers.verifyPmFilteredByAdditionalWorker(
              pmList[i],
              basicPmName,
            );
            pmPages.list.resetFilterButton.click();
          },
        );
      }
    },
  );
};

export default canFilterPmByAdditionalWorker;
