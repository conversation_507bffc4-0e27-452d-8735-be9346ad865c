import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe('Filter PM Templates v3', () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });
    const testId = 'pmTemplatesFilters';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'alpha team');
    });

    pmTests.canFilterTriggersByAssets();
    pmTests.canFilterTriggersByFile();
    pmTests.canPersisFilterSearch();
    pmTests.canFilterTriggersByPriority();
    pmTests.canFilterTriggersByTeam();
    pmTests.canFilterTriggersByLocation();
    pmTests.canFilterTriggersByAssignee();
    pmTests.canFilterTriggerByCategory();
    pmTests.canFilterPmByAdditionalWorker();
  });
});
