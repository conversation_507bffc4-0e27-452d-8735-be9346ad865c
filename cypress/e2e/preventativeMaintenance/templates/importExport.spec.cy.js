import filterTests from '../../../support/filterTests';
import * as pmTests from '../tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('PM Template import/exports', () => {
    const testId = 'pmExport';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'super team');
    });
    after(() => {
      cy.exec('rm -rf cypress/fixtures/pm-template-import.csv', {
        failOnNonZeroExit: false,
      });
    });

    pmTests.canExportFilteredPmTemplates();
    pmTests.canDownloadPMTemplateSample();
    pmTests.canExportCurrentPMTemplates();
    pmTests.canNavigateToImportPMTemplateHelp();
    pmTests.canExportUnfilteredPMTemplates();
    pmTests.canImportPmTemplates();
  });
});
