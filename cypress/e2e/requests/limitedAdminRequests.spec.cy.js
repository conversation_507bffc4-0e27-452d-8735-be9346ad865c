import filterTests from '../../support/filterTests';
import { limAdminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);

  describe('Limited Admin Request Workflows - Create and Edit', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'limadminflows';
    const teamName = 'lim admin bros';

    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      LIMITED_ADMIN: `engineering-test+${testId}_limadmin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName, emails);
    });

    limAdminTests.limAdminCanCreateNewRequest();
    limAdminTests.limAdminCanEditNewRequest(teamName);
  });
});
