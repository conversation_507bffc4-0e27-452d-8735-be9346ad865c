import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  createRequestButton: 'button:contains("Create Request")',
  filtersButton: 'button:contains("Filters")',
  statusFilter: 'button:contains("Status")',
  locationFilter: 'button:contains("Location")',
  requestRow: (name) => `tr:contains("${name}")`,
  requestRowContains: (name, contains) =>
    `tr:contains("${name}") td:contains(${contains})`,
  requestCheckbox: (name) => `tr:contains("${name}") input[type="checkbox"]`,
  deleteRequestButton: '.toast-container .action',
  confirmDeleteButton: '[data-cy="Delete"]',
  searchBar: '[data-cy="search-bar"]',
  noResults: '[data-cy="No Results"]',
  resetFiltersButton: '[data-cy="components.templates.FilterBar.reset"]',
  tableHeader: 'table thead tr',
  statusFilterPending: `li:contains("Pending")`,
  statusFilterApproved: `li:contains("Approved")`,
  statusFilterDeclined: `li:contains("Declined")`,
  clearFilter: '[data-cy="Clear"]:visible',
  saveFilter: '[data-cy="Save"]:visible',
  searchLocationBox: '[data-cy="LocationPickerSearch"]',
  locationSelectAllMatching:
    '[data-cy="components.organisms.MenuList.selectAllMatching"]',
  addFilterButton: 'button:contains("Add Filter")',
  filterItem: (title) => `tr:contains("${title}"):visible`,
  requestDetailsCardHeader: 'span:contains("Request"):first',
  approveRequestButton: '[data-cy="Approve Request"]:visible',
  requestCardModal: 'div[class*="modalLarge"]',
});

export default list;
