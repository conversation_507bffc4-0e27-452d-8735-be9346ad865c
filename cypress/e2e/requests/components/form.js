import { configureSelectorProxy } from '../../../helpers';

const form = configureSelectorProxy({
  asset: '#AssetListButton',
  additionalWorkers: '[id="Additional WorkersListButton"]',
  assignedTo: '[id="Primary WorkerListButton"]',
  category: '#CategoryListButton',
  checklist: '#ChecklistsListButton',
  dateTimePicker: '.rw-datetime-picker input',
  dayOfTheMonth: (number) => `.card-content tr:contains(${number})`,
  description: '[data-cy="note"]',
  listItem: (name) => `[data-cy="${name}"].menu-list-option`,
  locationItem: (name) => `li:contains("${name}")`,
  locationOptionItem: (name) => `td:contains("${name}")`,
  location: '#LocationListButton',
  priority: '#PriorityListButton',
  priorityDropdown: '.simplebar-mask li:contains("Priority"):visible',
  status: '#StatusListButton',
  team: '#TeamListButton',
  title: '[data-cy="mainDescription"]',
  declineRequestButton: 'button:contains("Decline Request")',
  declineReasonButton: 'button:contains("Decline"):last',
  approveRequestButton: 'button:contains("Approve Request")',
  saveWithoutApprovingRequestButton:
    'button:contains("Save Without Approving")',
  submitRequestButton: 'button:contains("Submit Request")',
  requestRow: (name) => `tr:contains("${name}")`,
  requestModalHeader: '.card-header:contains("Request")',
  updatesTab: '[data-cy="generic.labels.updates"]',
  updateNotesTextArea: '.card-content textarea',
  sendUpdate: 'button:has(.icon-send)',
  updateMessage: (desc) => `.card-content:contains("${desc}")`,
  searchCategoryBox: '.dropDown [name="menu-list-search"]',
});

export default form;
