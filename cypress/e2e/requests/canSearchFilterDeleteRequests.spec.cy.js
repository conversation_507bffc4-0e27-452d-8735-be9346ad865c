import filterTests from '../../support/filterTests';
import * as requestsTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);

  describe('Request Workflows - can delete requests', () => {
    const testId = 'createDeleteRequests';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS');
    });

    requestsTests.canDeleteRequest();
  });

  describe('Request Workflows - can search and filter requests', () => {
    const testId = 'searchFilterRequests';
    const teamName = 'superTeam';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
      cy.contains('Work Orders').should('be.visible');
    });

    requestsTests.canSearchAndFilterRequestList();
  });
});
