import filterTests from '../../support/filterTests';
import * as requestsTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  describe('Request Workflows - can create/edit requests', () => {
    const testId = 'createEditRequests';
    const team = 'Cool Team';
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', team);
    });

    requestsTests.canCreateNewRequest();
    requestsTests.canEditNewRequest(team);
  });
});
