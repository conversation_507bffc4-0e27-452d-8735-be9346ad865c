import filterTests from '../../support/filterTests';
import { techTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);

  describe('Tech Request Workflows', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = `techflows${now}`;

    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      LIMITED_ADMIN: `engineering-test+${testId}_limadmin_${now}@${domain}`,
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '', emails);
    });

    techTests.techCanCreateNewRequest();
    techTests.techCannotApproveNewRequest();
    techTests.techCanSearchAndFilterRequestList();
    techTests.techCanViewUpdatesPostedInRequest();
  });
});
