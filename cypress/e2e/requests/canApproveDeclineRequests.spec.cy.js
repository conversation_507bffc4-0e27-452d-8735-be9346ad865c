import filterTests from '../../support/filterTests';
import * as requestsTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);
  describe('Request Workflows - can approve/decline Request', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'declineApproveRequests';
    const teamName = 'super team bros';

    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName, emails);
    });

    requestsTests.canApproveNewRequest();
    requestsTests.canDeclineNewRequest();
  });
});
