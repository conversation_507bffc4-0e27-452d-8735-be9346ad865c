import * as requestsPages from '../components';
import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';

const selectValue = (level) => {
  const values = ['Optional', 'Hidden', 'Required'];
  cy.wait(750);
  cy.get(`[data-cy="${values[level]}"]`).click();
};

/**
 * Navigates to request config page and set permissions with UI
 * @param { number } level - 2: required, 1: hidden, 0: optional
 */
export const setRequestPermissionWithUI = (level) => {
  upkeepPages.WORK_REQUEST_SETTINGS.go();
  requestsPages.config
    .permissionSelect('Allow Requesters to set AssetListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config
    .permissionSelect('Allow Requesters to set LocationListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config
    .permissionSelect('Allow Requesters to set Worker AssignedListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config
    .permissionSelect('Allow Requesters to set Due DateListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config
    .permissionSelect('Allow Requesters to set CategoriesListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config
    .permissionSelect('Allow Requesters to set TeamListButton')
    .click({ force: true });
  selectValue(level);

  requestsPages.config.saveButton.click({ force: true });
  cy.contains('Form Template Updated').should('be.visible');
  requestsPages.config.saveButton.click();
};

/**
 * set request permissions using the api
 * @param {string} roleId - account role ID
 * @param {string} sessionToken - session token
 * @param {string} level - 2: required , 1: optional, 0: hidden
 * @returns
 */
export const setRequestPermissionWithRequest = (
  roleId,
  sessionToken,
  level,
) => {
  return cy
    .request({
      method: 'PATCH',
      url: `${Cypress.env(
        'CYPRESS_API_URL',
      )}/api/v1/roles/${roleId}/settings/work-request`,
      body: JSON.stringify({
        assetRequestFormItemAllowed: level,
        categoryRequestFormItemAllowed: level,
        dueDateRequestFormItemAllowed: level,
        locationRequestFormItemAllowed: level,
        teamRequestFormItemAllowed: level,
        userRequestFormItemAllowed: level,
      }),
      headers: {
        'X-User-Token': sessionToken,
        'Content-Type': ' application/json',
      },
    })
    .then((result) => {
      expect(result.status).eq(200);
      upkeepPages.WORK_REQUEST_SETTINGS.go();
      cy.contains('Save').click();
    });
};

export const createPoPrerequests = (location, assetName, vendor) => {
  // At least one Worker, Team, Location and Asset are created to assign to the Requestƒ
  h.createLocation({ stringName: location }).then((loc) => {
    const objectLocation = loc?.body?.result?.id;
    h.createAsset({ objectLocation, Name: assetName }, true);
  });

  if (vendor) h.createVendor(vendor);
};
