import { upkeepPages } from '../../../../support/constants';
import * as requestsPages from '../../components';
import * as h from '../../../../helpers';

const techCanViewUpdatesPostedInRequest = () => {
  it(
    'tech can view updates posted in request',
    { testCaseId: 'QA-T6047' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const category = 'Inspection';
      const name = `new request test ${now}`;
      const description = `description before edit ${now}`;
      const adminUpdate = `tech update from ${now}`;

      // navigating immediatly to other pages breaks the session
      cy.contains('Work Orders').should('be.visible');
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'TECH', {}, sessionToken);
      });

      h.createWoRequest({
        mainDescription: name,
        note: description,
        categoryType: category,
        priorityNumber: 0,
        requiresSignature: 0,
      });

      upkeepPages.REQUESTS.go();
      requestsPages.list.requestRow(name).click();

      requestsPages.form.updatesTab.click();
      requestsPages.form.updateNotesTextArea.type(adminUpdate);
      requestsPages.form.sendUpdate.click();
      requestsPages.form.updateMessage(adminUpdate).shouldBeVisible();

      cy.visit('/');
      upkeepPages.REQUESTS.go();
      requestsPages.list.requestRow(name).click();
      requestsPages.form.updatesTab.click();
      requestsPages.form.updateMessage(adminUpdate).shouldBeVisible();
    },
  );
};

export default techCanViewUpdatesPostedInRequest;
