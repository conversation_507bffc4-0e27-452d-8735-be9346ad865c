import { upkeepPages } from '../../../../support/constants';
import * as requestsPages from '../../components';
import * as h from '../../../../helpers';

const techCannotApproveNewRequest = () => {
  it(
    'tech cannot approve a pending request',
    { testCaseId: 'QA-T6074' },
    () => {
      const now = Date.now();
      const category = 'Safety';
      const name = `tech request approve ${now}`;
      const description = `approved by tech ${now}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'TECH', {}, sessionToken);
      });
      h.createWoRequest({
        mainDescription: name,
        note: description,
        categoryType: category,
        objectAsset: null,
        priorityNumber: 0,
        requiresSignature: 0,
      });
      upkeepPages.REQUESTS.go();

      // Check that a pending request exists
      requestsPages.list.requestRow(name).shouldBeVisible();
      requestsPages.list.requestRow(name).shouldContain('pending');
      requestsPages.list.requestRow(name).click();

      // Check that request details doesn't display approve request button
      requestsPages.list.requestDetailsCardHeader.shouldContain('Request');
      requestsPages.list.approveRequestButton.shouldNotExist();
    },
  );
};

export default techCannotApproveNewRequest;
