import { upkeepPages } from '../../../../support/constants';
import * as requestsPages from '../../components';
import * as h from '../../../../helpers';

const techCanSearchAndFilterRequestList = () => {
  it(
    'tech can search and filter the Requests list',
    { testCaseId: 'QA-T6054' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const toCreate = 3;
      const requestTitle = `search request ${now} `;
      const location = `location ${now}`;
      h.createLocation({ stringName: location });

      for (let i = 0; i < toCreate; i++) {
        const createLocation = `${location} ${i}`;
        h.createLocation({ stringName: createLocation }).then((loc) => {
          const objectLocation = loc?.body?.result?.id;
          const data = {
            mainDescription: `${requestTitle}${i}`,
            note: '',
            objectAsset: null,
            objectLocationForWorkOrder: objectLocation,
            priorityNumber: i,
            requiresSignature: 0,
          };

          h.createWoRequest(data).then((result) => {
            const requestId = result.body.result.id;

            // Decline Request
            if (i === 1) {
              h.cancelWoRequest(requestId, window.localStorage.authToken, {
                reasonForCancellation: 'Declined by Cypress Test',
              });
            }

            // Approve Request
            if (i === 0) {
              h.approveWoRequest(requestId);
            }
          });
        });
      }

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'LIMITED_ADMIN', {}, sessionToken);
      });
      upkeepPages.REQUESTS.go();

      // Searching by name
      // From the Requests list, type some text into the search bar
      requestsPages.list.searchBar.type(now);
      cy.wait(650);

      // In the case where the text entered is contained in the Title of at least 1 Request, then all Requests where the search term is contained in the name are returned in the list
      requestsPages.list.requestRow(requestTitle).shouldHaveLength(toCreate);

      // In the case where the text entered is not contained in the Title of at least 1 Request, then the list should return its empty state
      requestsPages.list.searchBar.type('qwerty');
      cy.wait(1500);
      requestsPages.list.noResults.shouldBeVisible();
      requestsPages.list.resetFiltersButton.click(); // clear state
      requestsPages.list.tableHeader.shouldBeVisible();
      requestsPages.list.requestRow(requestTitle).shouldHaveLength(toCreate);

      // Filtering by Status
      // From the Requests list, open the Status filter
      requestsPages.list.statusFilter.click();
      // Select 1 or many statuses from the picker
      requestsPages.list.statusFilterApproved.shouldBeVisible();
      requestsPages.list.statusFilterDeclined.shouldBeVisible();
      requestsPages.list.statusFilterPending.shouldBeVisible();

      requestsPages.list.statusFilterPending.click();
      cy.wait(650);
      requestsPages.list.statusFilterApproved.click();
      requestsPages.list.statusFilterDeclined.click();
      requestsPages.list.saveFilter.click();

      // If any Requests match the selected status(es), then those should be returned in the list
      requestsPages.list.requestRow(requestTitle).shouldHaveLength(toCreate);
      // If no Requests match the selected status(es), then the list should return its empty state
      requestsPages.list.statusFilter.click();
      requestsPages.list.clearFilter.click();
      requestsPages.list.statusFilterPending.click();
      requestsPages.list.saveFilter.click();
      requestsPages.list.requestRow('Pending').shouldHaveLength(0);

      // Filtering by Location
      requestsPages.list.resetFiltersButton.click(); // clear state
      requestsPages.list.tableHeader.shouldBeVisible();
      requestsPages.list.requestRow(requestTitle).shouldHaveLength(toCreate);

      // From the Requests list, open the Location filter
      // Select 1 or many locations from the picker
      requestsPages.list.locationFilter.click();
      cy.get('.picker').within(() => {
        requestsPages.list.searchLocationBox.type(`${now} 0`);
        cy.wait(650);
        requestsPages.form
          .locationOptionItem(`location ${now} 0`)
          .shouldHaveLength(1);
        requestsPages.form.locationOptionItem(`location ${now} 0`).click();
        requestsPages.list.saveFilter.click();
      });

      // If any Requests are assigned to the Location(s) selected, then those should be returned in the list
      // If no Requests are assigned to the Locations(s) selected, then the list should return its empty state
      requestsPages.form
        .locationOptionItem(`location ${now} `)
        .shouldHaveLength(1);

      // Resetting filters
      requestsPages.list.resetFiltersButton.click(); // clear state
      requestsPages.list.tableHeader.shouldBeVisible();
      requestsPages.list.requestRow(requestTitle).shouldHaveLength(toCreate);

      // From the Requests list, add at least one filter and one search that return a Request
      requestsPages.list.filtersButton.click();
      cy.wait(500);
      requestsPages.list.addFilterButton.click();
      // Error: ResizeObserver loop limit exceeded
      Cypress.on('uncaught:exception', () => {
        return false;
      });
      requestsPages.form.priorityDropdown.click();
      requestsPages.form.priority.click();
      requestsPages.form.listItem('Low').click();
      h.closeTopPortal();
      cy.contains('Apply').click();

      requestsPages.list.statusFilter.click();
      requestsPages.list.statusFilterDeclined.click();
      requestsPages.list.saveFilter.click();

      requestsPages.list.requestRowContains(now, 'low').shouldHaveLength(1);

      // Take the Reset action in the Filter bar
      // All filters should be removed, the search should be cleared. All Requests should now be visible in the list.
      requestsPages.list.resetFiltersButton.click();
      requestsPages.list.tableHeader.shouldBeVisible();
      requestsPages.list.requestRow(now).shouldHaveLength(toCreate);
    },
  );
};

export default techCanSearchAndFilterRequestList;
