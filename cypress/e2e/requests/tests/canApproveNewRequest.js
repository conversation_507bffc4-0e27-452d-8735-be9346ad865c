import { upkeepPages } from '../../../support/constants';
import * as requestsPages from '../components';
import * as h from '../../../helpers';

const canApprovePendingRequest = () => {
  it('Can approve a pending request', { testCaseId: 'QA-T6321' }, () => {
    const now = Date.now();
    const category = 'Safety';
    const name = `approve request test ${now}`;
    const description = `approve req ${now}`;

    h.createWoRequest({
      mainDescription: name,
      note: description,
      categoryType: category,
      objectAsset: null,
      priorityNumber: 0,
      requiresSignature: 0,
    });

    upkeepPages.REQUESTS.go();

    // Check that a pending request exists
    requestsPages.list.requestRow(name).shouldBeVisible();
    requestsPages.list.requestRow(name).shouldContain('pending');
    requestsPages.list.requestRow(name).click();

    // Check that request details shows and approve request
    requestsPages.list.requestDetailsCardHeader.shouldContain('Request');
    requestsPages.list.approveRequestButton.click();

    // Verify that modal closes after approval
    requestsPages.list.requestCardModal.shouldNotExist();

    // Verify request has been approved
    requestsPages.list.statusFilter.shouldBeVisible();
    requestsPages.list.statusFilter.click();

    requestsPages.list.statusFilterApproved.click();
    requestsPages.list.saveFilter.click();
    requestsPages.list.requestRow(name).shouldBeVisible();
    requestsPages.list.requestRowContains(name, 'approved').shouldBeVisible();
    requestsPages.list.requestRow(name).click();
    requestsPages.list.requestDetailsCardHeader.shouldContain('Request');
    requestsPages.list.approveRequestButton.shouldNotExist();
    cy.contains('This request was approved and turned into Work Order').should(
      'exist',
    );
  });
};

export default canApprovePendingRequest;
