import { upkeepPages } from '../../../support/constants';
import * as requestsPages from '../components';
import * as h from '../../../helpers';

const canDeclineNewRequest = () => {
  it('Decline a pending request', { testCaseId: 'QA-T6315' }, () => {
    const now = Date.now();
    const category = 'Safety';
    const name = `decline request test ${now}`;
    const description = `declined req ${now}`;

    h.createWoRequest({
      mainDescription: name,
      note: description,
      categoryType: category,
      objectAsset: null,
      priorityNumber: 0,
      requiresSignature: 0,
    });

    upkeepPages.REQUESTS.go();
    requestsPages.list.requestRow(name).shouldBeVisible();
    requestsPages.list.requestRowContains(name, category).shouldExist();
    requestsPages.list
      .requestRowContains(name, 'none')
      .should('exist', { matchCase: false });
    requestsPages.list.requestRowContains(name, 'pending').shouldExist();

    requestsPages.list.requestRow(name).click();

    requestsPages.form.declineRequestButton.click();
    requestsPages.form.declineReasonButton.click();

    // Verify that modal closes after declining
    requestsPages.list.requestCardModal.shouldNotExist();

    requestsPages.list.statusFilter.shouldBeVisible();
    requestsPages.list.statusFilter.click();
    requestsPages.list.statusFilterDeclined.click();
    requestsPages.list.saveFilter.click();

    requestsPages.list.requestRow(name).shouldExist();
    requestsPages.list.requestRowContains(name, category).shouldExist();
    requestsPages.list.requestRowContains(name, 'declined').shouldExist();

    requestsPages.list.requestRow(name).click();
    cy.contains('This request was declined').should('exist');
  });
};

export default canDeclineNewRequest;
