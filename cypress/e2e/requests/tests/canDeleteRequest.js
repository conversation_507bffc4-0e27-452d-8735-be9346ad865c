import { upkeepPages } from '../../../support/constants';
import * as requestsPages from '../components';
import * as h from '../../../helpers';

const canDeleteRequest = () => {
  it('Delete a pending request', {}, () => {
    const now = Date.now();
    const category = 'Safety';
    const name = `delete request test ${now}`;
    const description = `delete req ${now}`;

    h.createWoRequest({
      mainDescription: name,
      note: description,
      categoryType: category,
      objectAsset: null,
      priorityNumber: 0,
      requiresSignature: 0,
    });

    upkeepPages.REQUESTS.go();
    requestsPages.list.requestRow(name).shouldBeVisible();
    requestsPages.list.requestRowContains(name, category).shouldExist();
    requestsPages.list
      .requestRowContains(name, 'none')
      .should('exist', { matchCase: false });
    requestsPages.list.requestRowContains(name, 'pending').shouldExist();

    requestsPages.list.requestCheckbox(name).click();

    requestsPages.list.deleteRequestButton.click();
    requestsPages.list.confirmDeleteButton.click();

    requestsPages.list.requestRow(name).shouldNotExist();
  });
};

export default canDeleteRequest;
