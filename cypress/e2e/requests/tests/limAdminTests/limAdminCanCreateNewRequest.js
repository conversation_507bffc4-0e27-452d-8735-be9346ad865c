import { leftNavigation } from '../../../../support/constants';
import * as requestsPages from '../../components';

const limAdminCanCreateNewRequest = () => {
  it(
    'limited admin can create a new request',
    { testCaseId: 'QA-T6045' },
    () => {
      const now = Date.now();
      const priority = {
        high: 'High',
        medium: 'Medium',
        low: 'Low',
      };
      const name = `limadmin creates request ${now}`;
      const description = `description limadmin ${now}`;

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'LIMITED_ADMIN', {}, sessionToken);
      });

      cy.get(leftNavigation.REQUESTS.navSelector).click();

      // From the Request list, click the button “Create Request”
      requestsPages.list.createRequestButton.click();

      // Fill in all fields in the form
      requestsPages.form.title.type(name);
      requestsPages.form.description.type(description);

      // Priority should be a single select input (High, Medium, Low, None)
      requestsPages.form.priority.click();
      requestsPages.form.listItem(priority.high).shouldExist();
      requestsPages.form.listItem(priority.low).shouldExist();
      requestsPages.form.listItem(priority.medium).click();

      // Clicking “Submit Request” should create a new request
      requestsPages.form.submitRequestButton.click();

      // User is returned to the Request list
      // User can see the newly created Request with all details in the list
      requestsPages.list.requestRow(name).shouldExist();
      requestsPages.list
        .requestRowContains(name, 'medium')
        .should('exist', { matchCase: false });
    },
  );
};

export default limAdminCanCreateNewRequest;
