import { upkeepPages } from '../../../support/constants';
import * as requestsPages from '../components';
import * as helpers from '../helpers';

const canApplyRequestConfigToForm = ({
  location,
  assetName,
  teamName,
  vendor,
}) => {
  it(
    'Ensure request config Required is applied to the form',
    { testCaseId: 'QA-T6317' },
    () => {
      const now = Date.now();
      const name = `required config ${now}`;
      const description = `description ${now}`;
      const dayOfTheMonth = new Date().getDate();
      const category = 'Electrical';
      const priority = {
        high: 'High',
        medium: 'Medium',
        low: 'Low',
      };

      // Can't create request with missing required fields
      helpers.setRequestPermissionWithUI('2');
      upkeepPages.REQUESTS.go();

      requestsPages.list.createRequestButton.click();
      requestsPages.form.title.type(name);
      requestsPages.form.description.type(description);

      requestsPages.form.submitRequestButton.shouldBeDisabled();
      requestsPages.form.priority.click();
      requestsPages.form.listItem(priority.high).shouldExist();
      requestsPages.form.listItem(priority.low).shouldExist();
      requestsPages.form.listItem(priority.medium).click();

      // Due Date should be a date picker input
      requestsPages.form.dateTimePicker.click();
      requestsPages.form
        .dayOfTheMonth(dayOfTheMonth)
        .click({ scrollBehavior: 'bottom' });

      // Category should be a single select input from preset Category options
      requestsPages.form.category.click({ scrollBehavior: 'bottom' });
      requestsPages.form.listItem(category).click();

      // Location, Asset, Assigned to should be single select inputs from existing Locations/Assets/Users
      requestsPages.form.location.click();
      requestsPages.form.listItem(location).click({ scrollBehavior: 'bottom' });
      requestsPages.form.asset.click();
      requestsPages.form.listItem(assetName).click();

      // View Only and Requester users should not be included in Workers
      requestsPages.form.assignedTo.click();
      requestsPages.form.listItem('_requester_').shouldNotExist();
      requestsPages.form.listItem('_viewonly_').shouldNotExist();
      requestsPages.form.listItem(vendor).shouldExist();
      requestsPages.form.listItem('_tech_').click();

      requestsPages.form.additionalWorkers.click();
      requestsPages.form.listItem('_requester_').shouldNotExist();
      requestsPages.form.listItem('_viewonly_').shouldNotExist();
      // Vendors should be included in Workers
      requestsPages.form.listItem(vendor).shouldExist();
      requestsPages.form
        .listItem('_limitedadmin_')
        .click({ scrollBehavior: 'bottom', force: true });

      // Teams should be a multi select input from existing Teams in the account
      requestsPages.form.team.click({ scrollBehavior: 'bottom' });
      requestsPages.form.listItem(teamName).click({ scrollBehavior: 'bottom' });

      // Clicking “Submit Request” should create a new request
      requestsPages.form.submitRequestButton.click();
      requestsPages.list.requestRow(name).shouldExist();
      requestsPages.list.requestRowContains(name, category).shouldExist();
      requestsPages.list
        .requestRowContains(name, 'medium')
        .should('exist', { matchCase: false });
      requestsPages.list.requestRowContains(name, location).shouldExist();

      // hide permissions
      const hiddenName = `hidden config ${now}`;
      helpers.setRequestPermissionWithUI('0');
      upkeepPages.REQUESTS.go();

      requestsPages.list.createRequestButton.click();
      requestsPages.form.title.type(hiddenName);
      requestsPages.form.description.type(description);

      requestsPages.form.submitRequestButton.shouldBe('enabled');
      requestsPages.form.dateTimePicker.shouldExist();
      requestsPages.form.category.shouldExist();
      requestsPages.form.location.shouldExist();
      requestsPages.form.assignedTo.shouldExist();
      requestsPages.form.additionalWorkers.shouldExist();
      requestsPages.form.team.shouldExist();

      // Clicking “Submit Request” should create a new request
      requestsPages.form.submitRequestButton.click();
      requestsPages.list.requestRow(hiddenName).shouldExist();
    },
  );
};

export default canApplyRequestConfigToForm;
