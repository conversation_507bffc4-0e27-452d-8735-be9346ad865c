import { upkeepPages } from '../../../support/constants';
import { createPoPrerequests } from '../helpers';
import * as requestsPages from '../components';
import * as h from '../../../helpers';

const canEditNewRequest = (teamName) => {
  it('Edit a pending request', { testCaseId: 'QA-T6314' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const location = `location ${now}`;
    const assetName = `asset ${now}`;
    const category = 'Inspection';
    const priority = {
      high: 'High',
      medium: 'Medium',
      low: 'Low',
    };
    const name = `new request test ${now}`;
    const description = `description before edit ${now}`;
    const permissionOptional = '0';

    const requestEdit = {
      name: `edit request ${now}`,
      description: 'edit description',
      category: 'Electrical',
    };

    // navigating immediatly to other pages breaks the session
    cy.contains('Work Orders').should('be.visible');
    createPoPrerequests(location, assetName, '', permissionOptional);

    h.createWoRequest({
      mainDescription: name,
      note: description,
      categoryType: category,
      objectAsset: null,
      priorityNumber: 0,
      requiresSignature: 0,
    });

    upkeepPages.REQUESTS.go();
    requestsPages.list.requestRow(name).shouldBeVisible();
    requestsPages.list.requestRowContains(name, category).shouldExist();
    requestsPages.list
      .requestRowContains(name, 'none')
      .should('exist', { matchCase: false });
    requestsPages.list.requestRowContains(name, 'pending').shouldExist();

    requestsPages.list.requestRow(name).click();

    requestsPages.form.title.type(requestEdit.name, { scrollTo: false });
    requestsPages.form.description.type(requestEdit.description, {
      scrollTo: false,
    });

    requestsPages.form.priority.click({ scrollBehavior: 'bottom' });
    requestsPages.form.listItem(priority.high).shouldExist();
    requestsPages.form.listItem(priority.low).shouldExist();
    requestsPages.form.listItem(priority.high).click();
    // h.closeTopPortal();

    requestsPages.form.category.click({
      scrollBehavior: 'bottom',
    });

    requestsPages.form.searchCategoryBox.type(requestEdit.category);
    requestsPages.form
      .listItem(requestEdit.category)
      .click({ scrollBehavior: 'bottom' });

    requestsPages.form.requestModalHeader.click({ force: true });
    requestsPages.form.location.click({ scrollBehavior: 'bottom' });
    requestsPages.form.listItem(location).click({ scrollBehavior: 'bottom' });
    requestsPages.form.requestModalHeader.click({ force: true });

    requestsPages.form.location.scrollIntoView();
    requestsPages.form.asset.click();
    requestsPages.form.listItem(assetName).click();
    requestsPages.form.requestModalHeader.click({ force: true });

    requestsPages.form.team.click({ scrollBehavior: 'bottom' });
    requestsPages.form.listItem(teamName).click({ scrollBehavior: 'bottom' });
    requestsPages.form.requestModalHeader.click({ force: true });

    requestsPages.form.saveWithoutApprovingRequestButton.click();

    requestsPages.list.requestRow(requestEdit.name).shouldExist();
    requestsPages.list
      .requestRowContains(requestEdit.name, requestEdit.category)
      .shouldExist();
    requestsPages.list
      .requestRowContains(requestEdit.name, 'high')
      .should('exist', { matchCase: false });
    requestsPages.list
      .requestRowContains(requestEdit.name, location)
      .shouldExist();
    requestsPages.list
      .requestRowContains(requestEdit.name, 'pending')
      .shouldExist();
  });
};

export default canEditNewRequest;
