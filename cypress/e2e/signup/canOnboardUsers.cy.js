import filterTests from '../../support/filterTests';
import { upkeepPages } from '../../support/constants';

filterTests(['all', 'tier2', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);
  describe('Signup Users', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const teamName = 'super awesome team';

    it(
      'should allow user to signup to upkeep',
      { testCaseId: 'QA-T6329' },
      () => {
        const runnableEnvs = ['staging', 'staging3', 'production'];
        const env = Cypress.env('CYPRESS_ENV');
        if (!runnableEnvs.includes(env)) {
          cy.state('runnable').ctx.skip();
        }

        upkeepPages.LOGIN.go();
        const now = Date.now();
        const testId = `userSignUp${now}`;

        const emails = {
          ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
          LIMITED_ADMIN: `engineering-test+${testId}_limitedAdmin_${now}@${domain}`,
          REQUESTER: `engineering-test+${testId}_requester_${now}@${domain}`,
          TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
          VIEW_ONLY: `engineering-test+${testId}_viewonly_${now}@${domain}`,
        };

        cy.createOrLoginAdmin(
          testId,
          ['LIMITED_ADMIN', 'REQUESTER', 'TECH', 'VIEW_ONLY'],
          'BUSINESS_PLUS',
          teamName,
          emails,
        );

        cy.onboardUsersFromEmail({ email: emails.LIMITED_ADMIN });
        cy.onboardUsersFromEmail({ email: emails.REQUESTER });
        cy.onboardUsersFromEmail({ email: emails.TECH });
        cy.onboardUsersFromEmail({ email: emails.VIEW_ONLY });
      },
    );
  });
});
