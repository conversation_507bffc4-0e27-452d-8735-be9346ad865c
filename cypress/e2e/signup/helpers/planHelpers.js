import { findInIframe, getNestedIframe } from '.';
import * as signupPages from '../components';

// GLOBAL VARIABLES
const { checkout } = signupPages.checkout;
const iframe = 'https://upkeep-sandbox.subscription-suite.io/shop/';
const creditCardPaymentIframe = 'https://apisandbox.zuora.com/apps/';

// Select a plan
export const selectPlan = (planType) => {
  cy.wait(5500);
  findInIframe(iframe, checkout.planAnnualIncreaseButton(planType)).click({
    scrollBehavior: 'bottom',
  });
  findInIframe(iframe, checkout.goToCheckoutButton).click({
    scrollBehavior: 'bottom',
  });
  findInIframe(iframe, checkout.cartGoToCheckoutButton).click({
    scrollBehavior: 'bottom',
  });
};

// Select credit card for payment
export const selectCreditCardPayment = () => {
  findInIframe(iframe, checkout.paymentCreditCard).click({
    scrollBehavior: 'bottom',
  });
};

// Input credit card information
export const inputCreditCard = (user) => {
  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.cardNumberInput)
    .type(user.creditCard);
  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.cardCvv)
    .type(user.cvv);

  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.expirationMonth)
    .select('10');
  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.expirationYear)
    .select('2043');

  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.cardHolderName)
    .type(`${user.firstName} ${user.lastName}`);
  getNestedIframe(iframe, creditCardPaymentIframe)
    .find(checkout.creditCardCountry)
    .select('United States');
};

// Input billing address infortmation
export const inputBillingAddress = (user) => {
  findInIframe(iframe, checkout.firstNameInput).type(user.firstName);
  findInIframe(iframe, checkout.lastNameInput).type(user.lastName);
  findInIframe(iframe, checkout.companyNameInput).type(user.company);
  findInIframe(iframe, checkout.addressLineInput).type(user.address);
  findInIframe(iframe, checkout.cityInput).type(user.city);
  findInIframe(iframe, checkout.zipCodeInput).type(user.postalCode);

  // Select country
  findInIframe(iframe, checkout.countrySelect).click();
  findInIframe(iframe, `mat-option:contains("${user.country}")`).first().click({
    scrollBehavior: 'bottom',
  });

  // Select State
  findInIframe(iframe, checkout.stateSelect).click();
  findInIframe(iframe, `mat-option:contains("${user.state}")`).first().click({
    scrollBehavior: 'bottom',
  });
};

// Confirm purchase or plan
export const confirmPurchase = () => {
  findInIframe(iframe, checkout.termsAndConditionsBox).click({
    scrollBehavior: 'bottom',
  });
  findInIframe(iframe, checkout.confirmPurchaseButton).click({
    scrollBehavior: 'bottom',
  });
};

// Verify user can see confirmation page
export const verifyPurchaseSuccessful = () => {
  // Verify confirmation page shows
  findInIframe(iframe, checkout.orderConfirmedHeader).should('be.visible', {
    timeout: 90_000,
  });
};

export const verifyPurchaseUnsuccessful = () => {
  findInIframe(iframe, checkout.errorLabelNotification).should('be.visible');
};
