import { getIframeBody } from '../../../helpers/iframeHelpers';

export const findInIframe = (src, selector) => {
  return getIframeBody(src).find(selector);
};

export const getInIframe = (src, selector) => {
  return getIframeBody(src).get(selector);
};

export const getNestedIframe = (parentIframe, nestedIframe) => {
  return cy
    .get(`iframe[src^="${parentIframe}"]`)
    .its('0.contentDocument')
    .find(`iframe[src^="${nestedIframe}"]`, { timeout: 60_000 })
    .its('0.contentDocument')
    .its('body')
    .should('not.be.empty');
};
