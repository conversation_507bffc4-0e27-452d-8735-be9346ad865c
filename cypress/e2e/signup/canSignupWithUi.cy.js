import filterTests from '../../support/filterTests';
import { upkeepPages } from '../../support/constants';
import * as signUpPages from './components';

filterTests(['all', 'smoke', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);
  describe('Signup User with Ui', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');

    it('can sign up with ui', { testCaseId: 'QA-T5' }, () => {
      upkeepPages.SIGNUP.go();
      const now = Date.now();
      const testId = 'signUpWithUi';
      const user = {
        password: Cypress.env('PASSWORD'),
        email: `engineering-test+${testId}_admin_${now}@${domain}`,
        firstName: 'cool',
        lastName: 'tester',
        mobile: '4251234567',
        company: 'test corp',
        title: 'senior junior tester',
      };

      signUpPages.signUp.workEmail.type(user.email);
      signUpPages.signUp.password.type(user.password);
      signUpPages.signUp.firstName.type(user.firstName);
      signUpPages.signUp.lastName.type(user.lastName);
      signUpPages.signUp.mobilePhone.type(user.mobile);
      signUpPages.signUp.company.type(user.company);
      signUpPages.signUp.title.type(user.title);
      signUpPages.signUp.startUsingUpkeepButton.click();

      cy.contains('Your trial ends in 13 days').should('be.visible', {
        timeout: 75_000,
      });
      signUpPages.signUp.purchaseLink.shouldBeVisible();

      cy.logout();

      cy.login(user.email, user.password);
      cy.contains('Work Orders').should('be.visible');

      signUpPages.signUp.purchaseLink.shouldBeVisible();
      cy.contains('Your trial ends in 13 days').should('be.visible', {
        timeout: 75_000,
      });
    });
  });
});
