import filterTests from '../../support/filterTests';
import * as signupTests from './tests';

filterTests(['all', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);

  describe(
    'User is directed to an error page when user unsuccesfully purchases a plan',
    {
      retries: {
        runMode: 0,
      },
    },
    () => {
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const now = Date.now();
      const testId = 'purchasePlan';
      const user = {
        email: `engineering-test+${testId}_admin_${now}@${domain}`,
        firstName: 'Bart',
        lastName: 'Simpson',
        company: 'Test Corp',
        address: '123 Test St',
        city: 'Brothers',
        state: 'Oregon',
        country: 'United States',
        postalCode: '12345',
        creditCard: '****************',
        cvv: '123',
      };

      afterEach(() => {
        Cypress.session.clearAllSavedSessions();
      });

      signupTests.canUnsuccessfullyPurchaseBusinessPlus(testId, user);
      signupTests.canUnsuccessfullyPurchaseProfessional(testId, user);
      signupTests.canUnsuccessfullyPurchaseStarter(testId, user);
    },
  );
});
