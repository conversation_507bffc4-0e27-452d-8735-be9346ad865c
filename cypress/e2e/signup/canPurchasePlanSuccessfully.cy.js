import filterTests from '../../support/filterTests';
import * as signupTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  Cypress.on('uncaught:exception', () => false);

  describe(
    'User is directed to a confirmation page when user succesfully purchases a plan',
    {
      retries: {
        runMode: 0, // these tests are taking over 14 minutes in gh
      },
    },
    () => {
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const now = Date.now();
      const testId = 'purchasePlan';
      const user = {
        email: `engineering-test${now}@${domain}`,
        firstName: 'Bart',
        lastName: 'Simpson',
        company: 'Test Corp',
        address: '27 Fredrick Ave',
        city: 'Brothers',
        state: 'Oregon',
        country: 'United States',
        postalCode: '97712',
        creditCard: '****************',
        cvv: '123',
      };

      afterEach(() => {
        Cypress.session.clearAllSavedSessions();
      });

      signupTests.canSuccessfullyPurchaseBusinessPlus(testId, user);
      signupTests.canSuccessfullyPurchaseProfessional(testId, user);
      signupTests.canSuccessfullyPurchaseStarter(testId, user);
    },
  );
});
