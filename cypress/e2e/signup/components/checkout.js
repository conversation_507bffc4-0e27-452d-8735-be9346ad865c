import { configureSelectorProxy } from '../../../helpers';

export const checkout = {
  choosePlanHeader:
    '[data-cy="label-shop-header"]:contains("Choose the plan that is right for your team")',
  billingTermAnnual: '[data-cy="radio-shop-billing-annual"]',
  planAnnualIncreaseButton: (planType) =>
    `[data-cy="card-shop"]:contains("${planType} - Annually") [data-cy="quantity-increment"]`,
  goToCheckoutButton: '[data-cy="button-shop-go-to-checkout"]',
  cartGoToCheckoutButton: '[data-cy="button-cart-go-to-checkout"]',
  // payment form
  firstNameInput: '[id="firstName"]',
  lastNameInput: '[id="lastName"]',
  emailInput: '[id="email"]',
  companyNameInput: '[id="companyName"]',
  addressLineInput: '[id="street"]',
  zipCodeInput: '[id="zipCode"]',
  cityInput: '[id="city"]',
  countrySelect: '[id="country"]',
  stateSelect: '[id="state"]',
  paymentCreditCard:
    '[data-cy="list-payment-method-type"] li:contains("Credit card")',
  cardNumberInput: '[id="input-creditCardNumber"]',
  cardHolderName: '[id="input-creditCardHolderName"]',
  cardCvv: '[id="input-cardSecurityCode"]',
  expirationMonth: '[id="input-creditCardExpirationMonth"]',
  expirationYear: '[id="input-creditCardExpirationYear"]',
  creditCardCountry: '[id="input-creditCardCountry"]',
  submitButton: '[id="submitButton"]',
  termsAndConditionsBox: '[for="terms"]',
  confirmPurchaseButton: 'button:contains("Confirm purchase")',
  orderConfirmedHeader: '[data-cy="label-checkout-done-header"]',
  continueCheckoutButton: '[data-cy="button-checkout-continue"] button',
  continueToCompletePaymentButton:
    '[data-cy="button-checkout-payment-method"] button',
  errorLabelNotification: '[type="error"] [data-cy="label-notification"]',
};

export const checkoutProxy = configureSelectorProxy(checkout);
