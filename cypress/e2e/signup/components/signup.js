import { configureSelectorProxy } from '../../../helpers';

const signUp = configureSelectorProxy({
  workEmail: '[id="signup_username"]',
  password: '[id="signup_password"]',
  firstName: '[id="signup_firstname"]',
  lastName: '[id="signup_lastname"]',
  mobilePhone: '[id="signup_companyPhone"]',
  company: '[id="signup_companyname"]',
  title: '[id="signup_jobTitleLabel"]',
  startUsingUpkeepButton: 'button:contains("Start using UpKeep")',
  purchaseLink: 'a[href="/web/subscription/checkout"]',
});

export default signUp;
