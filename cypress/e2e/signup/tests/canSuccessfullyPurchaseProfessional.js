import { upkeepPages } from '../../../support/constants';
import * as planHelpers from '../helpers/planHelpers';

const canSuccessfullyPurchaseProfessional = (testId, user) => {
  it(
    'User is directed to a confirmation page when user successfully purchases a Professional plan',
    { testCaseId: 'QA-T2155', defaultCommandTimeout: 90_000 },
    () => {
      const runnableEnvs = ['staging3', 'staging'];
      const env = Cypress.env('CYPRESS_ENV');
      if (!runnableEnvs.includes(env)) {
        cy.state('runnable').ctx.skip();
      }

      const plan = 'Professional';

      cy.createOrLoginAdmin(testId);
      upkeepPages.SUBSCRIPTION_URL.go();
      cy.contains('Subscription').should('be.visible');

      planHelpers.selectPlan(plan);
      planHelpers.inputBillingAddress(user);
      planHelpers.selectCreditCardPayment();
      planHelpers.inputCreditCard(user);
      planHelpers.confirmPurchase();
      planHelpers.verifyPurchaseSuccessful();
    },
  );
};

export default canSuccessfullyPurchaseProfessional;
