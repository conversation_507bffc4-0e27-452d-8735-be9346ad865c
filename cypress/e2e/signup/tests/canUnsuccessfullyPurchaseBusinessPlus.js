import { AVAILABLE_PLANS_DISPLAY_NAMES } from '../../../support/planConstants';
import { upkeepPages } from '../../../support/constants';
import * as planHelpers from '../helpers/planHelpers';

const canUnsuccessfullyPurchaseBusinessPlus = (testId, user) => {
  it(
    `User is directed to a confirmation page when user unsuccessfully purchases a ${AVAILABLE_PLANS_DISPLAY_NAMES.BUSINESS_PLUS} plan`,
    { testCaseId: 'QA-T2159' },
    () => {
      const runnableEnvs = ['staging3', 'staging'];
      const env = Cypress.env('CYPRESS_ENV');
      if (!runnableEnvs.includes(env)) {
        cy.state('runnable').ctx.skip();
      }
      const plan = AVAILABLE_PLANS_DISPLAY_NAMES.BUSINESS_PLUS;

      cy.createOrLoginAdmin(testId);
      upkeepPages.SUBSCRIPTION_URL.go();
      cy.contains('Subscription').should('be.visible');

      planHelpers.selectPlan(plan);
      planHelpers.inputBillingAddress(user);
      planHelpers.selectCreditCardPayment();
      planHelpers.inputCreditCard(user);
      planHelpers.confirmPurchase();
      planHelpers.verifyPurchaseUnsuccessful();
    },
  );
};

export default canUnsuccessfullyPurchaseBusinessPlus;
