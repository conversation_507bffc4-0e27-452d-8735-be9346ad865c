import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'tier2'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('Create / Edit Part without multiple lines inventory', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const description = `description ${now}`;
    const imageFile = 'cypress/fixtures/cat.jpeg';
    const partName = `part ${now}`;
    const testId = 'partWithoutInventoryCreate';
    const teamId = 'super awesome team';
    const teamName = 'super awesome team';
    const teamName2 = 'team super awesome 2';

    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      LIMITED_ADMIN: `engineering-test+${testId}_limitedAdmin_${now}@${domain}`,
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    };

    before(() => {
      cy.createOrLoginAdmin(
        testId,
        [],
        'BUSINESS_PLUS',
        teamId,
        emails,
        teamName,
      );

      // toggle Enable Parts with multiple inventory lines off
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        const user = JSON.parse(window.localStorage.getItem('currentUser'));
        const role = user.roleId;

        cy.requestTogglePartsMultipleInventoryLines(
          { displayHierarchicalParts: false },
          role,
        );
        cy.switchUser(
          testId,
          'LIMITED_ADMIN',
          {
            email: emails.LIMITED_ADMIN,
            firstName: 'Cool',
            lastName: 'LimitedAdmin',
          },
          sessionToken,
        );
        cy.switchUser(
          testId,
          'TECH',
          {
            email: emails.TECH,
            firstName: 'Cool',
            lastName: 'Tech',
          },
          sessionToken,
        );
      });
    });

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS');
    });

    const testData = {
      description,
      partName,
      now,
      teamName,
      teamName2,
      imageFile,
    };
    partsTests.canEditPart(testData);
    partsTests.canCreateNewPart(testData);
    partsTests.canDeletePart(now);
  });
});
