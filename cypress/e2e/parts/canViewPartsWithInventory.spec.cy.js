import faker from 'faker';
import filterTests from '../../support/filterTests';
import * as tests from './tests';
import { upkeepPages } from '../../support/constants';

filterTests(['all', 'smoke'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);

  describe('View Part with multiple Inventory Lines', () => {
    const partName = 'Intelligent Metal Pants';
    const noLocationQuantity = faker.datatype.number({ min: 1, max: 10 });
    const testId = 'viewPartWithInventory';

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH', 'LIMITED_ADMIN'],
        'BUSINESS_PLUS',
        'super awesome team',
      );

      upkeepPages.PARTS.go();
      cy.contains('Create Part').should('be.visible');
    });

    const testData = {
      partName,
      noLocationQuantity,
    };
    tests.viewInventoryLines(testData);
    tests.editInventoryLine(testData);
    tests.galleryView(testData);
  });
});
