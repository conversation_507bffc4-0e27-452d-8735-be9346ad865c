import faker from 'faker';
import filterTests from '../../support/filterTests';
import * as tests from './tests';
import { upkeepPages } from '../../support/constants';

filterTests(['all', 'smoke'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);

  describe('View Part with multiple Inventory Lines', () => {
    const partName = 'Metal Gear Pants';
    const noLocationQuantity = faker.datatype.number({ min: 1, max: 10 });

    const now = Date.now();

    const testId = `addTransferInventory${now}`;

    const location = 'Location 1';
    const location2 = `Location ${now}`;

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH', 'LIMITED_ADMIN'],
        'BUSINESS_PLUS',
        'super awesome team',
      );

      upkeepPages.PARTS.go();
      cy.contains('Create Part').should('be.visible');
    });

    const testData = {
      partName,
      noLocationQuantity,
      location,
      location2,
    };
    tests.addInventoryLine(testData);
    tests.addInventoryAdjustment(testData);
    tests.transferQuantity(testData);
    tests.cannotTransferToSameInventory(testData);
  });
});
