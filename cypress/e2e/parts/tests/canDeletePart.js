import { upkeepPages } from '../../../support/constants';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const canDeletePart = (now) => {
  it('Admin can delete a part', { testCaseId: 'QA-T40' }, () => {
    const partName = `Delete Part ${now}`;

    // Create a new basic part
    h.createPart({ partName }, true);
    upkeepPages.PARTS.go();

    // Part should exist in Parts list
    partsPages.list.partRow(partName).shouldExist();
    partsPages.list.partRow(partName).click();

    // Part name is correct
    partsPages.details.nameRow.shouldContain(partName);

    // Click Delete Part
    partsPages.details.optionsButton.click();
    partsPages.details.deleteButton.click();
    partsPages.details.confirmDeleteButton.click();

    // Confirm part deleted -> should redirect back to Parts list page
    partsPages.list.partRow(partName).shouldNotExist();
  });
};

export default canDeletePart;
