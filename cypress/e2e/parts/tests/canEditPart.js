import { upkeepPages } from '../../../support/constants';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const scrollOffsetY = -120;

const canEditPart = ({ description, partName, now, teamName, teamName2 }) => {
  it(
    'Can edit part without multiple inventory lines',
    { testCaseId: 'QA-T6286' },
    () => {
      const additionalInfo = 'additional info 1';

      const now2 = Date.now();
      const assignee = 'Limited';
      const partNumber = `abc${now}`;
      const category = 'categort x';
      const vendor = `Business Vendor ${now}`;
      const vendor2 = `vendor 2 ${now2}`;
      const customer = `customer ${now}`;
      const customer2 = `customer 2 ${now2}`;
      const editedPart = {
        name: 'edited part name 2',
        number: `${now2}dcba`,
        description: `description 2 ${now2}`,
        category: 'category Y',
      };

      h.createVendor(vendor2);
      h.createCustomer(customer);
      h.createCustomer(customer2);

      h.createVendor(vendor).then((vendorTwo) =>
        h.createCustomer(customer).then((customerOne) => {
          cy.request({
            method: 'GET',
            url: `${Cypress.env('CYPRESS_API_URL')}/api/v1/users/me`,
          }).then((user) => {
            const userToken = JSON.parse(
              window.localStorage.getItem('currentUser'),
            );
            const { sessionToken } = userToken;
            cy.createTeam(teamName2, sessionToken).then((teamId2) => {
              h.createPartInventory(
                {
                  category,
                  partNumber,
                  additionalPartDetails: additionalInfo,
                  stringPartName: partName,
                  stringPartDescription: description,
                  arrayOfAssignedVendors: [vendorTwo.body.result.objectId],
                  arrayOfAssignedCustomers: [customerOne.body.result.objectId],
                  arrayOfAssignedTeams: [teamId2.result.id],
                  arrayOfAssignedUsers: [user.body.result.id],
                },
                true,
              );
            });
          });
        }),
      );

      // From the Parts list page, click on a part row
      upkeepPages.PARTS.go();
      partsPages.list.partRow(partName).shouldExist();
      partsPages.list.partRow(partName).click();

      // The Part’s detail page should be visible
      partsPages.details.nameRow.shouldContain(partName);
      partsPages.details.descriptionRow.shouldContain(description);
      partsPages.details.vendorsRow.shouldContain(vendor);
      partsPages.details.customersRow.shouldContain(customer);

      // Take the action to Edit Part
      cy.contains('button', 'Edit').click();

      // All inputs should be filled out with the current values (except files)
      partsPages.form.name(false).shouldHaveValue(partName);
      partsPages.form.partNumber.shouldHaveValue(partNumber);
      partsPages.form.description.shouldHaveValue(description);
      partsPages.form.category.shouldHaveValue(category);
      partsPages.form.assignTeam.shouldContain(teamName2);
      partsPages.form.vendorInput.shouldContain(vendor);
      partsPages.form.customers.shouldContain(customer);
      partsPages.form.additionalInformation.shouldContain(additionalInfo);

      // Input new values in all fields (except files)
      partsPages.form.name(false).type(editedPart.name, { scrollOffsetY });
      partsPages.form.partNumber.type(editedPart.number, { scrollOffsetY });
      partsPages.form.description.type(editedPart.description, {
        scrollOffsetY,
      });
      partsPages.form.category.type(editedPart.category, { scrollOffsetY });

      partsPages.form.assignWorkers.type(assignee, { scrollOffsetY });
      cy.contains('li', assignee, { matchCase: false }).click();

      partsPages.form.assignTeam.type(' ', { scrollOffsetY });
      partsPages.form.assignTeam.click();
      cy.contains(teamName).click();

      partsPages.form.vendorInput.click();
      cy.contains(vendor2).click();
      // eslint-disable-next-line cypress/no-force
      cy.contains('Edit Part').click({ force: true });

      partsPages.form.customerInput.click();
      cy.contains(customer2).click();
      partsPages.form.additionalInformation.type('2 info additional', {
        scrollOffsetY,
      });

      cy.contains('button', 'Add Custom Field').click({
        scrollBehavior: 'bottom',
      });

      // Value/Unit (Uneditable until Field Name is added)
      partsPages.form.customFieldName.type('Custom Field 1', { scrollOffsetY });
      partsPages.form.customFieldValue.shouldBe('enabled');
      partsPages.form.customFieldUnit.shouldBe('enabled');

      // If I toggle “This is a non-stock part”, then the minimum quantity field becomes uneditable
      partsPages.form.minimumPartQuantity(false).shouldBe('enabled');
      partsPages.form.invSettingsNonStockPartBox.click();
      partsPages.form.minimumPartQuantity(false).shouldBe('disabled');

      // Once all fields are edited, I can submit the form via the “Save changes” button at the top of the page
      partsPages.form.continuePartCreateButton.click();

      // After submitting the form, I am returned to the Parts detail page, I should be able to see the newly updated details
      partsPages.details.detailsTab.click();
      partsPages.details.nameRow.shouldContain(editedPart.name);
      partsPages.details.descriptionRow.shouldContain(editedPart.description);
      partsPages.details.vendorsRow.shouldContain(vendor2);
      partsPages.details.customersRow.shouldContain(customer2);

      partsPages.details.assignedToColumn.shouldContain(assignee);
      partsPages.details.assignedToColumn.shouldContain(teamName);
    },
  );
};

export default canEditPart;
