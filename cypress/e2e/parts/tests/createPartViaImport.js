import { upkeepPages } from '../../../support/constants';
import { startDromoImport } from '../../../helpers/dromoImportHelpers';

import * as h from '../../../helpers';
import * as partsHelpers from '../helpers/partsHelpers';

const createPartViaImport = (emails) => {
  it('should create part via import', { testCaseId: 'QA-T399' }, () => {
    Cypress.on('uncaught:exception', () => false);

    const csvFile = 'parts-import.csv';
    const now = Date.now();
    const partName = `Part A ${now}`;
    const partNumber = Date.now();
    const locationName = `Location A ${now}`;
    const quantity = '230';
    const costPerUnit = '32.40';
    const minQuantity = '100';
    const category = 'Category A';
    const workerEmail = emails.TECH;
    const vendor = `Vendor A ${now}`;
    const customer = `Customer A ${now}`;

    const testData = {
      partName,
      partNumber,
      locationName,
      quantity,
      costPerUnit,
      minQuantity,
      category,
      workerEmail,
      vendor,
      customer,
    };

    h.createLocation({ stringName: locationName });
    h.createVendor(vendor);
    h.createCustomer(customer);

    // Create csv
    const csvData = partsHelpers.createCsvData(testData);
    cy.writeFile(`cypress/fixtures/${csvFile}`, csvData);

    // Begin import
    upkeepPages.PARTS.go();
    partsHelpers.importParts(csvFile);
    startDromoImport();
    partsHelpers.verifyImportCreateSuccess();
  });
};

export default createPartViaImport;
