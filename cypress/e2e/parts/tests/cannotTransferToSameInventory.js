import { createPartsWithLocation } from '../helpers';
import * as partsPages from '../components/index';

export default function cannotTransferToSameInventory({
  partName,
  noLocationQuantity,
  location,
}) {
  it('cannot transfer inventory from an inventory line to itself', {}, () => {
    cy.on('uncaught:exception', () => false);

    createPartsWithLocation(location, partName, noLocationQuantity);

    partsPages.list.partsTab.click();

    partsPages.list.partRow(partName).click();

    partsPages.details.inventoryRowMenu(location).click();
    partsPages.details.transfer.click();

    cy.get('#portal').within(() => {
      partsPages.transferModal.transferButton.shouldBeDisabled();
      partsPages.transferModal.originInventoryLine.shouldContain(location);

      partsPages.transferModal.destinationInventoryLine.click();

      partsPages.form
        .selectLocationPortal(location)
        .should('have.class', 'isLocked');
    });
  });
}
