import { createPartsWithLocation } from '../helpers';
import { list } from '../components';

export default function viewInventoryLines({ partName, noLocationQuantity }) {
  it('Can see inventory lines for part', {}, () => {
    const location = 'Location 1';
    createPartsWithLocation(location, partName, noLocationQuantity);

    list.partsTab.click();

    list.partRow(partName).click();

    cy.get('tr')
      .should('contain.text', 'No Location')
      .should('contain.text', location);

    cy.contains('No Location')
      .parents('tr')
      .should('exist')
      .should('contain.text', 'Low stock');

    cy.contains(location)
      .parents('tr')
      .should('exist')
      .should('contain.text', 'In stock');
  });
}
