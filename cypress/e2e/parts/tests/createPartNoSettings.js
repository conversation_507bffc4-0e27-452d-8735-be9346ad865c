import * as partHelpers from '../helpers';
import * as partsPages from '../components';

export default function createPartNoSettings() {
  it('Can create with no shared settings', () => {
    cy.on('uncaught:exception', () => {
      return false;
    });

    partsPages.list.createPart.click();

    const inputs = partHelpers.getRandomInputInformation();
    partHelpers.inputPartInformation(inputs);

    partHelpers.selectUsers();

    partsPages.form.inventoryTab.click();

    partHelpers.createInventoryLines(3, {});

    partsPages.form.continuePartCreateButton.click();

    partsPages.list.partsTab.click();

    cy.get('tr')
      .should('have.length.greaterThan', 0)
      .should('contain.text', inputs.name);
  });
}
