import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';
import * as partsPages from '../components';

const canPerformQtyAdjustment = () => {
  it(
    'Can perform a quantity adjustment on a part',
    { testCaseId: 'QA-T6288' },
    () => {
      const now = Date.now();
      const description = `description ${now}`;
      const negativeDescription = `negative ${description}`;
      const partName = `partName ${now}`;

      const initialQuantity = 2;
      const quantityToAdd = 4;
      const newInventoryQuanity = `${initialQuantity + quantityToAdd}.00`;

      const negativeQtyToAdd = -10;
      const newNegativeQty = `${
        initialQuantity + quantityToAdd + negativeQtyToAdd
      }`;

      h.createPartInventory(
        {
          minimumPartQuantity: '1',
          numberPartQuantity: initialQuantity,
          additionalPartDetails: `details ${now}`,
          partNumber: `${now}0`,
          stringPartName: partName,
          stringPartDescription: description,
        },
        true,
      );

      upkeepPages.PARTS.go();
      // Adjust the part quantity by a positive value
      partsPages.list.partRow(partName).click();
      partsPages.details.inventoryAvailableQuantity.shouldContain(
        initialQuantity,
      );
      partsPages.list.button('Adjust Quantity').click();

      // A modal should pop up
      // The modal cannot be submitted unless a quantity is entered in the Adjust by field
      partsPages.adjustmentForm.submit.shouldBeDisabled();

      // There are static numbers called “Original” and “New”
      // Original should return the current quantity value of the part
      partsPages.adjustmentForm.originalQty.shouldContain(initialQuantity);
      partsPages.adjustmentForm.newQty.shouldContain(initialQuantity);
      partsPages.adjustmentForm.quantityToAdd.type(quantityToAdd);

      // Entering this quantity updates the “New” section below
      // New = Original + Adjust by, eg. I currently have 5 qty, I adjust by 6, the new value is 11
      partsPages.adjustmentForm.newQty.shouldContain(newInventoryQuanity);

      // I can enter details in the details section
      partsPages.adjustmentForm.eventNoteDetails.type(description);
      partsPages.adjustmentForm.submit.click();

      // The quantity of the part should be updated
      partsPages.details.inventoryAvailableQuantity.shouldContain(
        newInventoryQuanity,
      );

      // Go to the Adjustments tab
      partsPages.details.adjustmentsTab.click();
      partsPages.details
        .adjustmentsTableData(newInventoryQuanity)
        .shouldExist();
      partsPages.details.adjustmentsTableData(description).shouldExist();
      partsPages.details.adjustmentsTableData(quantityToAdd).shouldExist();

      // Adjust the part quantity by a negative value
      // From a Part’s details page, take the action to Adjust Quantity
      partsPages.details.detailsTab.click();
      partsPages.list.button('Adjust Quantity').click();
      // A modal should pop up
      // The modal cannot be submitted unless a quantity is entered in the Adjust by field
      partsPages.adjustmentForm.submit.shouldBeDisabled();

      // Original should return the current quantity value of the part
      // Enter a negative quantity into the Adjust by field
      partsPages.adjustmentForm.originalQty.shouldContain(newInventoryQuanity);
      partsPages.adjustmentForm.newQty.shouldContain(newInventoryQuanity);
      partsPages.adjustmentForm.quantityToAdd.type(negativeQtyToAdd);

      // Entering this quantity updates the “New” section below
      // New = Original + Adjust by eg. I currently have 5 qty, I adjust by -3, the new value is 2
      partsPages.adjustmentForm.newQty.shouldContain(newNegativeQty);
      partsPages.adjustmentForm.eventNoteDetails.type(negativeDescription);
      partsPages.adjustmentForm.submit.click();

      // The quantity of the part should be updated
      partsPages.details.inventoryAvailableQuantity.shouldContain(
        newNegativeQty,
      );
      // Go to the Adjustments tab
      partsPages.details.adjustmentsTab.click();
      cy.reload();
      partsPages.details.adjustmentsTableData(newNegativeQty).shouldExist();
      partsPages.details
        .adjustmentsTableData(negativeDescription)
        .shouldExist();
      partsPages.details.adjustmentsTableData(negativeQtyToAdd).shouldExist();
    },
  );
};

export default canPerformQtyAdjustment;
