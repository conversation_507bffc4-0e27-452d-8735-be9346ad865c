import * as partsPages from '../components';
import { createPartsWithLocation } from '../helpers';

const galleryView = ({ partName }) => {
  it('Can view parts in gallery view', { testCaseId: 'QA-T6353' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const location = 'Location 1';
    createPartsWithLocation(location, partName);
    // can go to gallery view
    partsPages.list.partRow(partName).shouldExist();
    partsPages.list.tableViewButton.shouldBeVisible();
    partsPages.list.tableViewButton.click();
    partsPages.list.galleryOptionInDropdown.click();
    partsPages.list.galleryViewButton.shouldBeVisible();

    // verify part exists in gallery view
    partsPages.list.partCard(partName).shouldBeVisible();
    partsPages.list.partCard(partName).click();
    partsPages.details.detailsTab.click();
    partsPages.details.nameRow.shouldContain(partName);
  });
};

export default galleryView;
