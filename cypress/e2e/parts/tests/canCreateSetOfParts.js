import { upkeepPages } from '../../../support/constants';

import * as h from '../../../helpers';
import * as partsHelpers from '../helpers/setOfPartsHelpers';

const canCreateSetOfParts = () => {
  it('can create a Set of Parts', { testCaseId: 'QA-T87' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const partNameA = `Part A ${now}`;
    const partNameB = `Part B ${now}`;
    const setName = `Set A ${now}`;

    h.createPart({ partName: partNameA, partNumber: `a_${now}` }, true);
    h.createPart({ partName: partNameB, partNumber: `b_${now}` }, true);
    upkeepPages.PARTS.go();

    partsHelpers.goToSetsTab();
    partsHelpers.createNewSet();
    partsHelpers.fillSetFields({ partNameA, partNameB, setName });
    partsHelpers.submitNewSet();
    partsHelpers.verifySet({ partNameA, partNameB, setName });
  });
};

export default canCreateSetOfParts;
