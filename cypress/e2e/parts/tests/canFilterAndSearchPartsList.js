import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';
import * as partsPages from '../components';

const canFilterAndSearchPartsList = () => {
  it('Can filter and search parts list', { testCaseId: 'QA-T6287' }, () => {
    const now = Date.now();
    const barCode = `barCode${now}`;
    const description = `description ${now}`;
    const location = `location ${now}`;
    const partName = `partName ${now}`;
    const toCreate = 4;

    for (let i = 0; i < toCreate; i++) {
      const createLocation = `${location} ${i}`;
      h.createLocation({ stringName: createLocation }).then((loc) => {
        h.createPartInventory(
          {
            minimumPartQuantity: `${i + 1}`,
            numberPartQuantity: i * 2, // to create Low Stock, set only to i
            additionalPartDetails: `details ${i}`,
            partNumber: `${now}${i}${i}${i}`,
            stringPartName: `${partName} ${i}`,
            stringPartDescription: `${description} ${i}`,
            stringSerialNumber: `${i}`.repeat(toCreate) + barCode,
            objectLocation: loc.body.result.id,
            nonStock: i % 2 === 0,
          },
          true,
        );
      });
    }
    upkeepPages.PARTS.go();

    // Searching by name
    // From the Parts list, type some text into the search bar
    partsPages.list.searchBar.type(partName);
    // In the case where the text entered is contained in the name of at least 1 part, then all parts where the search term is contained in the name are returned in the list
    partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
    // In the case where the text entered is not contained in the name of at least 1 part, then the list should return its empty state
    partsPages.list.clearSearchBar.click();
    partsPages.list.searchBar.type('qwerty');
    partsPages.list.partRow(partName).shouldHaveLength(0);

    // Searching by barcode
    // From the Parts list, type some text into the search bar
    partsPages.list.button('Reset').click();

    partsPages.list.searchBar.type(barCode);
    // In the case where the text entered is contained in the barcode of at least 1 part, then all parts where the search term is contained in the barcode are returned in the list
    partsPages.list.partRowCell(barCode).shouldHaveLength(toCreate);
    // In the case where the text entered is not contained in the barcode of at least 1 part, then the list should return its empty state
    partsPages.list.button('Reset').click();
    partsPages.list.searchBar.type('asdfghj');
    partsPages.list.partRow(barCode).shouldHaveLength(0);

    // Searching by description
    // From the Parts list, type some text into the search bar
    partsPages.list.button('Reset').click();
    partsPages.list.searchBar.type(description);
    // In the case where the text entered is contained in the description of at least 1 part, then all parts where the search term is contained in the description are returned in the list
    partsPages.list.partRowCell(description).shouldHaveLength(toCreate);
    // In the case where the text entered is not contained in the description of at least 1 part, then the list should return its empty state
    partsPages.list.button('Reset').click();
    partsPages.list.searchBar.type('zxcvbmn');
    partsPages.list.partRow(description).shouldHaveLength(0);
    partsPages.list.button('Reset').click();
    partsPages.list.loader.shouldNotExist();

    // Filtering by Status
    // From the Parts list, open the Status filter
    partsPages.list.button('Status').click(); // clear state

    // Select 1 or many statuses from the picker
    partsPages.list.filterCheckbox('In stock').click();
    partsPages.list.saveStatusButton.click();

    // If any parts match the selected status(es), then those should be returned in the list
    partsPages.list.partRow('In stock').shouldHaveLengthGreaterThan(1);
    // If no parts match the selected status(es), then the list should return its empty state
    partsPages.list.partRow('Non-stock').shouldHaveLength(0);
    partsPages.list.partRow('Out of stock').shouldHaveLength(0);

    partsPages.list.button('Status').click(); // clear state
    partsPages.list.clearStatusButton.click();
    partsPages.list.saveStatusButton.click();
    partsPages.list.loader.shouldNotExist();

    // Filtering by Location
    // From the Parts list, open the Location filter
    partsPages.list.button('Location').click();
    // Select 1 or many locations from the picker
    // If any parts are assigned to the Location(s) selected, then those should be returned in the list
    const locationZero = `${location} 0`;
    const locationOne = `${location} 1`;
    partsPages.list.locationFilterCheckbox(locationZero).click({ force: true });
    partsPages.list.saveLocationButton.click();
    partsPages.list.partRowCell(locationZero).shouldHaveLength(1);

    // If no parts are assigned to the Locations(s) selected, then the list should return its empty state
    partsPages.list.partRowCell(locationOne).shouldHaveLength(0);
    partsPages.list.button('Reset').click();
    partsPages.list.partRowCell(partName).shouldHaveLengthGreaterThan(1);
    partsPages.list.loader.shouldNotExist();

    // From the Parts list, add at least one filter and one search that return a part
    const partFilter = `${partName} 0`;
    partsPages.list.button('Filters').click();
    partsPages.list.button('Add Filter').click();
    partsPages.list.filterItem('Name').click();
    partsPages.list.filterNameInput.type(partFilter);
    partsPages.list.button('Apply').click();
    partsPages.list.partRowCell(partFilter).shouldHaveLength(1);

    // Take the Reset action in the Filter bar
    partsPages.list.button('Reset').click();

    // All filters should be removed, the search should be cleared. All parts should now be visible in the list.
    partsPages.list.button('Filters').shouldNotContain('(1)');
    partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
  });
};

export default canFilterAndSearchPartsList;
