import { upkeepPages } from '../../../support/constants';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const canExportPartsInventoryLines = () => {
  it(
    'can export parts with inventory lines',
    { testCaseId: 'QA-T6668' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const exportFile = 'upkeep-inventories.csv';
      const now = Date.now();
      const barCode = `barCode${now}`;
      const description = `description ${now}`;
      const location = `location ${now}`;
      const partName = `partName ${now}`;
      const toCreate = 2;

      for (let i = 0; i < toCreate; i++) {
        const createLocation = `${location} ${i}`;
        h.createLocation({ stringName: createLocation }).then((loc) => {
          h.createPart(
            {
              barCode: `${i}`.repeat(toCreate) + barCode,
              partName: `${partName} ${i}`,
              partNumber: `${now}${i}${i}${i}`,
              description: `${description} ${i}`,
              partLines: [
                {
                  objectLocation: loc.body.result.id,
                  numberPartCost: null,
                  stringArea: `area${i}`,
                  stringSerialNumber: '1',
                  numberPartQuantity: null,
                },
              ],
            },
            true,
          );
        });
      }
      cy.reload();
      upkeepPages.PARTS.go();
      partsPages.list.partRow(partName).shouldBeVisible();
      partsPages.list.dropdownButton.click();
      partsPages.list.exportFilteredViewButton.click();

      cy.readFile(`cypress/downloads/${exportFile}`).should(
        'contain',
        `${partName} ${toCreate - 1}`,
      );
    },
  );
};

export default canExportPartsInventoryLines;
