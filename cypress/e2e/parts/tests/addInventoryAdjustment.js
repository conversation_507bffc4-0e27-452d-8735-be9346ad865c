import faker from 'faker';
import { createPartsWithLocation } from '../helpers';
import * as partsPages from '../components';

export default function addInventoryAdjustment({
  partName,
  noLocationQuantity,
  location,
}) {
  it('Can add an inventory adjustment to an inventory line', {}, () => {
    cy.on('uncaught:exception', () => false);

    createPartsWithLocation(location, partName, noLocationQuantity);

    partsPages.list.partsTab.click();

    partsPages.list.partRow(partName).click();

    partsPages.details.inventoryRowMenu('No Location').click();
    partsPages.details.adjustQuantity.click();

    const quantityToAdjust = faker.datatype.number(500);
    partsPages.adjustmentForm.quantityToAdd.type(
      `{selectall}${quantityToAdjust}`,
    );
    partsPages.adjustmentForm.eventNoteDetails.type(faker.lorem.sentence());

    partsPages.adjustmentForm.submit.click();
    cy.wait(200);

    const newQuantity = noLocationQuantity + quantityToAdjust;
    cy.contains('tr', 'No Location').within(() => {
      cy.contains(`${newQuantity}.00`).should('exist');
    });
    partsPages.details.adjustmentsTab.click();

    cy.wait(1500);
    partsPages.details.selectInventoryDropdown.click();
    partsPages.details.locationDropdownItem('No Location').click();

    cy.get('tbody tr')
      .should('contain.text', `${quantityToAdjust}.00`)
      .should('contain.text', `${newQuantity}.00`);
  });
}
