import { upkeepPages } from '../../../support/constants';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const scrollOffsetY = -120;

const canCreateNewPart = ({
  description,
  imageFile,
  partName,
  now,
  teamName,
}) => {
  it(
    'Can create part without multiple inventory lines',
    { testCaseId: 'QA-T6285' },
    () => {
      const vendor = `Business Vendor 1 ${now}`;
      const customer = `customer 1 ${now}`;
      const assignee = 'Limited';
      upkeepPages.PARTS.go();
      h.createVendor(vendor);
      h.createCustomer(customer);

      partsPages.list.createPart.click();

      partsPages.form.name(false).type(partName, { scrollOffsetY });
      partsPages.form.partNumber.type(now, { scrollOffsetY });
      partsPages.form.description.type(description, { scrollOffsetY });
      partsPages.form.category.type('category', { scrollOffsetY });

      partsPages.form.imagesDropZone
        .get()
        .first()
        .selectFile(imageFile, { force: true });

      partsPages.form.assignWorkers.type(assignee, { scrollOffsetY });
      cy.contains('li', assignee, { matchCase: false }).click();

      partsPages.form.assignTeam.click();
      cy.contains(teamName, { matchCase: false }).click({
        scrollBehavior: 'bottom',
      });

      partsPages.form.vendorInput.click();
      cy.contains(vendor).click();
      // eslint-disable-next-line cypress/no-force
      cy.contains('Create Part').click({ force: true });

      partsPages.form.customerInput.click();
      cy.contains(customer).click();

      partsPages.form.additionalInformation.click();

      // custom data
      cy.contains('button', 'Add Custom Field').click({
        scrollBehavior: 'bottom',
      });

      partsPages.form.customFieldName.type('Custom Field 1', { scrollOffsetY });
      partsPages.form.customFieldValue.shouldBe('enabled');
      partsPages.form.customFieldUnit.shouldBe('enabled');

      partsPages.form.customDataSectioButton.click();

      cy.contains('li', 'Remove Field').click();
      partsPages.form.customFieldValue.shouldNotExist();
      partsPages.form.customFieldUnit.shouldNotExist();

      partsPages.form.minimumPartQuantity(false).shouldBe('enabled');
      partsPages.form.invSettingsNonStockPartBox.click();
      partsPages.form.minimumPartQuantity(false).shouldBe('disabled');

      partsPages.form.continuePartCreateButton.click();
      partsPages.list.partRow(partName).click();

      partsPages.details.detailsTab.click();

      partsPages.details.nameRow.shouldContain(partName);
      partsPages.details.descriptionRow.shouldContain(description);
      partsPages.details.vendorsRow.shouldContain(vendor);
      partsPages.details.customersRow.shouldContain(customer);
      partsPages.details.assignedToColumn.shouldContain(assignee);
      partsPages.details.assignedToColumn.shouldContain(teamName);
    },
  );
};

export default canCreateNewPart;
