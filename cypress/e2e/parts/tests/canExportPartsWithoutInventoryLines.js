import { upkeepPages } from '../../../support/constants';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const canExportPartsWithoutInventoryLines = () => {
  it(
    'can export parts without inventory lines',
    { testCaseId: 'QA-T6667' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const exportFile = 'upkeep-parts.csv';
      const now = Date.now();
      const barCode = `barCode${now}`;
      const description = `description ${now}`;
      const location = `location ${now}`;
      const partName = `partName ${now}`;
      const toCreate = 2;

      for (let i = 0; i < toCreate; i++) {
        const createLocation = `${location} ${i}`;
        h.createLocation({ stringName: createLocation }).then((loc) => {
          h.createPartInventory(
            {
              minimumPartQuantity: `${i + 1}`,
              numberPartQuantity: i * 2, // to create Low Stock, set only to i
              additionalPartDetails: `details ${i}`,
              partNumber: `${now}${i}${i}${i}`,
              stringPartName: `${partName} ${i}`,
              stringPartDescription: `${description} ${i}`,
              stringSerialNumber: `${i}`.repeat(toCreate) + barCode,
              objectLocation: loc.body.result.id,
              nonStock: i % 2 === 0,
            },
            true,
          );
        });
        cy.wait(1_500); // upkeep-parts.csv is empty, wait 1 second
      }
      upkeepPages.PARTS.go();
      cy.reload();

      partsPages.list.partRow(partName).shouldBeVisible();
      partsPages.list.dropdownButton.click();
      partsPages.list.exportFilteredViewButton.click();

      cy.readFile(`cypress/downloads/${exportFile}`)
        .should('contain', `${partName} ${toCreate - 1}`)
        .should('contain', `${location} ${toCreate - 1}`)
        .should('contain', barCode);
    },
  );
};

export default canExportPartsWithoutInventoryLines;
