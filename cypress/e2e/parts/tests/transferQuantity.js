import faker from 'faker';
import { createPartsWithLocation } from '../helpers';
import * as partsPages from '../components/index';
// import * as h from '../../../helpers';

export default function transferQuantity({
  partName,
  noLocationQuantity,
  location,
}) {
  it('can transfer quantity between inventory lines', {}, () => {
    cy.on('uncaught:exception', () => false);

    createPartsWithLocation(location, partName, noLocationQuantity);

    partsPages.list.partsTab.click();

    partsPages.list.partRow(partName).click();

    partsPages.details.inventoryRowMenu('Location 1').click();
    partsPages.details.transfer.click();

    cy.get('#portal').within(() => {
      partsPages.transferModal.transferButton.shouldBeDisabled();

      partsPages.transferModal.originInventoryLine.shouldContain(location);
      partsPages.transferModal.destinationInventoryLine.click();
      partsPages.form.selectLocationPortal('No Location').click();
    });

    // h.closeTopPortal();

    cy.get('#portal').within(() => {
      partsPages.transferModal.transferQty.type(faker.datatype.number(100));

      partsPages.transferModal.transferButton.click();
    });
  });
}
