import faker from 'faker';
import * as helpers from '../../../helpers';
import * as partHelpers from '../helpers';
import * as partsPages from '../components';

export default function createPartWithCostBarcodeNonStock() {
  it('Can create with same cost, same barcode, and nonStock', () => {
    cy.on('uncaught:exception', () => {
      return false;
    });

    // Given I have multiple locations
    for (let i = 0; i < 5; i++) {
      helpers.createLocation({ stringName: `Location ${i}` }, false);
    }
    helpers.createVendor('Parts Vendor', false);
    helpers.createCustomer('Parts Customer', false);

    partsPages.list.createPart.click();

    const inputs = partHelpers.getRandomInputInformation();
    partHelpers.inputPartInformation(inputs);

    const barcode = faker.random.alphaNumeric(15);
    const cost = faker.commerce.price();

    partHelpers.selectUsers();
    partHelpers.selectInventorySettings([
      { settingName: 'nonStock' },
      {
        settingName: 'sameBarcode',
        secondarySettingName: 'barcode',
        secondaryValue: barcode,
      },
      {
        settingName: 'sameCostForAll',
        secondarySettingName: 'cost',
        secondaryValue: cost,
      },
    ]);

    partsPages.form.inventoryTab.click();

    partHelpers.createInventoryLines(3, {
      nonStock: true,
      sameBarcode: true,
      samePartCost: true,
    });

    partsPages.form.continuePartCreateButton.click();

    partsPages.list.partsTab.click();

    cy.get('tr')
      .should('have.length.greaterThan', 0)
      .should('contain.text', inputs.name);
  });
}
