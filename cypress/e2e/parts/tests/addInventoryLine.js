import faker from 'faker';
import * as helpers from '../../../helpers/createHelpers/index';
import { createPartsWithLocation } from '../helpers';
import * as partHelpers from '../helpers';
import * as partsPages from '../components';
// import * as h from '../../../helpers';

export default function addInventoryLine({
  partName,
  noLocationQuantity,
  location,
  location2,
}) {
  it('Can add Inventory Line to Part', {}, () => {
    helpers.createLocation({ stringName: location2 }, false);
    createPartsWithLocation(location, partName, noLocationQuantity);

    partsPages.list.partsTab.click();

    partsPages.list.partRow(partName).click();
    partsPages.details.addInventoryLine.click();

    const inputs = {
      stringArea: faker.lorem.word(),
      minimumPartQuantity: faker.datatype.number(25),
      maximumPartQuantity: '1234',
      stringSerialNumber: faker.random.alphaNumeric(15),
      numberPartCost: faker.commerce.price(),
      numberPartQuantity: 0,
    };

    partsPages.form.selectNewLocation.click();
    partsPages.form.locationDropdownItem(location2).click();
    // h.closeTopPortal();

    partHelpers.inputPartInformation(inputs, true);
    partsPages.form.submitInventory('Add').click();
    const data = {
      ...inputs,
      location: location2,
      status: 'Out of stock',
      numberPartQuantity: '0.00',
      minQty: `${inputs.minimumPartQuantity}`,
      cost: `$${inputs.numberPartCost}`,
      // Add the additional "0.00" values that now appear in the table
      // These seem to be new quantity/cost columns in the updated UI
      additionalQuantity1: '0.00',
      additionalQuantity2: '0.00',
    };
    partHelpers.verifyInventoryLine(data);
  });
}
