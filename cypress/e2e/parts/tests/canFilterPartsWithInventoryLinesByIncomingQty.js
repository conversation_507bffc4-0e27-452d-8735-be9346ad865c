import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';
import * as partsPages from '../components';

const canFilterPartsWithInventoryLinesByIncomingQty = () => {
  it(
    'Can exclude parts with inventory lines by Inconming Quantity',
    { testCaseId: 'QA-T6677' },
    () => {
      const now = Date.now();
      const barCode = `barCode${now}`;
      const description = `description ${now}`;
      const partName = `partName ${now}`;
      const toCreate = 3;
      const pOTitle = `fulfill PO ${now}`;
      const requestQuantity = 40;
      const forceCreateParts = true;

      for (let i = 0; i < toCreate; i++) {
        h.createPart(
          {
            barCode: `${i}`.repeat(toCreate) + barCode,
            partName: `${partName} ${i}`,
            partNumber: `${now}${i}${i}${i}`,
            description: `${description} ${i}`,
            partLines: [
              {
                numberPartCost: null,
                stringArea: `area${i}`,
                stringSerialNumber: '1',
                numberPartQuantity: null,
              },
            ],
          },
          forceCreateParts,
        ).then((part) => {
          h.createCustomPurchaseOrder(
            { title: `${pOTitle} ${i}` },
            {
              stringPartName: `${partName} ${i}`,
              id: part.body.result.inventoryParts[0],
              quantity: requestQuantity * (i + 1),
            },
          ).then((po) => {
            const partId = po.body.result.parts[0].id;
            const data = {
              statusAction: 'partially-fulfilled',
              partsReceived: { [partId]: i * 10 },
            };

            if (i > 0) {
              // Approve Request
              cy.request({
                url: `${Cypress.env(
                  'CYPRESS_API_URL',
                )}/api/v1/purchase-orders/${po.body.result.objectId}`,
                body: { status: 'approved' },
                method: 'PATCH',
              });

              // Partially fulfill PO
              cy.request({
                url: `${Cypress.env(
                  'CYPRESS_API_URL',
                )}/api/v1/purchase-orders/${po.body.result.objectId}`,
                body: data,
                method: 'PATCH',
              });
            }
          });
        });
      }
      upkeepPages.PARTS.go();
      partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);

      partsPages.list.inventoryTab.click();
      partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
      // filter to exclude incoming qty
      partsPages.list.button('Incoming Qty').click();
      partsPages.list.filterCheckbox('Exclude Incoming Inventory').click();
      partsPages.list.saveStatusButton.click();

      // one PO wasn't approved, should not have Inconming parts
      partsPages.list.partRowCell(partName).shouldHaveLength(1);

      // reset and validate
      partsPages.list.button('Reset').click();
      partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
    },
  );
};

export default canFilterPartsWithInventoryLinesByIncomingQty;
