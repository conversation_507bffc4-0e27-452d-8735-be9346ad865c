import { upkeepPages } from '../../../support/constants';
import * as partsHelpers from '../helpers/partsHelpers';
import * as partsPages from '../components';

const adminCanDeleteFileOnPart = () => {
  it(
    'Admin can delete file on parts details view',
    { testCaseId: 'QA-T392' },
    () => {
      const now = Date.now();
      const partNo = `${now}`;
      const partName = `Part with file ${now}`;
      const file = 'sample.pdf';

      upkeepPages.PARTS.go();
      partsPages.list.partsTab.click();
      partsHelpers.createPartWithFile(partName, partNo, file);
      partsHelpers.goToPartDetails(partName);
      partsHelpers.goToFilesTabAndDeleteFile(partName, file);
    },
  );
};

export default adminCanDeleteFileOnPart;
