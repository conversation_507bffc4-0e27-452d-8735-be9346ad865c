import faker from 'faker';
import { createPartsWithLocation } from '../helpers';
import * as partHelpers from '../helpers';
import * as partsPages from '../components';

export default function editInventoryLine({ partName, noLocationQuantity }) {
  it('Can edit Inventory Line', {}, () => {
    createPartsWithLocation('Location 1', partName, noLocationQuantity);

    partsPages.list.partsTab.click();

    partsPages.list.partRow(partName).click();

    partsPages.details.inventoryRowMenu('No Location').click();
    partsPages.details.editInventoryRow.click();
    const inputs = {
      stringArea: faker.lorem.word(),
      minimumPartQuantity: 1,
      maximumPartQuantity: '234',
      stringSerialNumber: faker.random.alphaNumeric(15),
      numberPartCost: faker.commerce.price(),
    };
    partHelpers.inputPartInformation(inputs, true);
    partsPages.form.submitInventory('Edit').click();
    const data = {
      ...inputs,
      location: 'No Location',
      status: 'In stock',
      numberPartQuantity: `${noLocationQuantity}.00`,
      minQty: `${inputs.minimumPartQuantity}`,
      allocatedQty: '0.00',
      cost: `$${inputs.numberPartCost}`,
    };
    partHelpers.verifyInventoryLine(data);
  });
}
