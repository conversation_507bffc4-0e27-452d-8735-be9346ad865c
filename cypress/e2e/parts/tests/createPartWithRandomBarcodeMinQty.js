import faker from 'faker';
import * as partHelpers from '../helpers';
import * as partsPages from '../components';

export default function createPartWithRandomBarcodeMinQty() {
  it('Can create with random barcode, same min quantity for all', () => {
    cy.on('uncaught:exception', () => {
      return false;
    });

    partsPages.list.createPart.click();
    const inputs = partHelpers.getRandomInputInformation();
    partHelpers.inputPartInformation(inputs);

    const minQty = faker.datatype.number(100);
    partHelpers.selectUsers();
    partHelpers.selectInventorySettings([
      {
        settingName: 'minQty',
        secondarySettingName: 'minQuantity',
        secondaryValue: minQty,
      },
      {
        settingName: 'randomBarcode',
      },
    ]);

    partsPages.form.inventoryTab.click();

    partHelpers.createInventoryLines(3, {
      sameMinQty: true,
      randomBarcode: true,
    });

    partsPages.form.continuePartCreateButton.click();

    partsPages.list.partsTab.click();

    cy.get('tr')
      .should('have.length.greaterThan', 0)
      .should('contain.text', inputs.name);
  });
}
