import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';
import * as partsPages from '../components';

const canFilterPartsByIncomingQty = () => {
  it(
    'Can exclude parts with Inconming Quantity',
    { testCaseId: 'QA-T6676' },
    () => {
      const now = Date.now();
      const forceCreateParts = true;
      const barCode = `barCode${now}`;
      const description = `description ${now}`;
      const partName = `partName ${now}`;
      const toCreate = 3;
      const pOTitle = `fulfill PO ${now}`;
      const requestQuantity = 40;

      for (let i = 0; i < toCreate; i++) {
        h.createPartInventory(
          {
            minimumPartQuantity: `${i + 1}`,
            numberPartQuantity: i * 2, // to create Low Stock, set only to i
            additionalPartDetails: `details ${i}`,
            partNumber: `${now}${i}${i}${i}`,
            stringPartName: `${partName} ${i}`,
            stringPartDescription: `${description} ${i}`,
            stringSerialNumber: `${i}`.repeat(toCreate) + barCode,
            nonStock: i % 2 === 0,
          },
          forceCreateParts,
        ).then((part) => {
          h.createCustomPurchaseOrder(
            { title: pOTitle },
            {
              stringPartName: `${partName} ${i}`,
              id: part.body.result.objectId,
              quantity: requestQuantity * (i + 1),
            },
          ).then((po) => {
            const partId = po.body.result.parts[0].id;
            const data = {
              statusAction: 'partially-fulfilled',
              partsReceived: { [partId]: i * 10 },
            };

            if (i > 0) {
              // Approve Request
              cy.request({
                url: `${Cypress.env(
                  'CYPRESS_API_URL',
                )}/api/v1/purchase-orders/${po.body.result.objectId}`,
                body: { status: 'approved' },
                method: 'PATCH',
              });

              // Partially fulfill PO
              cy.request({
                url: `${Cypress.env(
                  'CYPRESS_API_URL',
                )}/api/v1/purchase-orders/${po.body.result.objectId}`,
                body: data,
                method: 'PATCH',
              });
            }
          });
        });
      }
      upkeepPages.PARTS.go();
      partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
      // filter to exclude incoming qty
      partsPages.list.button('Incoming Qty').click();
      partsPages.list.filterCheckbox('Exclude Incoming Inventory').click();
      partsPages.list.saveStatusButton.click();

      // one PO wasn't approved, should not have Inconming parts
      partsPages.list.partRowCell(partName).shouldHaveLength(1);

      // reset and validate
      partsPages.list.button('Reset').click();
      partsPages.list.partRowCell(partName).shouldHaveLength(toCreate);
    },
  );
};

export default canFilterPartsByIncomingQty;
