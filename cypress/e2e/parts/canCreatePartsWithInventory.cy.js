import filterTests from '../../support/filterTests';
import * as tests from './tests';
import { upkeepPages } from '../../support/constants';

filterTests(['all', 'smoke'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);

  describe(
    'Create Part with Multiple Inventory Lines',
    // partsV3CreateUI has been deprecated
    { featureFlags: { partsV3CreateUI: false } },
    () => {
      const testId = 'partWithInventoryCreate';

      beforeEach(() => {
        cy.createOrLoginAdmin(
          testId,
          ['TECH', 'LIMITED_ADMIN'],
          'BUSINESS_PLUS',
          'super awesome team',
        );
        upkeepPages.PARTS.go();
        cy.contains('Create Part').should('be.visible');
      });

      tests.createPartWithCostBarcodeNonStock();
      tests.createPartWithRandomBarcodeMinQty();
      tests.createPartNoSettings();
    },
  );
});
