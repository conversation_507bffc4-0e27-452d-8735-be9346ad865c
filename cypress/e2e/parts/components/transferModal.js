import { configureSelectorProxy } from '../../../helpers';

const transferModal = configureSelectorProxy({
  transferButton: '[data-cy="Transfer"]',
  originInventoryLine: '[id="Origin Inventory LineListButton"]',
  destinationInventoryLine: '[id="Destination Inventory LineListButton"]',
  inventoryLineSelect: (name) =>
    `[data-cy="destinationColumn"] .menu-list-option div:contains(${name}) div[display="block"]`,
  transferQty: '[data-cy="transferQty"]',
});

export default transferModal;
