import { configureSelectorProxy } from '../../../helpers';

const form = configureSelectorProxy({
  formWrapper: '[data-cy="detailsMainWrapper"]',
  name: (inPortal) => `${inPortal ? '#portal' : ''} [data-cy="stringPartName"]`,
  partNumber: '[data-cy="partNumber"]',
  description: '[data-cy="stringPartDescription"]',
  category: '[data-cy="category"]',
  details: '[data-cy="additionalPartDetails"]',
  stringArea: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="stringArea"]`,
  minimumPartQuantity: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="minimumPartQuantity"]`,
  maximumPartQuantity: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="maximumPartQuantity"]`,
  stringSerialNumber: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="stringSerialNumber"]`,
  numberPartCost: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="numberPartCost"]`,
  numberPartQuantity: (inPortal) =>
    `${inPortal ? '#portal' : ''} [data-cy="numberPartQuantity"]`,
  submitInventory: (type) => `[data-cy="${type} Inventory Line"]`,
  selectLocation: '#LocationListButton',
  selectLocationPortal: (label) => `.portal .dropDown [data-cy="${label}"]`,
  selectNewLocation: '#LocationListButton',
  locationDropdownItem: (name) => `.item-container:contains("${name}")`,
  addInventoryLine: 'button:contains("Add Inventory Line")',
  inventoryTab: '[data-cy="Inventory"]',
  continuePartCreateButton: '[data-cy="continuePartCreateButton"]',
  dropdown: (name) => `label:contains("${name}") +div [title="Open"]`,
  imagesDropZone: '.dropzone input',
  fileDropZone: 'section:contains("Files") [data-cy="dropzone"]',
  assignWorkers: '[data-cy="Workers"] input',
  assignTeam: '[data-cy="Teams"]',
  assignTeamInput: '[data-cy="Teams"] input',
  customDataSection: 'section:contains("Custom Data")',
  assignedToSection: 'section:contains("Assigned To")',
  customDataSectioButton: 'section:contains("Custom Data") button',
  customFieldName: '[data-cy="custom-field-name"]',
  customFieldValue: '[data-cy="custom-field-value"]',
  customFieldUnit: '[data-cy="custom-field-unit"]',
  nonStockPartBox: '[data-cy="nonStock"]',
  invSettingsNonStockPartBox: '[data-cy="nonStock"]',
  invSettingsMinQtyThreshold: '[data-cy="minQty"]',
  vendors: '[data-cy="Vendors"]',
  vendorInput: '[id="VendorsListButton"]',
  customerInput: '[data-cy="Customers"] input',
  customers: '[data-cy="Customers"]',
  additionalInformation: '[data-cy="additionalPartDetails"]',
});

export default form;
