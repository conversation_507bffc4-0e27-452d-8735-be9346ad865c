import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  inventoryRowMenu: (location) => `tr:contains(${location}) button`,
  editInventoryRow: '[role="menuitem"]:contains("Edit")',
  adjustQuantity: '[role="menuitem"]:contains("Adjust Quantity")',
  addInventoryLine: 'button:contains("Add Inventory Line")',
  transfer: '[role="menuitem"]:contains("Transfer")',
  adjustmentsTab: '[data-cy="generic.labels.adjustments"]',
  selectInventoryDropdown: 'button[id="Inventory Line: ListButton"]',
  detailsTab: '[data-cy="generic.labels.details"]',
  assignedToColumn: '[data-cy="assignedToSection"]',
  nameRow: '[data-cy="Name"]',
  descriptionRow: '[data-cy="Description"]',
  categoryRow: '[data-cy="Category"]',
  vendorsRow: '[data-cy="Vendors"]',
  customersRow: '[data-cy="Customers"]',
  inventoryAvailableQuantity: '[data-cy="availableQtyLine"]',
  locationDropdownItem: (name) => `.item-container:contains("${name}")`,
  adjustmentsTableData: (value) => `td:contains(${value})`,
  optionsButton: '[data-cy="icon-button"]:first()',
  deleteButton: '[data-cy="menu-item-Delete"]',
  confirmDeleteButton: '[data-cy="Delete"]',
  filesTab: '[data-cy="generic.upkeepEntity.files"]',
  fileInFilesTab: (file) => `tr [href$="${file}"]`,
  fileOptionsButton: 'tr [data-cy="icon-button"]',
  removeFileButton: 'button:contains("Remove File")',
});

export default details;
