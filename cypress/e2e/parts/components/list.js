import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  partsHeader: 'h2:contains("Parts")',
  partRow: (partName) => `tr:contains(${partName}) .table-cell-interactive`,
  partRowCell: (partName) => `td:contains("${partName}")`,
  createPart: '[data-cy="CreatePartButton"]',
  searchBar: '[data-cy="search-bar"]',
  clearSearchBar: '.search-bar .icon-close',
  table: 'table',
  locationSearchMenu: '[data-cy="menu-list-search"]',
  filterCheckbox: (status) =>
    `[data-cy="undefinedMenuListWrapper"] li:contains(${status}) input[type="checkbox"]`,
  locationFilterCheckbox: (status) =>
    `.picker tr:contains(${status}) [data-cy^="cellSelector"]`,
  clearStatusButton: '[data-cy="Clear"]',
  saveLocationButton: '[data-cy="Save"]:visible',
  saveStatusButton:
    '[data-cy="undefinedMenuListWrapper"] [data-cy="Save"]:visible',
  filterItem: (filter) => `[data-cy="${filter}"] .item-container`,
  button: (text) => `button:contains(${text})`,
  filterNameInput: '[data-cy="stringPartName"]',
  loader: '.infinity-loader',
  setsTab: '[data-cy="generic.labels.sets"]',
  inventoryTab: '[data-cy="generic.labels.inventory"]',
  partsTab: '[data-cy="generic.upkeepEntity.parts"]',
  createSetButton: '[data-cy="CreatePartButton"]',
  tableViewButton: '[data-cy="generic.labels.table"]',
  galleryViewButton: '[data-cy="generic.labels.gallery"]',
  galleryOptionInDropdown: 'span:contains("Gallery")',
  partCard: (name) => `[class*="bottom-content"]>span:contains("${name}")`,
  dropdownButton: '[data-cy="icon-button"]',
  importButton: '[data-cy="menu-item-Import/Export"]',
  exportFilteredViewButton: '[data-cy="menu-item-Export Filtered View"]',
  startInventoryImportButton: '[data-cy="StartInventoryImportProcessButton"]',
});

export default list;
