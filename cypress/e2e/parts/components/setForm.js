import { configureSelectorProxy } from '../../../helpers';

const setForm = configureSelectorProxy({
  setName: '#name',
  addPartsButton: 'button:contains("Add Parts")',
  addPartsModal: '[data-cy="part-select-modal"]',
  partInModal: (name) => `td:contains("${name}") .table-cell-interactive`,
  submitPartsInSet: '[data-cy="AddPartsButton"]',
  createSetButton: 'button:contains("Create Set")',
  partsAdded: (name) => `section>div span:contains("${name}")`,
});

export default setForm;
