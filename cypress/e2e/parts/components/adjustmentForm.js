import { configureSelectorProxy } from '../../../helpers';

const adjustmentForm = configureSelectorProxy({
  quantityToAdd: '[data-cy="quantityToAdd"]',
  eventNoteDetails: '[data-cy="eventNoteDetails"]',
  submit: '[data-cy="adjustQuantityButton"]',
  originalQty:
    '[data-cy="components.molecules.AdjustQuantityModal.originalQty"]',
  newQty: '[data-cy="components.molecules.AdjustQuantityModal.newQty"]',
});

export default adjustmentForm;
