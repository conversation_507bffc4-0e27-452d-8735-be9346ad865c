import { configureSelectorProxy } from '../../../helpers';

const formSettings = configureSelectorProxy({
  minQty: '[data-cy="minQty"]',
  randomBarcode: '[data-cy="randomBarcode"]',
  nonStock: '[data-cy="nonStock"]',
  sameBarcode: '[data-cy="sameBarcode"]',
  sameCostForAll: '[data-cy="sameCostForAll"]',
  minQuantity: '[data-cy="minQuantity"]',
  barcode: '[data-cy="barcode"]',
  cost: '[data-cy="cost"]',
});

export default formSettings;
