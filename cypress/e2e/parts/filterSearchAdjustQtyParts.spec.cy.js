import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'smoke'], () => {
  // Error: ResizeObserver loop limit exceeded
  Cypress.on('uncaught:exception', () => false);
  describe('Filter Search and Adjust Qty Parts without multiple lines inventory', () => {
    const testId = 'filterAdjustQtyInventoryCreate';

    before(() => {
      const runnableEnvs = ['staging3', 'staging', 'production'];
      const env = Cypress.env('CYPRESS_ENV');
      if (!runnableEnvs.includes(env)) {
        cy.state('runnable').ctx.skip();
      }
      cy.createOrLoginAdmin(testId, ['TECH', 'LIMITED_ADMIN'], 'BUSINESS_PLUS');

      // toggle Enable Parts with multiple inventory lines off
      cy.window().then((window) => {
        const user = JSON.parse(window.localStorage.getItem('currentUser'));
        const role = user.roleId;
        cy.requestTogglePartsMultipleInventoryLines(
          { displayHierarchicalParts: false },
          role,
        );
      });
    });

    beforeEach(() => {
      cy.createOrLoginAdmin(testId);
    });

    partsTests.canPerformQtyAdjustment();
    partsTests.canFilterAndSearchPartsList();
    partsTests.canFilterPartsByIncomingQty();
  });
  describe('Filter Search and Adjust Qty Parts with multiple lines inventory', () => {
    const testId = 'filterWithInventoryLines';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS');
    });

    partsTests.canFilterPartsWithInventoryLinesByIncomingQty();
  });
});
