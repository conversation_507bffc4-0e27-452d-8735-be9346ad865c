import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Export Inventory lines', () => {
    const now = Date.now();
    const testId = `parts-export${now}`;

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'teamName');
    });

    after(() => {
      cy.exec('rm -rf cypress/fixtures/upkeep-parts.csv');
      cy.exec('rm -rf cypress/fixtures/upkeep-inventories.csv');
    });
    partsTests.canExportInventoryLines();
  });
});
