import faker from 'faker';
import * as partsPages from '../components';
import * as h from '../../../helpers';

const scrollOffsetY = -120;

export const getRandomInputInformation = () => ({
  name: `${faker.commerce.productMaterial()} ${faker.commerce.productName()}`,
  partNumber: faker.random.alphaNumeric(9),
  description: faker.commerce.productDescription(),
  category: faker.commerce.productAdjective(),
  details: faker.commerce.productDescription(),
});

export const inputPartInformation = (inputs, inPortal) => {
  Object.keys(inputs).forEach((input) => {
    const formProxy = partsPages.form[input];
    if (typeof formProxy === 'function') {
      formProxy(inPortal).type(inputs[input], { scrollOffsetY });
    } else {
      formProxy.type(inputs[input], { scrollOffsetY });
    }
  });
};

export const selectUsers = () => {
  partsPages.form.assignWorkers.select('engineering-test', {}, scrollOffsetY);
  partsPages.form.assignTeam.select('super awesome team', {}, scrollOffsetY);
  partsPages.form.vendorInput.select('Parts Vendor', {}, scrollOffsetY);
  // eslint-disable-next-line cypress/no-force
  cy.contains('Create Part').click({ force: true });
  partsPages.form.customerInput.select('Parts Customer', {}, scrollOffsetY);
};

export const selectInventorySettings = (settings) => {
  settings.forEach(({ settingName, secondarySettingName, secondaryValue }) => {
    partsPages.formSettings[settingName].check();
    if (secondaryValue) {
      partsPages.formSettings[secondarySettingName].type(secondaryValue);
    }
  });
};

export const createInventoryLines = (numberOfLines, inventorySettings) => {
  for (let i = 0; i < numberOfLines; i++) {
    cy.get(`[data-cy="inventoryLineRow${i}"]`).within(() => {
      partsPages.form.selectLocation.click({ scrollBehavior: 'bottom' });
    });

    partsPages.form
      .selectLocationPortal(`Location ${i}`)
      .click({ scrollBehavior: 'bottom' });

    cy.get(`[data-cy="inventoryLineRow${i}"]`).within(() => {
      partsPages.form.stringArea().type(faker.lorem.word());
      if (inventorySettings.sameBarcode || inventorySettings.randomBarcode) {
        partsPages.form.stringSerialNumber().shouldBeDisabled();
      } else {
        partsPages.form
          .stringSerialNumber()
          .type(faker.random.alphaNumeric(15));
      }
      if (inventorySettings.samePartCost) {
        partsPages.form.numberPartCost().shouldBeDisabled();
      } else {
        partsPages.form.numberPartCost().type(faker.commerce.price());
      }
      partsPages.form.numberPartQuantity().type(faker.datatype.number(1000));
      if (inventorySettings.sameMinQty || inventorySettings.nonStock) {
        partsPages.form.minimumPartQuantity().shouldBeDisabled();
      } else {
        partsPages.form.minimumPartQuantity().type(faker.datatype.number(100));
      }
    });
    if (i < numberOfLines) {
      partsPages.form.addInventoryLine.click({ scrollBehavior: 'bottom' });
    }
  }
};

export const verifyInventoryLine = (data) => {
  cy.contains('tr', data.location)
    .should('exist')
    .find('td')
    .each((child) => {
      if (child.hasClass('col-generic.upkeepEntity.barcode')) {
        cy.wrap(child).should('contain.text', data.stringSerialNumber);
      } else {
        // Skip empty cells (action columns, buttons, etc.)
        cy.wrap(child).invoke('text').then((cellText) => {
          if (cellText.trim() !== '') {
            cy.wrap(child).invoke('text').should('be.oneOf', Object.values(data));
          }
        });
      }
    });
};

export const createPartsWithLocation = (
  locationName,
  partName,
  noLocationQuantity = 0,
) => {
  // Given that I have a part with multiple inventory lines
  h.createLocation({ stringName: locationName }).then((loc) => {
    h.createPart({
      partName,
      partLines: [
        {
          objectLocation: loc.body.result?.id || loc.body.results[0]?.id,
          numberPartQuantity: faker.datatype.number({ min: 1000 }),
          minimumPartQuantity: `${faker.datatype.number({ max: 999 })}`,
          stringSerialNumber: faker.random.alphaNumeric(10),
          numberPartCost: parseInt(faker.commerce.price(), 10),
        },
        {
          numberPartQuantity: noLocationQuantity,
          minimumPartQuantity: `${faker.datatype.number({ min: 11 })}`,
          stringSerialNumber: faker.random.alphaNumeric(10),
          numberPartCost: parseInt(faker.commerce.price(), 10),
        },
      ],
    });
    cy.reload();
  });
};
