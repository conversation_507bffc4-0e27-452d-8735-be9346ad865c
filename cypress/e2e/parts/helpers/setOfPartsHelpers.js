import * as partsPages from '../components';

export const goToSetsTab = () => {
  partsPages.list.setsTab.shouldBeVisible();
  partsPages.list.setsTab.click();
  cy.url().should('contain', 'web/sets');
};

export const createNewSet = () => {
  partsPages.list.createSetButton.shouldBeVisible();
  partsPages.list.createSetButton.click();
};

export const fillSetFields = (testData = {}) => {
  partsPages.setForm.setName.click().type(testData.setName);

  // add parts
  partsPages.setForm.addPartsButton.shouldBeVisible();
  partsPages.setForm.addPartsButton.click();

  // select parts to add
  partsPages.setForm.addPartsModal.shouldBeVisible();
  partsPages.setForm.partInModal(testData.partNameA).click();
  partsPages.setForm.partInModal(testData.partNameB).click();

  partsPages.setForm.submitPartsInSet.click();
};

export const submitNewSet = () => {
  partsPages.setForm.createSetButton.shouldBeVisible();
  partsPages.setForm.createSetButton.click();
};

export const verifySet = (testData = {}) => {
  partsPages.setList.rows.shouldExist();
  cy.reload();
  partsPages.setList.setRow(testData.setName).shouldBeVisible();
  partsPages.setList.setRow(testData.setName).click();
  partsPages.setForm.setName.shouldHaveValue(testData.setName);
  partsPages.setForm.partsAdded(testData.partNameA).shouldBeVisible();
  partsPages.setForm.partsAdded(testData.partNameB).shouldBeVisible();
};
