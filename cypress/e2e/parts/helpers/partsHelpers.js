import { getIframeBody } from '../../../helpers/iframeHelpers';
import * as partsPages from '../components';

export const createCsvData = (testData = {}) => {
  let csv = '';
  csv =
    `Name,Part Number,Location Name,Quantity,Cost per unit,Minimum Quantity,Category,Assigned User Emails,Assigned Vendor Names,Assigned Customer Names\n` +
    `${testData.partName},${testData.partNumber},${testData.locationName},${testData.quantity},${testData.costPerUnit},${testData.minQuantity},${testData.category},${testData.workerEmail},${testData.vendor},${testData.customer}`;
  return csv;
};

export const importParts = (file) => {
  partsPages.list.dropdownButton.shouldBeVisible();
  partsPages.list.dropdownButton.click();
  partsPages.list.importButton.click();
  cy.wait(1500);
  partsPages.list.startInventoryImportButton.click();
  cy.wait(1500);

  getIframeBody('https://widget.dromo.io/')
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportCreateSuccess = () => {
  cy.contains('Import Complete. Created 1 and updated 0 Inventories.').should(
    'be.visible',
  );
};

export const createPartWithFile = (partName, partNo, file) => {
  partsPages.list.createPart.shouldBeVisible();
  partsPages.list.createPart.click();

  // Fill out Create Part form
  partsPages.form.name(false).type(partName);
  partsPages.form.partNumber.click().type(partNo);

  // Drag and drop to upload file
  partsPages.form.fileDropZone.scrollIntoView();
  partsPages.form.fileDropZone.shouldBeVisible();
  partsPages.form.fileDropZone.selectFile(`cypress/fixtures/${file}`, {
    action: 'drag-drop',
    force: true,
  });
  cy.contains(file).should('be.visible');

  // Confirm part created
  partsPages.form.continuePartCreateButton.click();
  cy.contains('Part created').should('be.visible');
};

export const goToPartDetails = (partName) => {
  // Select part in list
  cy.contains('h2', 'Parts').should('be.visible');
  partsPages.list.partRow(partName).shouldBeVisible();
  partsPages.list.partRow(partName).click();
};

export const goToFilesTabAndDeleteFile = (partName, file) => {
  cy.contains('h2', partName).should('be.visible');
  partsPages.details.filesTab.click();
  partsPages.details.fileInFilesTab(file).shouldBeVisible();
  partsPages.details.fileOptionsButton.click();
  partsPages.details.removeFileButton.click();
  partsPages.details.fileInFilesTab(file).shouldNotExist();
};
