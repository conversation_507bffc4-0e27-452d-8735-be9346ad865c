import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const testId = 'setOfParts';
  const now = Date.now();
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
  };

  describe('Set of Parts', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '', emails);
    });

    partsTests.canCreateSetOfParts();
  });
});
