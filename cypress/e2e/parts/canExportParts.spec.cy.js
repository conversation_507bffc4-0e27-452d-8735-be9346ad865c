import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Export parts without inventory lines', () => {
    const now = Date.now();
    const testId = `parts-import${now}`;
    const teamId = 'super awesome team';

    before(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamId);

      // toggle Enable Parts with multiple inventory lines off
      cy.window().then((window) => {
        const user = JSON.parse(window.localStorage.getItem('currentUser'));
        const role = user.roleId;
        cy.requestTogglePartsMultipleInventoryLines(
          { displayHierarchicalParts: false },
          role,
        );
      });
    });

    beforeEach(() => {
      cy.createOrLoginAdmin(testId);
    });

    after(() => {
      cy.exec('rm -rf cypress/downloads/upkeep-parts.csv');
    });

    partsTests.canExportPartsWithoutInventoryLines();
  });
});
