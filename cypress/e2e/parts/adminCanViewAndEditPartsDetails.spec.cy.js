import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'ui'], () => {
  describe('<PERSON><PERSON> can view / edit parts details', () => {
    Cypress.on('uncaught:exception', () => false);
    const testId = 'partsdetails';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
    });

    partsTests.adminCanDeleteFileOnPart();
  });
});
