import filterTests from '../../support/filterTests';
import * as partsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Parts Imports', () => {
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = `parts-import${now}`;
    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['TECH'],
        'BUSINESS_PLUS',
        'teamName',
        emails,
      );
    });
    after(() => {
      cy.exec('rm -rf cypress/fixtures/parts-import.csv');
    });

    partsTests.createPartViaImport(emails);
  });
});
