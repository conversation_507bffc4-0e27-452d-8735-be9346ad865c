import filterTests from '../../support/filterTests';
import * as purchaseOrdersTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  describe('Purchase Orders - can filter and download', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      const now = Date.now();
      const testId = `searchFilterDownloadPurchaseOrders${now}`;

      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'Team name');
      cy.contains('Work Orders').should('be.visible');
    });

    purchaseOrdersTests.canFilterAndSearchPoList();
    purchaseOrdersTests.canDownloadPurchaseOrderPdf();
  });
});
