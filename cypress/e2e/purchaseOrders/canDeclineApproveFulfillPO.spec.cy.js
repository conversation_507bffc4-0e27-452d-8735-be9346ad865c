import filterTests from '../../support/filterTests';
import * as purchaseOrdersTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const testId = 'declineApproveFulfillPO';

  describe('Purchase Orders - can approve/decline/fulfill', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'Team name');
    });

    purchaseOrdersTests.canApprovePurchaseOrder();
    purchaseOrdersTests.canDeclinePurchaseOrder();
    purchaseOrdersTests.canFulfillPurchaseOrder();
    purchaseOrdersTests.canOverFulfillPurchaseOrder();
  });
});
