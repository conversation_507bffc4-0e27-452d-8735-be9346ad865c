import filterTests from '../../support/filterTests';
import * as purchaseOrdersTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const teamId = 'Team name';
  const testId = 'purchaseOrders';
  const now = Date.now();
  let count = 1;

  describe('Purchase Orders - can create and edit', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(`${testId}+${count}`, [], 'BUSINESS_PLUS', teamId, {
        ADMIN: `engineering-test+${testId}+${count}_admin_${now}@${domain}`,
      });
      count += 1;
      cy.contains('Work Orders').should('be.visible');
    });

    purchaseOrdersTests.canCreatePurchaseOrders();
    purchaseOrdersTests.canEditPurchaseOrders();
  });
});
