import filterTests from '../../support/filterTests';
import { techTests } from './tests';

filterTests(['all', 'ui'], () => {
  describe('Tech Purchase Orders', () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'tech-po';
    const emails = {
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '', emails);

      // Activate and login to tech account
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          { email: emails.TECH },
          win.localStorage.authToken,
        );
      });
    });

    techTests.techCanGeneratePdf();
  });
});
