import filterTests from '../../support/filterTests';
import { viewOnlyTests } from './tests';

filterTests(['all', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);

  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const testId = 'vo-po';
  const emails = {
    VIEW_ONLY: `engineering-test+${testId}_view_only_${now}@${domain}`,
  };

  describe('View Only Purchase Orders tests', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
    });

    viewOnlyTests.canNavigateToPurchaseOrders(emails, testId);
    viewOnlyTests.canSubmitMessageViaActivityTab(emails, testId);
  });
});
