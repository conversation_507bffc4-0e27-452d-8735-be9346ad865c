import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  editButton: 'button:contains("Edit")',
  backButton: '[data-cy="backButton"]',
  declineButton: 'button:contains("Decline")',
  approveButton: 'button:contains("Approve")',
  fulfillButton: 'button:contains("Fulfill")',
  elipsesButton: '[data-cy="icon-button"]',
  deleteButton: '[data-cy="menu-item-Delete"]',
  confirmDeleteButton: '[data-cy="Delete"]',
  generatePdfButton: '[data-cy="menu-item-Generate PDF"]',
  emailPOButton: '[data-cy="menu-item-Email PO"]',
  sendEmailButton: 'button:contains("Send")',
  recepientEmailsInput: 'input',
  sendEmailSuccess: 'p:contains("Email sent successfully")',
  title: (name) => `h2:contains("${name}")`,
  detailsVendor: (name) =>
    `[data-cy="generic.upkeepEntity.vendor"]:contains("${name}")`,
  detailsRequisitioner: (name) =>
    `[data-cy="pages.purchaseOrders.labels.requisitioner"]:contains("${name}")`,
  detailsCompanyName: (name) =>
    `[data-cy="generic.labels.companyName"]:contains("${name}")`,
  additionalDetailsShippingMethod: (name) =>
    `[data-cy="Shipping Method"]:contains("${name}")`,
  additionalDetailsTerms: (name) => `[data-cy="Terms"]:contains("${name}")`,
  additionalDetailsShippingFob: (name) =>
    `[data-cy="F.O.B. Shipping Point"]:contains("${name}")`,
  partsReceivedInput: 'input[type="number"]',
  fulfillButtonOnFulfillCard: '[data-cy="Fulfill"]',
  detailsPartiallyFulfilledStatus:
    '[data-cy="generic.labels.partially-fulfilled"]',
  detailsFulfilledStatus: '[data-cy="generic.labels.fulfilled"]',
  generatePDFButton: 'p:contains("Generate PDF")',
  approvedStatus: '[data-cy="generic.labels.approved"]',
  declinedStatus: '[data-cy="generic.labels.declined"]',
  activityTab: '[data-cy="generic.labels.activity"]',
  messageTextarea: 'textarea:first()',
  sendMessageButton: '[data-cy="generic.send"]',
});

export default details;
