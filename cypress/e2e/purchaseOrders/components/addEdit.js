import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  title: '[data-cy="title"]',
  poNumber: '[data-cy="purchaseOrderNumber"]',
  vendor: '#VendorListButton',
  dueDate: '[name="dueDate"]',
  category: '#CategoryListButton',
  additionalDetails: '[data-cy="description"]',
  addPartsButton: 'button:contains("Add Parts")',
  createPurchaseOrdersButton: 'button:contains("Create Purchase Order")',
  saveChangesButton: 'button:contains("Save Changes")',
  companyAddressRadioButton: '[value="companyAddressOption"]',
  differentAddressRadioButton: '[value="differentAddress"]',
  companyName: '[data-cy="companyName"]',
  billingAddress: '[data-cy="address"]',
  billingCity: '[data-cy="city"]',
  billingState: '[data-cy="state"]',
  billingZipCode: '[data-cy="zipCode"]',
  billingPhoneNumber: '[data-cy="phoneNumber"]',
  billingFaxNumber: '[data-cy="faxNumber"]',
  shipToName: '[data-cy="paymentTerms"]',
  sameBillingAddressRadioButton: '[value="sameAsBilling"]',
  differentShippingAddressRadioButton: '[value="differentAddress""]',
  purchaseDate: '[name="purchaseDate"]',
  partName: (name) => `td:contains(${name}) .table-cell-interactive`,
  searchPartsToAddInput: '[data-cy="partSelectSearch"]',
  addPart: '.card-footer button:contains("Add")',
  dayOfTheMonth: (number) => `td:contains(${number})`,
  additionalDetailsTerms: '[data-cy="terms"]',
  additionalDetailsShippingMethod: '[data-cy="shippingMethod"]',
  additionalDetailsFobPoint: '[data-cy="fobPoint"]',
  additionalDetailsRequisitioner: '[data-cy="requisitioner"]',
  additionalDetailsNotes: '[data-cy="notes"]',
  additionalDetailsHideUpkeepLogo: '[data-cy="hideUpkeepLogo"]',
});

export default addEdit;
