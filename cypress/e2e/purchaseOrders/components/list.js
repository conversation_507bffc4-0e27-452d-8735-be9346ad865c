import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  createPurchaseOrderButton: 'button:contains("Create Purchase Order")',
  firstPurchaseOrderInRow: 'tbody tr:first()',
  purchaseOrderRow: (name) => `tr:contains("${name}")`,
  poRowContains: (name, contains) =>
    `tr:contains("${name}") td:contains(${contains})`,
  saveFilter: '[data-cy="Save"]:visible',
  searchBar: '[data-cy="search-bar"]',
  noResults: '[data-cy="No Results"]',
  resetFiltersButton: '[data-cy="components.templates.FilterBar.reset"]',
  filtersButton: 'button:contains("Filters")',
  statusFilter: 'button:contains("Status")',
  statusFilterFulfilled: `li:contains("Fulfilled")`,
  statusFilterApproved: `li:contains("Approved") input`,
  statusFilterPartiallyFulfilled: `li:contains("Partially Fulfilled")`,
  statusFilterAwaitingApproval: `li:contains("Awaiting Approval")`,
  statusFilterDeclined: `li:contains("Declined") input`,
  tableHeader: 'thead tr',
});

export default list;
