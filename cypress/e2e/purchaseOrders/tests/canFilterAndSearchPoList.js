import { upkeepPages } from '../../../support/constants';
import { approvePO, declinePO } from '../helpers';
import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';

const canFilterAndSearchPoList = () => {
  it(
    'can filter and search PO list',
    {
      testCaseId: 'QA-T6339',
    },
    () => {
      const now = Date.now();
      const partNumber = `a${now}`;
      const stringPartName = `search ${now}`;
      const toCreate = 4;
      const title = `searchAndFilterPO ${now}`;
      const purchaseOrder0 = `0 ${title}`;
      const purchaseOrder1 = `1 ${title}`;

      for (let i = 0; i < toCreate; i++) {
        h.createPartInventory(
          {
            numberPartQuantity: 10,
            partNumber: `${i}${partNumber}`,
            stringPartName: `${i} ${stringPartName}`,
          },
          true,
        ).then((part) => {
          h.createCustomPurchaseOrder(
            { title: `${i} ${title}` },
            {
              stringPartName: `${i} ${stringPartName}`,
              id: part.body.result.objectId,
              quantity: 19 + i,
            },
          );
        });
      }

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list
        .purchaseOrderRow(title)
        .shouldHaveLength(toCreate);

      // Searching by name
      // From the Requests list, type some text into the search bar
      purchaseOrdersPages.list.searchBar.type(now);
      cy.wait(1000);
      purchaseOrdersPages.list
        .purchaseOrderRow(title)
        .shouldHaveLength(toCreate);
      purchaseOrdersPages.list.searchBar.type('qwerty');
      purchaseOrdersPages.list.noResults.shouldBeVisible();
      purchaseOrdersPages.list.resetFiltersButton.click(); // clear state
      purchaseOrdersPages.list
        .purchaseOrderRow(title)
        .shouldHaveLength(toCreate);

      // Filtering by Status
      declinePO(purchaseOrder0);
      approvePO(purchaseOrder1);
      purchaseOrdersPages.list.statusFilter.click();
      purchaseOrdersPages.list.statusFilterApproved.shouldBeVisible();
      purchaseOrdersPages.list.statusFilterApproved.click();
      purchaseOrdersPages.list.statusFilterApproved
        .get()
        .invoke('prop', 'checked')
        .should('be.true');

      purchaseOrdersPages.list.statusFilterDeclined.click();
      purchaseOrdersPages.list.statusFilterDeclined
        .get()
        .invoke('prop', 'checked')
        .should('be.true');

      purchaseOrdersPages.list.saveFilter.click();
      purchaseOrdersPages.list.tableHeader.shouldBeVisible();
      purchaseOrdersPages.list
        .poRowContains(purchaseOrder0, 'declined')
        .shouldExist();
      purchaseOrdersPages.list
        .poRowContains(purchaseOrder1, 'approved')
        .shouldExist();
      purchaseOrdersPages.list.purchaseOrderRow(title).shouldHaveLength(2);

      purchaseOrdersPages.list.resetFiltersButton.click(); // clear state
      purchaseOrdersPages.list.tableHeader.shouldBeVisible();
      purchaseOrdersPages.list
        .purchaseOrderRow(title)
        .shouldHaveLength(toCreate);
    },
  );
};

export default canFilterAndSearchPoList;
