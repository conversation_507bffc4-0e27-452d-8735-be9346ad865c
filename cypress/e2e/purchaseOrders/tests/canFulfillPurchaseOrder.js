import { upkeepPages } from '../../../support/constants';
import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';

const canFulfillPurchaseOrder = () => {
  it(
    'should fulfill a purchase order',
    {
      testCaseId: 'QA-T6344',
    },
    () => {
      const now = Date.now();
      const partNumber = `fulfill${now}`;
      const stringPartName = `partFulfilled ${now}`;
      const title = `fulfill PO ${now}`;
      const requestQuantity = 16;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: requestQuantity,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();

      // approve purchase order
      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();
      purchaseOrdersPages.details.approveButton.click();

      // partially fulfill purchase order
      purchaseOrdersPages.details.fulfillButton.shouldBeVisible();
      purchaseOrdersPages.details.fulfillButton.click();

      purchaseOrdersPages.details.partsReceivedInput.shouldBeVisible();
      purchaseOrdersPages.details.partsReceivedInput.click();
      purchaseOrdersPages.details.partsReceivedInput.type(
        `{selectall}${requestQuantity / 2}`,
      );
      purchaseOrdersPages.details.fulfillButtonOnFulfillCard.click();
      purchaseOrdersPages.details.detailsPartiallyFulfilledStatus.shouldBeVisible();

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list
        .poRowContains(title, 'partially fulfilled')
        .shouldExist();

      purchaseOrdersPages.list.purchaseOrderRow(title).click();

      // completely fulfill purchase order
      purchaseOrdersPages.details.fulfillButton.shouldBeVisible();
      purchaseOrdersPages.details.fulfillButton.click();

      purchaseOrdersPages.details.partsReceivedInput.shouldBeVisible();
      purchaseOrdersPages.details.partsReceivedInput.click();
      purchaseOrdersPages.details.partsReceivedInput.type(
        `{selectall}${requestQuantity / 2}`,
      );
      purchaseOrdersPages.details.fulfillButtonOnFulfillCard.click();
      purchaseOrdersPages.details.detailsFulfilledStatus.shouldBeVisible();

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list.poRowContains(title, 'fulfilled').shouldExist();
    },
  );
};

export default canFulfillPurchaseOrder;
