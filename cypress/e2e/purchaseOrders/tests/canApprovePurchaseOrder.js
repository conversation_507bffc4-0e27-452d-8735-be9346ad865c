import { upkeepPages } from '../../../support/constants';
import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';

const canApprovePurchaseOrder = () => {
  it(
    'can approve Purchase Order',
    {
      testCaseId: 'QA-T6340',
    },
    () => {
      const now = Date.now();
      const partNumber = `approve${now}`;
      const stringPartName = `partApproved ${now}`;
      const title = `approve PO ${now}`;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: 19,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();

      purchaseOrdersPages.details.approveButton.click();
      purchaseOrdersPages.details.backButton.click();
      purchaseOrdersPages.list.poRowContains(title, 'approved').shouldExist();
    },
  );
};

export default canApprovePurchaseOrder;
