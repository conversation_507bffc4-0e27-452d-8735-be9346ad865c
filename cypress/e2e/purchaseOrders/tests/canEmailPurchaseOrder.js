import { upkeepPages } from '../../../support/constants';
import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';

const canFulfillPurchaseOrder = ({ emails }) => {
  it(
    'should email a purchase order',
    {
      testCaseId: 'QA-T6378',
    },
    () => {
      const now = Date.now();
      const partNumber = `fulfill${now}`;
      const stringPartName = `partFulfilled ${now}`;
      const title = `fulfill PO ${now}`;
      const requestQuantity = 16;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: requestQuantity,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();

      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();
      purchaseOrdersPages.details.elipsesButton.shouldBeVisible();
      purchaseOrdersPages.details.elipsesButton.click();

      purchaseOrdersPages.details.emailPOButton.shouldBeVisible();
      purchaseOrdersPages.details.emailPOButton.click();

      purchaseOrdersPages.details.recepientEmailsInput.shouldBeVisible();
      purchaseOrdersPages.details.recepientEmailsInput.click();
      purchaseOrdersPages.details.recepientEmailsInput.type(emails.ADMIN);

      purchaseOrdersPages.details.sendEmailButton.click();

      purchaseOrdersPages.details.sendEmailSuccess.shouldBeVisible();
    },
  );
};

export default canFulfillPurchaseOrder;
