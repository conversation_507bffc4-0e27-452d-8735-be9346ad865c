import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as poHelpers from '../../helpers/purchaseOrdersHelpers';

const techCanGeneratePdf = () => {
  it(
    'Tech can generate PDF via Details tab on Purchase Order detail view',
    { testCaseId: 'QA-T1162' },
    () => {
      const now = Date.now();
      const poTitle = `Tech PO ${now}`;
      const poDescription = 'Tech can generate pdf';

      // Create purchase order
      h.createPurchaseOrder({ title: poTitle, description: poDescription });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poHelpers.selectPoByName(poTitle);
      poHelpers.generatePoPdf();
    },
  );
};

export default techCanGeneratePdf;
