import faker from 'faker';
import { upkeepPages } from '../../../../support/constants';
import * as purchaseOrdersPages from '../../components';
import * as h from '../../../../helpers';

const techCanEditPurchaseOrders = (testId) => {
  it(
    'tech can edit all fields of a Purchase Order',
    {
      testCaseId: 'QA-T846',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const dayOfTheMonth = new Date().getDate();
      const partNumber = `ab${now}`;
      const stringPartName = `partB ${now}`;
      const title = `PO Edit ${now}`;
      const vendor = 'Business Vendor ABC';

      const poEditDetails = {
        title: `Edit Test Title ${now}`,
        name: 'tester Peralta',
        poNumber: faker.random.alphaNumeric(10),
        category: '',
        additionalDetails: faker.lorem.word(),
        company: {
          name: 'Test Inc',
          address: 'fake street 123',
        },
        shippingMethod: 'overnight',
        fobShippingPoint: '4321',
        requisitioner: 'mario bros',
        terms: 'testing terms agreement',
      };

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'TECH',
          {
            firstName: 'Cool',
            lastName: 'Tech',
          },
          sessionToken,
        );
      });
      h.createVendor(vendor);
      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: 100,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();
      purchaseOrdersPages.details.editButton.click();

      cy.wait(450);
      purchaseOrdersPages.addEdit.title.shouldBeVisible();
      purchaseOrdersPages.addEdit.title.type(poEditDetails.title);
      purchaseOrdersPages.addEdit.vendor.click();
      cy.contains(vendor).click();
      // h.closeTopPortal();
      purchaseOrdersPages.addEdit.additionalDetails.type(
        poEditDetails.additionalDetails,
      );

      // Billing Address
      purchaseOrdersPages.addEdit.differentAddressRadioButton.click();
      purchaseOrdersPages.addEdit.shipToName.type(poEditDetails.name);
      purchaseOrdersPages.addEdit.companyName.type(poEditDetails.company.name);

      // additional details
      purchaseOrdersPages.addEdit.purchaseDate.click({
        scrollBehavior: 'bottom',
      });
      purchaseOrdersPages.addEdit
        .dayOfTheMonth(dayOfTheMonth)
        .click({ scrollBehavior: 'bottom' });
      purchaseOrdersPages.addEdit.additionalDetailsTerms.type(
        poEditDetails.terms,
      );
      purchaseOrdersPages.addEdit.additionalDetailsShippingMethod.type(
        poEditDetails.shippingMethod,
      );
      purchaseOrdersPages.addEdit.additionalDetailsFobPoint.type(
        poEditDetails.fobShippingPoint,
      );
      purchaseOrdersPages.addEdit.additionalDetailsRequisitioner.type(
        poEditDetails.requisitioner,
      );

      purchaseOrdersPages.addEdit.additionalDetailsHideUpkeepLogo.click();
      purchaseOrdersPages.addEdit.saveChangesButton.click();
      purchaseOrdersPages.details.title(poEditDetails.title).shouldBeVisible();
      purchaseOrdersPages.details.detailsVendor(vendor).shouldBeVisible();
      purchaseOrdersPages.details
        .detailsCompanyName(poEditDetails.company.name)
        .shouldBeVisible();
      purchaseOrdersPages.details
        .additionalDetailsShippingMethod(poEditDetails.shippingMethod)
        .shouldExist();
      purchaseOrdersPages.details
        .additionalDetailsTerms(poEditDetails.terms)
        .shouldExist();
      purchaseOrdersPages.details
        .additionalDetailsShippingFob(poEditDetails.fobShippingPoint)
        .shouldExist();
    },
  );
};

export default techCanEditPurchaseOrders;
