import { upkeepPages } from '../../../support/constants';
import * as poPages from '../components';
import * as h from '../../../helpers';

const canOverFulfillPurchaseOrder = () => {
  it(
    'should over fulfill a purchase order',
    {
      testCaseId: 'QA-T6377',
    },
    () => {
      const now = Date.now();
      const partNumber = `overfulfill${now}`;
      const stringPartName = `partOverFulfilled ${now}`;
      const title = `overfulfill PO ${now}`;
      const requestQuantity = 16;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: requestQuantity,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();

      // approve purchase order
      poPages.list.purchaseOrderRow(title).shouldBeVisible();
      poPages.list.purchaseOrderRow(title).click();
      poPages.details.approveButton.click();

      // partially fulfill purchase order
      poPages.details.fulfillButton.shouldBeVisible();
      poPages.details.fulfillButton.click();

      poPages.details.partsReceivedInput.shouldBeVisible();
      poPages.details.partsReceivedInput.click();
      poPages.details.partsReceivedInput.type(
        `{selectall}${requestQuantity / 2}`,
      );
      poPages.details.fulfillButtonOnFulfillCard.click();
      poPages.details.detailsPartiallyFulfilledStatus.shouldBeVisible();

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poPages.list.poRowContains(title, 'partially fulfilled').shouldExist();

      poPages.list.purchaseOrderRow(title).click();

      // over fulfill purchase order
      poPages.details.fulfillButton.shouldBeVisible();
      poPages.details.fulfillButton.click();

      poPages.details.partsReceivedInput.shouldBeVisible();
      poPages.details.partsReceivedInput.click();
      poPages.details.partsReceivedInput.type(
        `{selectall}${requestQuantity / 2 + 1}`,
      );
      poPages.details.fulfillButtonOnFulfillCard.click();
      poPages.details.detailsFulfilledStatus.shouldBeVisible();

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poPages.list.poRowContains(title, 'fulfilled').shouldExist();
    },
  );
};

export default canOverFulfillPurchaseOrder;
