import { upkeepPages } from '../../../support/constants';
import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';

const canDeclinePurchaseOrder = () => {
  it(
    'can decline Purchase Order',
    {
      testCaseId: 'QA-T6341',
    },
    () => {
      const now = Date.now();
      const partNumber = `decline${now}`;
      const stringPartName = `partDeclined ${now}`;
      const title = `decline PO ${now}`;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: 19,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();

      purchaseOrdersPages.details.declineButton.click();
      purchaseOrdersPages.details.backButton.click();
      purchaseOrdersPages.list.poRowContains(title, 'declined').shouldExist();
    },
  );
};

export default canDeclinePurchaseOrder;
