import { upkeepPages } from '../../../../support/constants';
import * as poHelpers from '../../helpers/purchaseOrdersHelpers';
import * as h from '../../../../helpers';

const limTechCanGeneratePdf = () => {
  it(
    'Limited Tech user can generate a PDF on Purchase Order details view',
    { testCaseId: 'QA-T531' },
    () => {
      const now = Date.now();
      const title = `PO ${now}`;

      // Create a PO via api
      h.createPurchaseOrder({ title });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poHelpers.selectPoByName(title);
      poHelpers.verifyGeneratedPdf();
      poHelpers.generatePoPdf();
      cy.wait('@verifyGeneratedPdf').then((res) => {
        expect(res.response.statusCode).to.eq(200);
      });
    },
  );
};

export default limTechCanGeneratePdf;
