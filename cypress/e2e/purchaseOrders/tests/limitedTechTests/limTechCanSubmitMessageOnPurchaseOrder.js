import { upkeepPages } from '../../../../support/constants';
import * as poHelpers from '../../helpers/purchaseOrdersHelpers';
import * as h from '../../../../helpers';

const limTechCanSubmitMessageOnPurchaseOrder = () => {
  it(
    'Limited Tech user can submit a message via Activity tab on Purchase Order view',
    { testCaseId: 'QA-T533' },
    () => {
      const now = Date.now();
      const title = `PO ${now}`;
      const message = `Lim tech purchase order message ${now}`;

      // Create a PO via api
      h.createPurchaseOrder({ title });

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poHelpers.selectPoByName(title);
      poHelpers.submitMessageInActivityTab(message);
    },
  );
};

export default limTechCanSubmitMessageOnPurchaseOrder;
