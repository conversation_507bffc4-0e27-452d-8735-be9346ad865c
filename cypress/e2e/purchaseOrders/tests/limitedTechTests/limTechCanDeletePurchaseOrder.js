import { upkeepPages } from '../../../../support/constants';
import * as poHelpers from '../../helpers/purchaseOrdersHelpers';
import * as h from '../../../../helpers';

const limTechCanDeletePurchaseOrder = () => {
  it(
    'Limited Tech user can delete a Purchase Order',
    { testCaseId: 'QA-T527' },
    () => {
      const now = Date.now();
      const title = `PO ${now}`;

      // Create a PO via api
      h.createPurchaseOrder({ title });

      // Go to POs to select and delete
      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poHelpers.selectPoByName(title);
      poHelpers.deletePo();

      // Go back to POs page to verify PO has been deleted
      upkeepPages.PURCHASE_ORDERS_WEB.go();
      poHelpers.verifyPoDeleted(title);
    },
  );
};

export default limTechCanDeletePurchaseOrder;
