import faker from 'faker';
import { upkeepPages } from '../../../../support/constants';
import * as purchaseOrdersPages from '../../components';
import * as h from '../../../../helpers';

const limTechCanSubmitPurchaseOrder = (testId) => {
  it(
    'limited tech can submit a purchase order',
    {
      testCaseId: 'QA-T525',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const partName = `part${now}`;
      const partNumber = `a${now}`;
      const vendor = 'Business Vendor ABC';
      const dayOfTheMonth = new Date().getDate();

      h.createVendor(vendor);
      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName: partName,
        },
        true,
      );

      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          {
            firstName: 'Cool',
            lastName: 'Lim Tech',
          },
          sessionToken,
        );
      });

      const poDetails = {
        title: `Test Title ${now}`,
        name: 'tester lim tech',
        poNumber: faker.random.alphaNumeric(10),
        category: '',
        additionalDetails: `details ${now}`,
        company: {
          name: 'Test Inc',
          address: 'fake street 123',
        },
        shippingMethod: 'Overnight',
        fobShippingPoint: '123',
        requisitioner: 'req test',
        terms: 'test terms',
      };

      upkeepPages.PURCHASE_ORDERS_WEB.go();
      purchaseOrdersPages.list.createPurchaseOrderButton.shouldBeVisible();
      purchaseOrdersPages.list.createPurchaseOrderButton.click();

      // Details
      cy.wait(450);
      purchaseOrdersPages.addEdit.title.shouldBeVisible();
      purchaseOrdersPages.addEdit.title.type(poDetails.title);
      purchaseOrdersPages.addEdit.vendor.click();
      cy.contains(vendor).click();
      // eslint-disable-next-line cypress/no-force
      cy.contains('Line Items').click({ force: true });
      purchaseOrdersPages.addEdit.additionalDetails.type(
        poDetails.additionalDetails,
      );

      // Line Items
      purchaseOrdersPages.addEdit.addPartsButton.click();
      purchaseOrdersPages.addEdit.searchPartsToAddInput.type(partName);
      cy.wait(1500); // cypress is faster than the crash
      purchaseOrdersPages.addEdit.partName(partName).click();
      purchaseOrdersPages.addEdit.addPart.click();

      // Billing Address
      purchaseOrdersPages.addEdit.differentAddressRadioButton.click();
      purchaseOrdersPages.addEdit.shipToName.type(poDetails.name);
      purchaseOrdersPages.addEdit.companyName.type(poDetails.company.name);

      // additional details
      purchaseOrdersPages.addEdit.purchaseDate.click({
        scrollBehavior: 'bottom',
      });
      purchaseOrdersPages.addEdit
        .dayOfTheMonth(dayOfTheMonth)
        .click({ scrollBehavior: 'bottom' });
      purchaseOrdersPages.addEdit.additionalDetailsTerms.type(poDetails.terms);
      purchaseOrdersPages.addEdit.additionalDetailsShippingMethod.type(
        poDetails.shippingMethod,
      );
      purchaseOrdersPages.addEdit.additionalDetailsFobPoint.type(
        poDetails.fobShippingPoint,
      );
      purchaseOrdersPages.addEdit.additionalDetailsRequisitioner.type(
        poDetails.requisitioner,
      );
      purchaseOrdersPages.addEdit.additionalDetailsHideUpkeepLogo.click();
      purchaseOrdersPages.addEdit.createPurchaseOrdersButton.click();

      purchaseOrdersPages.list
        .purchaseOrderRow(poDetails.title)
        .shouldBeVisible();
      cy.reload();
      purchaseOrdersPages.list
        .poRowContains(poDetails.title, vendor)
        .shouldBeVisible();

      purchaseOrdersPages.list.purchaseOrderRow(poDetails.title).click();

      purchaseOrdersPages.details.title(poDetails.title).shouldBeVisible();
      purchaseOrdersPages.details.detailsVendor(vendor).shouldBeVisible();
      purchaseOrdersPages.details
        .detailsCompanyName(poDetails.company.name)
        .shouldBeVisible();
      purchaseOrdersPages.details
        .additionalDetailsShippingMethod(poDetails.shippingMethod)
        .shouldExist();
      purchaseOrdersPages.details
        .additionalDetailsTerms(poDetails.terms)
        .shouldExist();
      purchaseOrdersPages.details
        .additionalDetailsShippingFob(poDetails.fobShippingPoint)
        .shouldExist();
    },
  );
};

export default limTechCanSubmitPurchaseOrder;
