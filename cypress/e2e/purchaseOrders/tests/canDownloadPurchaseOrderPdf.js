import { upkeepPages } from '../../../support/constants';

import * as purchaseOrdersPages from '../components';
import * as h from '../../../helpers';
import * as purchaseOrdersHelpers from '../helpers/purchaseOrdersHelpers';

const canDownloadPurchaseOrderPdf = () => {
  it(
    'should download a purchase order pdf',
    {
      testCaseId: 'QA-T6345',
    },
    () => {
      const now = Date.now();
      const partNumber = `fulfill${now}`;
      const stringPartName = `partFulfilled ${now}`;
      const title = `fulfill PO ${now}`;

      h.createPartInventory(
        {
          numberPartQuantity: 10,
          partNumber,
          stringPartName,
        },
        true,
      ).then((part) => {
        h.createCustomPurchaseOrder(
          { title },
          {
            stringPartName,
            id: part.body.result.objectId,
            quantity: 16,
          },
        );
      });

      upkeepPages.PURCHASE_ORDERS_WEB.go();

      purchaseOrdersPages.list.purchaseOrderRow(title).shouldBeVisible();
      purchaseOrdersPages.list.purchaseOrderRow(title).click();

      purchaseOrdersHelpers.verifyGeneratedPdf();
      purchaseOrdersPages.details.elipsesButton.shouldBeVisible();
      purchaseOrdersPages.details.elipsesButton.click();
      purchaseOrdersPages.details.generatePDFButton.shouldBeVisible();
      purchaseOrdersPages.details.generatePDFButton.click();
      cy.wait('@verifyGeneratedPdf').then((res) => {
        expect(res.response.statusCode).to.eq(200);
      });
    },
  );
};

export default canDownloadPurchaseOrderPdf;
