import { upkeepPages } from '../../../../support/constants';
import * as poHelpers from '../../helpers/purchaseOrdersHelpers';
import * as h from '../../../../helpers';
import * as poPages from '../../components';

const canSubmitMessageViaActivityTab = (emails, testId) => {
  it(
    'View Only user can submit a message via activity tab on Purchase Orders view',
    { testCaseId: 'QA-T691' },
    () => {
      const now = Date.now();
      const title = `Purchase Order ${now}`;
      const message = `View Only Purchase Order message ${now}`;

      // Create a purchase order as Admin user
      h.createPurchaseOrder({ title });

      // Login as View Only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Go to Purchase Orders page as View Only user
      upkeepPages.PURCHASE_ORDERS_WEB.go();

      // Select Purchase Order to submit message
      poPages.list.purchaseOrderRow(title).click();
      poHelpers.submitMessageInActivityTab(message);
    },
  );
};

export default canSubmitMessageViaActivityTab;
