import { leftNavigation } from '../../../../support/constants';

const canNavigateToPurchaseOrders = (emails, testId) => {
  it(
    'View Only user can navigate to Purchase Orders page',
    { testCaseId: 'QA-T689' },
    () => {
      // Sign in as View Only user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          { email: emails.VIEW_ONLY },
          win.localStorage.authToken,
        );
      });

      // Navigate to Purchase Orders page
      cy.contains('Work Orders').should('be.visible');
      cy.get(leftNavigation.PURCHASE_ORDERS.navSelector).click();
      cy.url().should('contain', '/web/purchase-orders');
      cy.contains('Purchase Orders').should('be.visible');
    },
  );
};

export default canNavigateToPurchaseOrders;
