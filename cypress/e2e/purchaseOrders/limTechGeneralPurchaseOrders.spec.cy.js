import { limitedTechTests } from './tests';
import filterTests from '../../support/filterTests';

filterTests(['all', 'ui'], () => {
  Cypress.on('uncaught:exception', () => false);
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const teamId = 'Team name';
  const testId = 'limTechPo';
  const emails = {
    LIMITED_TECH: `engineering-test+${testId}_lim_tech_${now}@${domain}`,
  };

  describe('Limited Tech general Purchase Order tests', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamId);

      // Sign in as limited tech user
      cy.window().then((win) => {
        cy.switchUserAndLoginUser(
          testId,
          'LIMITED_TECH',
          { email: emails.LIMITED_TECH },
          win.localStorage.authToken,
        );
      });
    });

    limitedTechTests.limTechCanDeletePurchaseOrder();
    limitedTechTests.limTechCanGeneratePdf();
    limitedTechTests.limTechCanSubmitMessageOnPurchaseOrder();
  });
});
