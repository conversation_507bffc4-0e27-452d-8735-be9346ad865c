import { techTests } from './tests';
import filterTests from '../../support/filterTests';

filterTests(['all', 'tier2', 'ui'], () => {
  const teamId = 'mega team';
  const testId = 'techPurchaseOrders';

  describe('Technician can create Purchase Orders', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, ['TECH'], 'BUSINESS_PLUS', teamId);
    });

    techTests.techCanSubmitPurchaseOrder(testId);
    techTests.techCanEditPurchaseOrders(testId);
  });
});
