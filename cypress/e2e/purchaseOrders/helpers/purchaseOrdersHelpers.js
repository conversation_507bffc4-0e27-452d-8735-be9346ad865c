import * as poPages from '../components';

export const verifyGeneratedPdf = () => {
  cy.intercept({
    method: 'POST',
    url: `${Cypress.env('CYPRESS_API_URL')}/api/purchase-order`,
  }).as('verifyGeneratedPdf');
};

export const selectFirstPoInList = () => {
  poPages.list.firstPurchaseOrderInRow.shouldBeVisible();
  poPages.list.firstPurchaseOrderInRow.click();
};

export const selectPoByName = (name) => {
  poPages.list.purchaseOrderRow(name).shouldBeVisible();
  poPages.list.purchaseOrderRow(name).click();
};

export const deletePo = () => {
  poPages.details.elipsesButton.shouldBeVisible();
  poPages.details.elipsesButton.click();
  poPages.details.deleteButton.shouldBeVisible();
  poPages.details.deleteButton.click();
  poPages.details.confirmDeleteButton.click();
};

export const verifyPoDeleted = (name) => {
  cy.contains('h2', 'Purchase Orders').should('be.visible');
  poPages.list.purchaseOrderRow(name).shouldNotExist();
};

export const generatePoPdf = () => {
  poPages.details.elipsesButton.shouldBeVisible();
  poPages.details.elipsesButton.click();
  poPages.details.generatePdfButton.shouldBeVisible();
  poPages.details.generatePdfButton.openLinkInNewTab();
};

export const submitMessageInActivityTab = (message) => {
  poPages.details.activityTab.click();
  poPages.details.messageTextarea.type(message);
  poPages.details.sendMessageButton.click();
  cy.contains('[display="inline-block"]', message).should('be.visible');
};
