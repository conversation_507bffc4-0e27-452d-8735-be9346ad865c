import { allSideNavigations, excludeMatrix } from './test-data';

export function runNavTest(planLevel, roleLevel) {
  describe(`Side Nav ${planLevel} for ${roleLevel}`, () => {
    it(`${planLevel} ${roleLevel} sees correct side menu in React`, () => {
      cy.logInAs(
        `nav-${planLevel.substring(0, 3)}-${roleLevel.substring(0, 3)}`,
        roleLevel,
        planLevel,
      ).then(() => {
        cy.get('#upkeep-logo');
        const checkNavItems = allSideNavigations.filter(
          (nav) => !excludeMatrix[planLevel][roleLevel].includes(nav),
        );
        checkNavItems.forEach((nav) => {
          cy.get(nav).scrollIntoView().should('exist');
        });
        excludeMatrix[planLevel][roleLevel].forEach((nav) => {
          cy.get(nav).should('not.exist');
        });
      });
    });
  });
}
