import filterTests from '../../support/filterTests';
import { runNavTest } from './helpers';
import { UPKEEP_PAGES } from '../../support/constants';

filterTests(['all', 'ui', 'tier2'], () => {
  runNavTest('BASIC', 'ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('BASIC', 'LIMITED_ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('BASIC', 'LIMITED_TECH', UPKEEP_PAGES.METERS);
  runNavTest('BASIC', 'REQUESTER');
  runNavTest('BASIC', 'TECH', UPKEEP_PAGES.METERS);
  runNavTest('BASIC', 'VIEW_ONLY', UPKEEP_PAGES.METERS);

  runNavTest('BUSINESS_PLUS', 'ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('BUSINESS_PLUS', 'LIMITED_ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('BUSINESS_PLUS', 'LIMITED_TECH', UPKEEP_PAGES.METERS);
  runNavTest('BUSINESS_PLUS', 'REQUESTER');
  runNavTest('BUSINESS_PLUS', 'TECH', UPKEEP_PAGES.METERS);
  runNavTest('BUSINESS_PLUS', 'VIEW_ONLY', UPKEEP_PAGES.METERS);

  runNavTest('PROFESSIONAL', 'ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('PROFESSIONAL', 'LIMITED_ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('PROFESSIONAL', 'LIMITED_TECH', UPKEEP_PAGES.METERS);
  runNavTest('PROFESSIONAL', 'REQUESTER');
  runNavTest('PROFESSIONAL', 'TECH', UPKEEP_PAGES.METERS);
  runNavTest('PROFESSIONAL', 'VIEW_ONLY', UPKEEP_PAGES.METERS);

  runNavTest('STARTER', 'ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('STARTER', 'LIMITED_ADMIN', UPKEEP_PAGES.METERS);
  runNavTest('STARTER', 'LIMITED_TECH', UPKEEP_PAGES.METERS);
  runNavTest('STARTER', 'REQUESTER');
  runNavTest('STARTER', 'TECH', UPKEEP_PAGES.METERS);
  runNavTest('STARTER', 'VIEW_ONLY', UPKEEP_PAGES.METERS);
});
