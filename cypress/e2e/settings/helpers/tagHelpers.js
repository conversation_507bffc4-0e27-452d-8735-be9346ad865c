import * as settingsPages from '../components';

/**
 * Select tag model on Tags/Settings page
 * @param {string} tagName the name of the tag to create
 * @param {string[]} models the models to select in the dropdown
 */
export const addTag = (tagName, models) => {
  settingsPages.withSettingsRevampOn.tagsSettings.addButton.click();
  settingsPages.withSettingsRevampOn.tagsSettings.tagNameInput.type(tagName);
  if (Array.isArray(models)) {
    models.forEach((model) => {
      settingsPages.withSettingsRevampOn.tagsSettings.modelsInput.select(model);
    });
  } else {
    settingsPages.withSettingsRevampOn.tagsSettings.modelsInput.select(models, {
      force: true,
    });
  }
  settingsPages.withSettingsRevampOn.tagsSettings.modelsInput.click({
    force: true,
  });
  settingsPages.withSettingsRevampOn.tagsSettings.submitTagButton.click();
};

/**
 *
 * @param {string} tagName the original name of the tag to change
 * @param {string} newTagName the new name of the tag
 * @param {string} newModel the extra model to select in the dropdown
 */
export const editTag = (tagName, newTagName, newModel) => {
  settingsPages.withSettingsRevampOn.tagsSettings
    .tagRow(tagName)
    .get()
    .within(() => {
      settingsPages.withSettingsRevampOn.tagsSettings.actionsMenu.click();
    });
  settingsPages.withSettingsRevampOn.tagsSettings.editButton.click();
  settingsPages.withSettingsRevampOn.tagsSettings.tagNameInput.type(newTagName);
  settingsPages.withSettingsRevampOn.tagsSettings.modelsInput.click();
  settingsPages.withSettingsRevampOn.tagsSettings.selectModel(newModel).click();
  settingsPages.withSettingsRevampOn.tagsSettings.modelsInput.click({
    force: true,
  });
  settingsPages.withSettingsRevampOn.tagsSettings.submitTagButton.click();
};

/**
 *
 * @param {string} tagName the name of the tag that was created
 * @param {string} models the models that were added to the tag
 */
export const verifyTagCreated = (tagName, models) => {
  settingsPages.withSettingsRevampOn.tagsSettings
    .tagRow(tagName)
    .shouldBeVisible();
  settingsPages.withSettingsRevampOn.tagsSettings
    .tagRow(tagName)
    .get()
    .within(() => {
      models.forEach((model) => {
        cy.contains(model.toLowerCase()).should('be.visible');
      });
    });
};

/**
 *
 * @param {string} tagName the name of the tag to delete
 */
export const deleteTag = (tagName) => {
  settingsPages.withSettingsRevampOn.tagsSettings
    .tagRow(tagName)
    .get()
    .within(() => {
      settingsPages.withSettingsRevampOn.tagsSettings.actionsMenu.click();
    });
  settingsPages.withSettingsRevampOn.tagsSettings.deleteButton.click();
  settingsPages.withSettingsRevampOn.tagsSettings.deleteConfirmButton.click();
  settingsPages.withSettingsRevampOn.tagsSettings
    .tagRow(tagName)
    .shouldNotExist();
};
