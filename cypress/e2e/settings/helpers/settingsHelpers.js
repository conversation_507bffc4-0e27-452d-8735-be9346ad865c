import * as settingsPages from '../components';

export const visitApiSettings = () => {
  settingsPages.withSettingsRevampOn.sidebar.api.click();
  cy.url().should('include', '/web/settings/sections/api');
};

export const verifyApiSettingsElements = () => {
  settingsPages.withSettingsRevampOn.apiSettings.changeButton.shouldBeVisible();
  settingsPages.withSettingsRevampOn.apiSettings.versioningLink.shouldBeVisible();
  settingsPages.withSettingsRevampOn.apiSettings.devDocsLink.shouldBeVisible();
};

export const verifyDevDocsLinkUrl = () => {
  settingsPages.withSettingsRevampOn.apiSettings.devDocsLink
    .get()
    .should(($link) => {
      expect($link.prop('href')).to.include('developers.onupkeep.com');
    });
};

export const changeApiVersion = (version) => {
  settingsPages.withSettingsRevampOn.apiSettings.changeButton.click();
  settingsPages.withSettingsRevampOn.apiSettings.apiVersionModalHeader.shouldBeVisible();

  settingsPages.withSettingsRevampOn.apiSettings.versionListButton.click();
  settingsPages.withSettingsRevampOn.apiSettings.versionOption(version).click();
  settingsPages.withSettingsRevampOn.apiSettings.changeApiVersionButton.click();
  settingsPages.withSettingsRevampOn.apiSettings.apiVersionModalHeader.shouldNotExist();
};

export const verifyApiVersion = (version) => {
  settingsPages.withSettingsRevampOn.apiSettings
    .apiVersion(version)
    .shouldBeVisible();
};

export const visitAuthenticationSettings = () => {
  settingsPages.withSettingsRevampOn.sidebar.authentication.click();
  cy.url().should('contain', '/web/settings/sections/authentication');
  cy.contains('SAML Authentication').should('be.visible');
};

export const visitWebhookSettings = () => {
  settingsPages.withSettingsRevampOn.sidebar.webhooks.click();
  cy.url().should('contain', '/web/settings/sections/webhooks');
};

export const visitTagsSettings = () => {
  settingsPages.withSettingsRevampOn.sidebar.tags.click();
  cy.url().should('contain', '/web/settings/sections/tags');
};

export const addWebhook = (title, endpoint) => {
  settingsPages.withSettingsRevampOn.webhookSettings.addWebhookButton.click();
  settingsPages.withSettingsRevampOn.webhookSettings.titleInput
    .click()
    .type(title);
  settingsPages.withSettingsRevampOn.webhookSettings.endpointInput
    .click()
    .type(endpoint);
  settingsPages.withSettingsRevampOn.webhookSettings.allEventsRadio.click();
  settingsPages.withSettingsRevampOn.webhookSettings.submitWebhook.click();
  cy.contains('Webhook created').should('be.visible');
};

export const verifyWebhookCreated = (title, endpoint) => {
  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookRow(title)
    .shouldBeVisible();
  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookRow(endpoint)
    .shouldBeVisible();
};

export const deleteWebhook = (title) => {
  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookOptions(title)
    .click();
  settingsPages.withSettingsRevampOn.webhookSettings.deleteWebhookOption.click();
  settingsPages.withSettingsRevampOn.webhookSettings.deleteButton.click();
  cy.contains('Webhook deleted').should('be.visible');
};

export const verifyWebhookDeleted = (title, endpoint) => {
  cy.reload();
  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookRow(title)
    .shouldNotExist();
  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookRow(endpoint)
    .shouldNotExist();
};

export const updateWebhook = (title, editedTitle, editedEndpoint) => {
  Cypress.on('uncaught:exception', () => false);

  settingsPages.withSettingsRevampOn.webhookSettings
    .webhookOptions(title)
    .click();
  cy.wait(500);
  settingsPages.withSettingsRevampOn.webhookSettings.editWebhookOption.click();
  settingsPages.withSettingsRevampOn.webhookSettings.titleInput
    .click()
    .clear()
    .type(editedTitle);
  settingsPages.withSettingsRevampOn.webhookSettings.endpointInput
    .click()
    .clear()
    .type(editedEndpoint);

  settingsPages.withSettingsRevampOn.webhookSettings.updateWebhookButton.click();
};

export const visitWorkOrderSettings = () => {
  settingsPages.withSettingsRevampOn.sidebar.workOrders.click();
  cy.url().should('include', '/web/settings/sections/work-orders/general');
};

export const closeSettings = () => {
  settingsPages.settingsTabs.closeButton.click();
};
