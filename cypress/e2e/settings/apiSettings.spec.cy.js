import filterTests from '../../support/filterTests';
import * as apiSettingsTests from './tests/apiSettingsTests';

filterTests(['all', 'ui', 'tier2'], () => {
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const testId = 'api-settings';
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
  };

  describe('API settings', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'My Team', emails);
    });

    apiSettingsTests.canCheckDevDocs();
    apiSettingsTests.canUpdateApiVersion();
    apiSettingsTests.shouldCheckDeveloperDocsLink();
  });
});
