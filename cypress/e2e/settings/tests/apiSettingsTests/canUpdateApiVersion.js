import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';

const canUpdateApiVersion = () => {
  it('Can update API version', () => {
    Cypress.on('uncaught:exception', () => false);
    const version = '2022-03-17';

    upkeepPages.SETTINGS.go();

    settingsHelpers.visitApiSettings();
    settingsHelpers.changeApiVersion(version);
    settingsHelpers.verifyApiVersion(version);
  });
};

export default canUpdateApiVersion;
