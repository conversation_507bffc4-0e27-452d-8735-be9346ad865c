import { apiSettings, withSettingsRevampOn } from '../../components';
import { upkeepPages } from '../../../../support/constants';

const shouldCheckDeveloperDocsLink = () => {
  it('should verify developer docs link', { testCaseId: 'QA-T5552' }, () => {
    upkeepPages.SETTINGS.go();

    const devDocsUrl = 'developers.onupkeep.com';
    withSettingsRevampOn.sidebar.api.click();
    cy.url().should('include', '/settings/sections/api');
    apiSettings.changeButton.shouldExist();
    apiSettings.devDocsLink.shouldExist();
    // Verify if the href includes the expected value
    apiSettings.devDocsLink.get().should(($link) => {
      const href = $link.prop('href');
      expect(href).to.include(devDocsUrl);
    });
  });
};

export default shouldCheckDeveloperDocsLink;
