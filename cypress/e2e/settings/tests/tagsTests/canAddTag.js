import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';
import * as tagHelpers from '../../helpers/tagHelpers';

const canAddTag = () => {
  it('Admin can navigate to tags tab', () => {
    upkeepPages.SETTINGS.go();
    settingsHelpers.visitTagsSettings();
  });

  it('Admin can add a tag', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const title = `Tag ${now}`;
    const models = ['Files'];
    upkeepPages.SETTINGS.go();

    settingsHelpers.visitTagsSettings();
    tagHelpers.addTag(title, models);
    tagHelpers.verifyTagCreated(title, models);
  });
};

export default canAddTag;
