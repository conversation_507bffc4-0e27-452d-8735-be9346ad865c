import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';
import * as tagHelpers from '../../helpers/tagHelpers';

const canEditTag = () => {
  it('Admin can edit a tag', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const title = `Tag ${now}`;
    const models = ['Files'];
    upkeepPages.SETTINGS.go();

    settingsHelpers.visitTagsSettings();
    tagHelpers.addTag(title, models);
    tagHelpers.verifyTagCreated(title, models);

    const editedTitle = `Edited ${title}`;
    const newModel = 'Parts';
    tagHelpers.editTag(title, editedTitle, newModel);
    tagHelpers.verifyTagCreated(editedTitle, [newModel]);
  });
};

export default canEditTag;
