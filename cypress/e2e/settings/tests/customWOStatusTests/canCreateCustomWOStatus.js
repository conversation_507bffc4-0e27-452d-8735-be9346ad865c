import { customWOStatusSettings } from '../../components/withSettingsRevampOn';

const canCreateCustomWOStatus = () => {
  it(
    'Admin should be able to create a new Custom Work Order Status',
    {
      testCaseId: 'QA-T6372',
      featureFlags: { customWOStatuses: true },
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const statusName = 'Awaiting Approval';
      customWOStatusSettings.createButton.click();
      customWOStatusSettings.statusNameInput.type(statusName);
      customWOStatusSettings.submitButton.click();
      customWOStatusSettings
        .listViewValue(`${statusName}`)
        .should('be.visible');
      customWOStatusSettings.listCountText.should('have.text', '5 statuses');
    },
  );
};

export default canCreateCustomWOStatus;
