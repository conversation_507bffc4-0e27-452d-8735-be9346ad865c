import { customWOStatusSettings } from '../../components/withSettingsRevampOn';

const canDeleteCustomWOStatus = () => {
  it(
    'Admin should be able to delete an existing Custom Work Order Status',
    {
      testCaseId: 'QA-T6374',
      featureFlags: { customWOStatuses: true },
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const oldStatusName = 'Awaiting Approval';
      customWOStatusSettings.createButton.click();
      customWOStatusSettings.statusNameInput.type(oldStatusName);
      customWOStatusSettings.submitButton.click();
      customWOStatusSettings
        .listViewValue(`${oldStatusName}`)
        .should('be.visible');
      customWOStatusSettings.openMenuButton.click();
      customWOStatusSettings.deleteButton.click();
      customWOStatusSettings.deleteConfirmButton.click();
      cy.wait(1500);
      cy.reload();
      customWOStatusSettings.listCountText.should('have.text', '4 statuses');
    },
  );
};

export default canDeleteCustomWOStatus;
