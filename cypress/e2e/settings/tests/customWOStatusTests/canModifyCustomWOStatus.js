import { customWOStatusSettings } from '../../components/withSettingsRevampOn';

const canModifyCustomWOStatus = () => {
  it(
    'Admin should be able to modify an existing Custom Work Order Status',
    {
      testCaseId: 'QA-T6373',
      featureFlags: { customWOStatuses: true },
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const oldStatusName = 'Awaiting Approval';
      customWOStatusSettings.createButton.click();
      customWOStatusSettings.statusNameInput.type(oldStatusName);
      customWOStatusSettings.submitButton.click();
      customWOStatusSettings
        .listViewValue(`${oldStatusName}`)
        .should('be.visible');
      cy.wait(1500);
      const newStatusName = 'Pending Approval';
      customWOStatusSettings.openMenuButton.click();
      customWOStatusSettings.modifyButton.click();
      cy.contains('Edit Custom Status').should('be.visible');
      customWOStatusSettings.statusNameInput.clear().type(newStatusName);
      customWOStatusSettings.submitButton.click();
      customWOStatusSettings
        .listViewValue(`${newStatusName}`)
        .should('be.visible');
    },
  );
};

export default canModifyCustomWOStatus;
