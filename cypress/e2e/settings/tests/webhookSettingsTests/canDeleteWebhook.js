import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';

const canDeleteWebhook = () => {
  it('Admin can delete a webhook', () => {
    Cypress.on('uncaught:exception', () => false);

    const now = Date.now();
    const title = `Webhook ${now}`;
    const endpoint = `https://mywebsite.com/api/v2/webhooks/${now}`;

    upkeepPages.SETTINGS.go();

    // Create a webhook to delete
    settingsHelpers.visitWebhookSettings();
    settingsHelpers.addWebhook(title, endpoint);
    settingsHelpers.verifyWebhookCreated(title, endpoint);

    // Delete created webhook
    settingsHelpers.deleteWebhook(title);
    settingsHelpers.verifyWebhookDeleted(title, endpoint);
  });
};

export default canDeleteWebhook;
