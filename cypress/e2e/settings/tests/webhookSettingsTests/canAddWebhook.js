import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';

const canAddWebhook = () => {
  it('<PERSON>min can navigate to webhooks tab', { testCaseId: 'QA-T113' }, () => {
    upkeepPages.SETTINGS.go();

    settingsHelpers.visitWebhookSettings();
  });

  it('Admin can add a webhook item', () => {
    Cypress.on('uncaught:exception', () => false);

    const now = Date.now();
    const title = `Webhook ${now}`;
    const endpoint = 'https://mywebsite.com/api/v2/webhooks';
    upkeepPages.SETTINGS.go();

    settingsHelpers.visitWebhookSettings();
    settingsHelpers.addWebhook(title, endpoint);
    settingsHelpers.verifyWebhookCreated(title, endpoint);
  });
};

export default canAddWebhook;
