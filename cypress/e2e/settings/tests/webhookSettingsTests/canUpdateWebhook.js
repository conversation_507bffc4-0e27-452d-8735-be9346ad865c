import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../helpers/settingsHelpers';

const canUpdateWebhook = () => {
  it('Can update webhook', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const title = `Webhook ${now}`;
    const endpoint = `https://mywebsite.com/api/v2/webhooks/${now}`;

    const editedTitle = `Webhook ${now}_edited`;
    const editedEndpoint = `https://mywebsite.com/api/v2/webhooks/${now}/edited`;

    upkeepPages.SETTINGS.go();

    // Create a webhook to update
    settingsHelpers.visitWebhookSettings();
    settingsHelpers.addWebhook(title, endpoint);
    settingsHelpers.verifyWebhookCreated(title, endpoint);

    // Update created webhook
    settingsHelpers.updateWebhook(title, editedTitle, editedEndpoint);
    settingsHelpers.verifyWebhookCreated(editedTitle, editedEndpoint);
  });
};

export default canUpdateWebhook;
