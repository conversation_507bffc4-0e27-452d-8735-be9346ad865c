import filterTests from '../../support/filterTests';
import * as webhookTests from './tests/webhookSettingsTests';

filterTests(['all', 'ui'], () => {
  describe('Webhook Settings', () => {
    const testId = 'webhook-settings';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, ['ADMIN'], 'BUSINESS_PLUS', 'My Cool Team');
    });

    webhookTests.canAddWebhook();
    webhookTests.canUpdateWebhook();
    webhookTests.canDeleteWebhook();
  });
});
