import { configureSelectorProxy } from '../../../../helpers';

const apiSettings = configureSelectorProxy({
  changeButton: 'button:contains("Change")',
  versioningLink: '[href="https://developers.onupkeep.com/#versioning"]',
  devDocsLink: '[href="https://developers.onupkeep.com"]',
  apiVersionModalHeader: '#portal span:contains("Change Default API Version")',
  versionListButton: '[id="VersionListButton"]',
  versionOption: (version) => `[data-cy="${version}"]`,
  changeApiVersionButton: 'div#portal button:contains("Change")',
  apiVersion: (version) => `span:contains("${version}")`,
});

export default apiSettings;
