import { configureSelectorProxy } from '../../../../helpers';

const webhookSettings = configureSelectorProxy({
  addWebhookButton: 'button:contains("Add Webhook")',
  titleInput: 'input#name',
  endpointInput: 'input#endpoint',
  allEventsRadio: 'input[id="select-eventsAll events"]',
  submitWebhook: '#portal button:contains("Add Webhook")',
  webhookRow: (name) => `tbody tr:contains("${name}")`,
  webhookOptions: (name) => `tr:contains("${name}") button`,
  deleteWebhookOption: 'button:contains("Delete")',
  deleteButton: '.card-footer button:contains("Delete")',
  editWebhookOption: 'button:contains("Edit")',
  updateWebhookButton: 'button:contains("Update Webhook")',
});

export default webhookSettings;
