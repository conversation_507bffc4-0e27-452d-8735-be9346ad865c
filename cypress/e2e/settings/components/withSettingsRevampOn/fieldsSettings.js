import { configureSelectorProxy } from '../../../../helpers';

const fieldsSettings = configureSelectorProxy({
  assetsField: '[data-test="assetrow"]',
  createFieldButton: '[data-cy="CreateFieldButton"]',
  singleLineText: '[data-cy="menu-item-Single Line Text"]',
  multiLineText: '[data-cy="menu-item-Multi-Line Text"]',
  dropdown: '[data-cy="menu-item-Dropdown"]',
  date: '[data-cy="menu-item-Date"]',
  number: '[data-cy="menu-item-Number"]',
  currency: '[data-cy="menu-item-Currency"]',
  fieldInput: '[data-cy="Field NameInput"] input',
  createFieldDialogButton: '[role="dialog"] [data-cy="CreateFieldButton"]',
  dropdownOptionField: '[data-cy="undefinedInput"] input',
});

export default fieldsSettings;
