import { configureSelectorProxy } from '../../../../helpers';

const woConfigurationSettings = configureSelectorProxy({
  menuSelector: '[data-cy="Statuses"]',
  listViewTable: '[data-cy="wo-status-list-view"]',
  listCountText: '[data-cy="wo-status-count-text"]',
  listViewValue: (value) => `[data-cy="${value}"]`,
  openMenuButton: '[data-cy="icon-button"]',
  createButton: '[data-cy="wo-status-create-button"]',
  modifyButton: '[data-cy="wo-status-modify-button"]',
  deleteButton: '[data-cy="wo-status-delete-button"]',
  submitButton: '[data-cy="wo-status-submit-button"]',
  statusNameInput: '[data-cy="wo-status-name-input"]',
  deleteConfirmButton: '[data-cy="Delete"]',
});

export default woConfigurationSettings;
