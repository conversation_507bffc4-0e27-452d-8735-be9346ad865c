import { configureSelectorProxy } from '../../../../helpers';

const sidebar = configureSelectorProxy({
  api: '[href="/web/settings/sections/api"]',
  assetsFields: '[href="/web/settings/sections/assets/fields"]',
  authentication: '[href="/web/settings/sections/authentication"]',
  automation: '[href="/web/settings/sections/automation"]',
  purchaseOrders: '[href^="/web/settings/sections/purchase-orders"]',
  webhooks: '[href="/web/settings/sections/webhooks"]',
  workOrders: '[href="/web/settings/sections/work-orders/general"]',
  tags: '[href="/web/settings/sections/tags/general"]',
});

export default sidebar;
