import { configureSelectorProxy } from '../../../../helpers';

const tagsSettings = configureSelectorProxy({
  addButton: '[data-cy="add-tag-button"]',
  tagNameInput: '[data-cy="tagName"]',
  modelsInput: '#ModelsListButton',
  submitTagButton: '[data-cy="submit-tag-button"]',
  tagRow: (name) => `tbody tr:contains("${name}")`,
  actionsMenu: '[data-cy="icon-button"]',
  editButton: 'button:contains("Edit")',
  selectModel: (model) => `li[data-cy="${model}"]`,
  deleteButton: 'button:contains("Delete")',
  deleteConfirmButton: '[data-cy="Delete"]',
});

export default tagsSettings;
