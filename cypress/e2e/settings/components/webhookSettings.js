import { configureSelectorProxy } from '../../../helpers';

export const webhookSettings = configureSelectorProxy({
  title: '[data-cy="page-header-title"]',
  webhookSettingsTab: '[data-cy="WebhooksTab"]',
  settingsTitle: '[data-cy="webhook-settings-title"]',
  addWebhookButton: '[data-cy="add-webhook-button"]',
});

export const webhookDialog = configureSelectorProxy({
  activityModal: '[data-cy="dialog"]',
  activityModalTitle: '[data-cy="modal-title"]',
  activityModalClose: '[data-for="modal-close"]',
  webhookNameInput: '[data-cy="name"]',
  webhookEndpointInput: '[data-cy="endpoint"]',
  allEventsRadio: '[data-cy="all-events"]',
  someEventsRadio: '[data-cy="some-events"]',
  eventListItem: '[data-cy="event-list-item"]',
  cancelButton: '[data-cy="cancel"]',
  submitButton: '[data-cy="submit"]',
});

export const webhookDeleteDialog = configureSelectorProxy({
  activityModal: '[data-cy="dialog"]',
  activityModalTitle: '[data-cy="modal-title"]',
  activityModalClose: '[data-for="modal-close"]',
  cancelButton: '[data-cy="cancel-button"]',
  confirmButton: '[data-cy="confirmBtn"]',
});

export const webhookList = configureSelectorProxy({
  webhookListWrapper: '[data-cy="webhook-list-wrapper"]',
  webhookItem: '[data-cy="webhook-item"]',
  webhookItemStatus: '[data-cy="webhook-item-status"]',
  webhookItemName: '[data-cy="webhook-item-name"]',
  webhookItemEndpoint: '[data-cy="webhook-item-endpoint"]',
  deleteWebhookButton: '[data-cy="delete-webhook-button"]',
});

export const webhookDetail = configureSelectorProxy({
  editWebhookButton: '[data-cy="edit-webhook-button"]',
  backButton: '[data-cy="back-button"]',
  webhookStatusButton: '[data-cy="webhook-status-button"]',
  webhookName: '[data-cy="webhook-name"]',
  webhookEndpoint: '[data-cy="webhook-endpoint"]',
});
