import { configureSelectorProxy } from '../../../helpers';

const apiSettings = configureSelectorProxy({
  changeButton: 'button:contains("Change")',
  versioningLink: '[href="https://developers.onupkeep.com/#versioning"]',
  devDocsLink: '[href="https://developers.onupkeep.com"]',
  apiVersionModalHeader: '.modal:contains("Change Default API Version")',
  versionListButton: '[id="VersionListButton"]',
  versionOption: (version) => `[data-cy="${version}"]`,
  changeApiVersionButton: '.modal  [data-cy="Change"]',
  apiVersion: (version) => `span:contains("${version}")`,
});

export default apiSettings;
