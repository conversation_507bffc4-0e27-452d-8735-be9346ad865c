import filterTests from '../../support/filterTests';
import { customWOStatusTests } from './tests';
import { upkeepPages } from '../../support/constants';
import * as settingsHelpers from './helpers/settingsHelpers';
import { customWOStatusSettings } from './components/withSettingsRevampOn';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Settings', () => {
    Cypress.on('uncaught:exception', () => false);
    beforeEach(() => {
      const now = Date.now();
      const testId = `cwosAdmin${now}`;
      cy.createOrLoginAdmin(testId, ['ADMIN'], 'BUSINESS_PLUS', 'CWOS Team');
      upkeepPages.SETTINGS.go();
      settingsHelpers.visitWorkOrderSettings();
      customWOStatusSettings.menuSelector.click();
    });

    customWOStatusTests.cwosTableShouldBeVisible();
    customWOStatusTests.canCreateCustomWOStatus();
    customWOStatusTests.canModifyCustomWOStatus();
    customWOStatusTests.canDeleteCustomWOStatus();
  });
});
