import { configureSelectorProxy } from '../../../helpers';

const general = configureSelectorProxy({
  askForFeedbackToggle:
    '[data-cy="togrow-askForFeedbackOnWOClose"] [name="askForFeedbackOnWOClose"]',
  autoassignWoToggle:
    '[data-cy="togrow-isAutoAssignTasks"] [name="isAutoAssignTasks"]',
  autoassignRequestToggle:
    '[data-cy="togrow-isAutoAssignTasks"] [name="isAutoAssignTasks"]',
  englishOption: '#react-select-2-option-0',
  settingsHeader: '[data-cy="page-header-title"]',
  languageSelector: (language) => `[id="${language}ListButton"]`,
  languageOption: (language) => `[data-cy="${language}"]`,
  dateFormatMmDdYy: '[data-cy="MM/DD/YY"] .item-container',
  dateFormatDdMmYy: '[data-cy="DD/MM/YY"] .item-container',
  dateFormatSelector: '[id="Date FormatListButton" ]',
  dateFormatOptions: (value) => `.react-select__option:contains("${value}")`,
  currencySelector: '[id="CurrencyListButton"]',
  currencyOptions: (value) => `.item-container:contains("${value}")`,
  businessTypeSelector: '[data-cy="gen-cfg-btype-selector"]',
  businessTypeOptions: (value) => `.react-select__option:contains("${value}")`,
  timeZoneSelector: '[id="Time ZoneListButton"]',
  timeZoneOptions: (value) => `.item-container:contains("${value}")`,
  workOrderSettings: '[data-cy="work-order-settings"]',
  workOrderSequenceField: '[data-cy="work-order-sequence"]',
  purchaseOrderSequenceField: '[data-cy="purchaseOrderStartCount"]',
  purchaseOrderPrefixField: '[data-cy="purchaseOrderPrefix"]',
  settingsHeaderText: 'Settings',
  v1SettingsHeader: 'UpKeep Settings',
  successBanner: 'preferences updated',
  laborCostToggle:
    '.react-toggle:has([label="Include labor costs in the total cost"])',
});

export default general;
