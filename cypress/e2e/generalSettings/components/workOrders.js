import { configureSelectorProxy } from '../../../helpers';

const workOrders = configureSelectorProxy({
  workOrdersTab: '[href="/web/settings/sections/work-orders/general"]',
  woCountInput: '[id="companyWorkOrderCount"]',
  generalTab: '[data-cy="General"]',
  configurationTab: '[data-cy="Configuration"]',
  statusesTab: '[data-cy="Statuses"]',
  createWoFieldSelect: '.card:first .menu-list-button',
  completeWoFieldSelect: 'hr~div:last() div:has(> label)',
  laborCostToggle: '[name="includeLaborCostInTotalCost"]',
});

export default workOrders;
