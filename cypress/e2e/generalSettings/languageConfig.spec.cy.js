import filterTests from '../../support/filterTests';
import { TEST_DATA } from './helpers/constants';
import * as genSettingsTests from './tests';

filterTests(['all', 'ui'], () => {
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const testId = 'language-settings';
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
  };

  describe('Language configuration', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        [],
        'BUSINESS_PLUS',
        'My Team',
        emails,
        (adminSession) => {
          cy.switchUser(
            testId,
            'TECH',
            { email: emails.TECH },
            adminSession.localStorage.authToken,
          );
        },
      );
    });

    TEST_DATA.forEach((testData) => {
      genSettingsTests.canChangeLanguageSettings(testId, testData);
    });
  });
});
