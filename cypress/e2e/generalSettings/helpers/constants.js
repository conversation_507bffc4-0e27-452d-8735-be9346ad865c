const LANGUAGES_IN_ENGLISH = {
  ENGLISH: 'English',
  SPANISH: 'Spanish',
  ARABIC: 'Arabic',
  GERMAN: 'German',
  // FRENCH: 'French',
};

const OTHER_LANGUAGES_TEXT = {
  SPANISH: 'Español',
  ARABIC: 'العربية',
  GERMAN: 'Deutsche',
  // FRENCH: 'Français',
};

const OTHER_LANGUAGES_HEADER = {
  SPANISH: 'Configuraciones',
  ARABIC: 'الإعدادات',
  GERMAN: 'die Einstellungen',
  // FRENCH: 'Paramètres',
};

const HEADER_IN_OTHER_LANGUAGES = {
  SPANISH: 'Idioma',
  ARABIC: 'اللغة',
  GERMAN: 'Language',
  // FRENCH: 'Langue',
};

const ENGLISH_IN_OTHER_LANGUAGES = {
  SPANISH: 'Inglés',
  ARABIC: 'الإنجليزية',
  GERMAN: 'Deutsche',
  // FRENCH: 'Anglais',
};

const TEST_DATA = [
  [
    LANGUAGES_IN_ENGLISH.ENGLISH,
    LANGUAGES_IN_ENGLISH.SPANISH,
    OTHER_LANGUAGES_TEXT.SPANISH,
    OTHER_LANGUAGES_HEADER.SPANISH,
    HEADER_IN_OTHER_LANGUAGES.SPANISH,
    ENGLISH_IN_OTHER_LANGUAGES.SPANISH,
  ],
  [
    LANGUAGES_IN_ENGLISH.ENGLISH,
    LANGUAGES_IN_ENGLISH.ARABIC,
    OTHER_LANGUAGES_TEXT.ARABIC,
    OTHER_LANGUAGES_HEADER.ARABIC,
    HEADER_IN_OTHER_LANGUAGES.ARABIC,
    ENGLISH_IN_OTHER_LANGUAGES.ARABIC,
  ],
  // [
  //   LANGUAGES_IN_ENGLISH.ENGLISH,
  //   LANGUAGES_IN_ENGLISH.GERMAN,
  //   OTHER_LANGUAGES_TEXT.GERMAN,
  //   OTHER_LANGUAGES_HEADER.GERMAN,
  //   HEADER_IN_OTHER_LANGUAGES.GERMAN,
  //   ENGLISH_IN_OTHER_LANGUAGES.GERMAN,
  // ],
  // [
  //   LANGUAGES_IN_ENGLISH.ENGLISH,
  //   LANGUAGES_IN_ENGLISH.FRENCH,
  //   OTHER_LANGUAGES_TEXT.FRENCH,
  //   OTHER_LANGUAGES_HEADER.FRENCH,
  //   HEADER_IN_OTHER_LANGUAGES.FRENCH,
  //   ENGLISH_IN_OTHER_LANGUAGES.FRENCH,
  // ],
];

const WORK_ORDER_CONFIG = {
  AUTO_ASSIGN_WORK_ORDERS: 0,
  AUTO_ASSIGN_REQUESTS: 1,
  DISABLE_CLOSED_WORK_ORDER_NOTIFICATIONS: 2,
  ASK_FOR_FEEDBACK_WHEN_WORK_ORDER_IS_CLOSED: 3,
  INCLUDE_LABOR_COSTS_IN_THE_TOTAL_COST: 4,
  ENABLE_WORK_ORDER_UPDATES_FOR_REQUESTERS: 5,
};

module.exports = {
  TEST_DATA,
  WORK_ORDER_CONFIG,
};
