// Libraries
import * as generalSettingsPages from '../components';

const validateToggleStatus = (toggleIndex) => {
  generalSettingsPages.general.workOrderSettings
    .get()
    .find('div[class*="react-toggle "]')
    .eq(toggleIndex)
    .invoke('attr', 'class')
    .then((classText1) => {
      const oldValue = classText1.includes('react-toggle--checked');
      generalSettingsPages.general.workOrderSettings
        .get()
        .find('div[class*="react-toggle "]')
        .eq(toggleIndex)
        .click();

      // Then The state will be reflected instantaneously
      generalSettingsPages.general.successBanner.containsTextShouldBeVisible();
      generalSettingsPages.general.workOrderSettings
        .get()
        .find('div[class*="react-toggle "]')
        .eq(toggleIndex)
        .invoke('attr', 'class')
        .then((classText2) => {
          const newValue = classText2.includes('react-toggle--checked');
          expect(newValue).to.equal(!oldValue);
        });
    });
};

export { validateToggleStatus };
