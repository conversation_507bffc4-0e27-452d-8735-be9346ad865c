import * as generalSettingsPages from '../components';
import * as workOrderBetaPages from '../../workOrders2.0/components';

export const englishToOtherLanguage = (language) => {
  generalSettingsPages.general.languageSelector('Language').click();
  generalSettingsPages.general.languageOption(language).click();
};

export const verifyLanguageSet = (header, language) => {
  generalSettingsPages.general.languageSelector(header).shouldContain(language);
};

export const resetToEnglish = (header, englishOption) => {
  generalSettingsPages.general.languageSelector(header).click();
  generalSettingsPages.general.languageOption(englishOption).click();
};

export const visitWorkOrderSection = (tab) => {
  generalSettingsPages.workOrders.workOrdersTab.click();
  if (tab === 'general') {
    generalSettingsPages.workOrders.generalTab.click();
  } else if (tab === 'configuration') {
    generalSettingsPages.workOrders.configurationTab.click();
  } else if (tab === 'statuses') {
    generalSettingsPages.workOrders.statusesTab.click();
  }
  cy.url().should('include', `/web/settings/sections/work-orders/${tab}`);
};

export const setWoCount = (num) => {
  generalSettingsPages.workOrders.woCountInput.click().clear().type(num);

  cy.contains('Work order count updated').should('be.visible');
};

export const verifyWoCount = (woName, num) => {
  workOrderBetaPages.list.woNameInList(woName).shouldBeVisible();
  cy.contains('td', num).should('exist');
};

export const changeCreateWoFields = () => {
  generalSettingsPages.workOrders.createWoFieldSelect.get().each(($ele) => {
    if ($ele.text() !== 'Hidden') {
      cy.get($ele).scrollIntoView().click();
      cy.contains('li:visible', 'Hidden').click();
      cy.contains('Work Order Creation Configuration settings updated').should(
        'be.visible',
      );
    }
  });
  cy.wait(600);
};

export const changeCompleteWoFields = () => {
  generalSettingsPages.workOrders.completeWoFieldSelect.get().each(($ele) => {
    if (
      $ele.text().includes('Time') ||
      $ele.text().includes('Parts') ||
      $ele.text().includes('Cost')
    ) {
      cy.get($ele).scrollIntoView().click();
      cy.get('li:visible:first()').scrollIntoView();
      cy.contains('li:visible', 'Hidden').scrollIntoView().click();
      cy.get($ele).contains('Hidden');
      cy.contains(
        'Work Order Completion Configuration settings updated',
      ).should('be.visible');
    }
  });
};

export const toggleLaborCostsOn = () => {
  // Check to see if toggle is on / off
  // If toggle is off, then turn toggle on
  cy.contains('Include labor costs in the total WO cost').should('be.visible');
  generalSettingsPages.workOrders.laborCostToggle.get().then(($ele) => {
    if ($ele.hasClass('checked')) {
      generalSettingsPages.workOrders.laborCostToggle.click();
    }
  });

  generalSettingsPages.workOrders.laborCostToggle.should(
    'have.class',
    'checked',
  );
};
