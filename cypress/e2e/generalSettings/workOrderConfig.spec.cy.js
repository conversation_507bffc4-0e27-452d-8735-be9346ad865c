import filterTests from '../../support/filterTests';
import * as generalSettingsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  const testId = 'work-order-settings';

  describe('Validate Persistence of Work Order Settings on General Settings page', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
    });

    generalSettingsTests.canValidateWorkOrderConfigurationOptions();
    generalSettingsTests.canValidateRequestsConfigurationOptions();
    generalSettingsTests.canUpdateWorkOrderSettings();
  });
});
