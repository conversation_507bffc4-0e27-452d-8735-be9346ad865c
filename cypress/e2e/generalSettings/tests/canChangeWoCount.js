import { upkeepPages } from '../../../support/constants';
import * as h from '../../../helpers';
import * as generalSettingsHelpers from '../helpers/generalSettingsHelpers';

const canChangeWoCount = () => {
  it('Should pick the correct value of Work Order Sequence during creation of a Work Order', () => {
    Cypress.on('uncaught:exception', () => false);
    const mainDescription = 'Change WO Count';

    cy.wait(1000); // session was breaking at this point
    upkeepPages.SETTINGS.go();

    // Set starting work order count
    generalSettingsHelpers.visitWorkOrderSection('general');
    generalSettingsHelpers.setWoCount(100);

    // Create a basic work order via API and go to Work Orders page
    h.createBasicWorkOrder({ mainDescription });
    cy.wait(1000);
    cy.visit('/');
    upkeepPages.WORK_ORDERS.go();

    // Verify starting count follows newly set work order count
    generalSettingsHelpers.verifyWoCount(mainDescription, 1001);
  });
};

export default canChangeWoCount;
