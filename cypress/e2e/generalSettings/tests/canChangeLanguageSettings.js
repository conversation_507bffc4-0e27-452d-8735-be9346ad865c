import { upkeepPages } from '../../../support/constants';
import * as generalSettingsHelpers from '../helpers/generalSettingsHelpers';

const canChangeLanguageSettings = (testId, testData) => {
  describe(`Changing language from ${testData[0]} to ${testData[1]} in General Settings`, () => {
    it(`should expect the selected language to be ${testData[2]}`, () => {
      Cypress.on('uncaught:exception', () => false);
      cy.createOrLoginAdmin(testId);

      cy.log(testData);
      cy.intercept('/web/settings/sections').as('generalSettings');
      upkeepPages.SETTINGS.go();

      // Set language from English to other language
      generalSettingsHelpers.englishToOtherLanguage(testData[1]);
      cy.wait('@generalSettings');
      generalSettingsHelpers.verifyLanguageSet(testData[4], testData[2]);

      // Reset language back to English
      generalSettingsHelpers.resetToEnglish(testData[4], testData[5]);
      cy.wait('@generalSettings');
      generalSettingsHelpers.verifyLanguageSet('Language', 'English');
    });
  });
};

export default canChangeLanguageSettings;
