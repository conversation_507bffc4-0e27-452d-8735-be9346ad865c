import { upkeepPages } from '../../../support/constants';
import * as generalSettingsHelpers from '../helpers/generalSettingsHelpers';
import * as workOrderHelpers from '../../workOrders2.0/helpers/woHelpers';

const canHideWoFields = (testId) => {
  it('Can hide work order fields', () => {
    Cypress.on('uncaught:exception', () => false);
    const woName = 'hide wo fields';

    upkeepPages.SETTINGS.go();

    // set all fields in "Creating a Work Order" section to Hidden
    generalSettingsHelpers.visitWorkOrderSection('configuration');
    generalSettingsHelpers.changeCreateWoFields();

    // go to work orders page and verify only required field is title
    cy.visit('/');
    upkeepPages.WORK_ORDERS.go();

    workOrderHelpers.openCreateWorkOrder(woName);
    cy.contains('Submit').click();

    // go back to settings page and change fields in "Completing a Work Order" section to Hidden
    upkeepPages.SETTINGS.go();

    generalSettingsHelpers.visitWorkOrderSection('configuration');
    generalSettingsHelpers.changeCompleteWoFields();

    // Go back to work orders page and create a new work order to complete
    cy.visit('/');
    upkeepPages.WORK_ORDERS.go();

    // Complete work order as admin
    workOrderHelpers.openCreateWorkOrder(woName);
    workOrderHelpers.setWorkOrderToState('Complete');
    workOrderHelpers.validateWorkOrderStatus('Complete', 'grey');

    // Sign out and sign in as lim admin
    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(testId, 'LIMITED_ADMIN', {}, sessionToken);
    });

    // Complete work order as lim admin
    workOrderHelpers.openCreateWorkOrder(`${woName} lim_admin`);
    workOrderHelpers.setWorkOrderToState('Complete');
    workOrderHelpers.validateWorkOrderStatus('Complete', 'grey');
  });
};

export default canHideWoFields;
