import * as generalSettingsPages from '../components';
import { upkeepPages } from '../../../support/constants';

const canValidateRequestsConfigurationOptions = () => {
  it(
    'can navigate to work request checklist page',
    { testCaseId: 'QA-T117' },
    () => {
      // And I visit the Settings page
      upkeepPages.SETTINGS.go();
      generalSettingsPages.requests.requestsTab.click();

      cy.contains('Request fields').should('be.visible');
      cy.contains('Title').should('exist');
      cy.contains('Description').should('exist');
      cy.contains('Priority').should('exist');
      cy.contains('Images').should('exist');
      cy.contains('Due Date').should('exist');
      cy.contains('Category').should('exist');
      cy.contains('Assigned Location').should('exist');
      cy.contains('Assigned Asset').should('exist');
      cy.contains('Primary Worker').should('exist');
      cy.contains('Assigned Team').should('exist');
    },
  );
};

export default canValidateRequestsConfigurationOptions;
