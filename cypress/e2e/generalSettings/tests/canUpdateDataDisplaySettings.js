import { upkeepPages } from '../../../support/constants';
import * as generalSettingsPages from '../components';

const canUpdateDataDisplaySettings = () => {
  it('should expect the data display settings be updated instantaneously', () => {
    const dateFormatValue = 'DD/MM/YY';
    const currencyValue = 'INR - Indian Rupee - ₹';
    const timeZoneValue = 'GMT +00:00 GMT';

    // And I visit the General Settings tab
    upkeepPages.SETTINGS.go();
    generalSettingsPages.general.settingsHeader.shouldBeVisible();

    // When I select a Date Format
    generalSettingsPages.general.dateFormatSelector.click();
    generalSettingsPages.general.dateFormatDdMmYy.click({ force: true });

    // Then The specified Date Format will be applied to the web app instantaneously
    cy.reload();
    generalSettingsPages.general.dateFormatSelector.shouldContain(
      dateFormatValue,
    );

    // When I select a Currency
    generalSettingsPages.general.currencySelector.click();
    generalSettingsPages.general
      .currencyOptions(currencyValue)
      .click({ force: true });

    // Then The specified Currency will be applied to the web app instantaneously
    cy.reload();
    generalSettingsPages.general.currencySelector.shouldContain(currencyValue);

    // When I select a Timezone
    generalSettingsPages.general.timeZoneSelector.click();
    generalSettingsPages.general
      .timeZoneOptions(timeZoneValue)
      .click({ force: true });

    // Then The specified Time Zone will be applied to the web app instantaneously
    // generalSettingsPages.general.successBanner.shouldBeVisible();
    cy.reload();
    generalSettingsPages.general.timeZoneSelector.shouldContain(timeZoneValue);
  });
};

export default canUpdateDataDisplaySettings;
