import * as generalSettingsPages from '../components';
import { sidebar } from '../../settings/components/withSettingsRevampOn';
import { upkeepPages } from '../../../support/constants';

const canUpdateWorkOrderSettings = () => {
  it('should expect the work order settings be updated instantaneously', () => {
    // And I visit the Settings page
    upkeepPages.SETTINGS.go();

    // And I visit the General Settings tab
    sidebar.workOrders.click();

    // When I toggle Auto-assign Work Orders
    generalSettingsPages.general.autoassignWoToggle.click();
    generalSettingsPages.general.successBanner.containsTextShouldBeVisible();

    // When I toggle Auto-assign Requests
    generalSettingsPages.general.autoassignRequestToggle.click();
    generalSettingsPages.general.successBanner.containsTextShouldBeVisible();

    // When I toggle Ask for feedback when Work Order is Closed
    generalSettingsPages.general.askForFeedbackToggle.click();
    generalSettingsPages.general.successBanner.containsTextShouldBeVisible();
  });
};

export default canUpdateWorkOrderSettings;
