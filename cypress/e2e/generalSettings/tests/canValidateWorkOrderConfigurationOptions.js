import * as generalSettingsPages from '../components';
import { upkeepPages } from '../../../support/constants';

const canValidateWorkOrderConfigurationOptions = () => {
  it(
    'can navigate to Completing a Work order tab from WO Configuration tab', // {
    { testCaseId: 'QA-T116' },
    () => {
      // And I visit the Settings page
      upkeepPages.SETTINGS.go();
      generalSettingsPages.workOrders.workOrdersTab.click();
      cy.contains('When a Work Order is Created').should('be.visible');
      cy.contains('When a Work Order is Completed').should('be.visible');

      generalSettingsPages.workOrders.configurationTab.click();

      cy.contains('Completing a Work Order').should('be.visible');
      cy.contains('Files').should('exist');
      cy.contains('Tasks').should('exist');
      cy.contains('Time').should('exist');
      cy.contains('Parts').should('exist');
      cy.contains('Cost').should('exist');
    },
  );
};

export default canValidateWorkOrderConfigurationOptions;
