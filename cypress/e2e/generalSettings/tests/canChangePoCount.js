import { upkeepPages } from '../../../support/constants';
import * as generalSettingsPages from '../components';
import * as purchaseOrderPages from '../../purchaseOrders/components';
import * as h from '../../../helpers';
import { sidebar } from '../../settings/components/withSettingsRevampOn';

const canChangePoCount = () => {
  it('Should pick the correct value of Purchase Order Sequence during creation of a Purchase Order', () => {
    const now = Date.now();
    const stringPartName = `Part A ${now}`;
    const partNumber = `a${now}`;
    const title = `Part A PO ${now}`;
    const purchaseOrderNumber = '1001';

    // And I visit the Settings page
    upkeepPages.SETTINGS.go();
    sidebar.purchaseOrders.click();

    // And I specify an amount for the Purchase Order Start Count
    generalSettingsPages.general.purchaseOrderSequenceField.click();
    generalSettingsPages.general.purchaseOrderSequenceField.type(100);
    cy.reload();
    cy.go('back');
    // I create a purchase order
    h.createPartInventory(
      {
        numberPartQuantity: 10,
        partNumber,
        stringPartName,
      },
      true,
    ).then((part) => {
      h.createCustomPurchaseOrder(
        { title, purchaseOrderNumber },
        {
          stringPartName,
          id: part.body.result.objectId,
          quantity: 20,
        },
      );
    });

    // Then I go to Purchase Orders page
    upkeepPages.PURCHASE_ORDERS_WEB.go();
    purchaseOrderPages.list.purchaseOrderRow(title).shouldContain('1001');
  });

  it('Should pick the correct value of Purchase Order prefix during creation of a Purchase Order', () => {
    const now = Date.now();
    const stringPartName = `Part A ${now}`;
    const partNumber = `a${now}`;
    const title = `Part A PO ${now}`;

    // And I visit the Settings page
    upkeepPages.SETTINGS.go();
    sidebar.purchaseOrders.click();

    // And I specify an amount for the Purchase Order Start Count
    generalSettingsPages.general.purchaseOrderPrefixField.click();
    generalSettingsPages.general.purchaseOrderPrefixField.type('AB');
    cy.reload();
    cy.go('back');

    // I create a purchase order
    h.createPartInventory(
      {
        numberPartQuantity: 10,
        partNumber,
        stringPartName,
      },
      true,
    ).then((part) => {
      h.createCustomPurchaseOrder(
        { title },
        {
          stringPartName,
          id: part.body.result.objectId,
          quantity: 20,
        },
      );
    });

    // Then I go to Purchase Orders page
    upkeepPages.PURCHASE_ORDERS_WEB.go();
    purchaseOrderPages.list.purchaseOrderRow(title).shouldContain('AB');
  });
};

export default canChangePoCount;
