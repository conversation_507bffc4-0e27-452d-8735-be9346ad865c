import filterTests from '../../support/filterTests';
import * as genSettingsTests from './tests';

filterTests(['all', 'ui'], () => {
  const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
  const now = Date.now();
  const testId = 'woconfig';
  const emails = {
    ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
  };

  describe('Validate Persistence of Work Order Settings on General Settings page', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(now, [], 'BUSINESS_PLUS', '', emails);
    });

    genSettingsTests.canChangeWoCount();
    genSettingsTests.canHideWoFields(testId);
  });
});
