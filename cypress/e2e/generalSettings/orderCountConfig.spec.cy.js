/* eslint-disable func-names */
import filterTests from '../../support/filterTests';
import * as generalSettingsTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  const testId = 'sequence-settings';

  describe('Validation of PO Sequence and Prfix Input Fields in General Settings page', () => {
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, '', 'BUSINESS_PLUS', 'testTeam');
    });

    generalSettingsTests.canChangeWoCount();
    generalSettingsTests.canChangePoCount();
    generalSettingsTests.canUpdateDataDisplaySettings();
  });
});
