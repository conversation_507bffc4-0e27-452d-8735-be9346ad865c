import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const team = 'My Cool Team';
  const testId = 'assetAdmin';

  describe('Assets Create, update, delete, search', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', team);
    });

    adminTests.shouldUpdateAsset();
    adminTests.shouldDeleteAsset();
    adminTests.canSearchAssets();
    adminTests.shouldCreateWorkOrderFromAsset();
  });
});
