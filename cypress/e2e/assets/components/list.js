import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  assetNavButton: '[data-cy="main-nav-Assets"]',
  assetPageHeader: '[data-cy="Assets-header-title"]',
  button: (text) => `button:contains(${text})`,
  createButton: '[data-cy="CreateAssetButton"]',
  searchBar: 'input[data-cy="search-bar"]',
  importAssetsButton: 'button:contains("Import Assets")',
  dropdownButton: 'button[data-cy="icon-button"]',
  firstAssetInList: '[data-cy="cell-row-0"]',
  valueInTable: (val) => `[data-cy="${val}"]`,
  rowInTable: (name) => `tbody td .${name}`,
  idValueInTable: 'span[data-cy="id-value"]',
  locationFilterCheckbox: (location) =>
    `.picker tr:contains(${location}) [data-cy^="cellSelector"]`,
  saveLocationButton: '[data-cy="Save"]:visible',
  filterChip: 'span.MuiChip-label',
  filterChipContent: 'span.MuiChip-label span:nth-child(2)',
  resetFilter: '[data-cy="components.templates.FilterBar.reset"]',
  toggleExpandRow: (parentAsset) =>
    `tbody tr:contains(${parentAsset}) .cell-expander`,
  assetRow: (assetName) => `tr:contains("${assetName}")`,
  assetRowCheckBox: (assetName) =>
    `tr:contains(${assetName}) [type="checkbox"]`,
  actionButtonDeleteAssets: 'span.action:contains("Delete")',
  deleteButtonInModal: 'button[data-cy="Delete"]',
  exportFilteredList: 'li:contains("Export Filtered View")',
  assetsHeader: 'h2.header-title',
  allFiltersButton: '[data-cy="FilterBar-filters-button"]',
  addFilterButton: 'button[data-cy="add-filter"]',
  multiSelect: (field) => `[data-cy="${field}"]`,
  statusFilterDropdown: 'button#StatusListButton',
  statusOption: (name) => `span:contains("${name}")`,
  modalCardHeader: '.modal .card-header',
  applyFilterButton: 'button[data-cy="Apply"]',
});

export default list;
