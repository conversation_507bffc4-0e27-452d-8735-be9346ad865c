import { configureSelectorProxy } from '../../../helpers';

const details = configureSelectorProxy({
  title: '[data-cy="Assets-header-title"]',
  workOrdersTab: '[data-cy="generic.workOrders"]',
  detailsTab: '[data-cy="generic.labels.details"]',
  partsTab: '[data-cy="generic.upkeepEntity.parts"]',
  filesTab: '[data-cy="generic.upkeepEntity.files"]',
  metersTab: '[data-cy="generic.upkeepEntity.meters"]',
  editButton: '[data-cy="EditButton"]',
  createWorkOrderButton: 'button[data-cy="CreateWorkOrderButton"]',
  menuOptionsDropdown: 'div[data-test="menu-options"] button',
  deleteAsset: 'li:contains("Delete")',
  deleteAssetConfirm: 'span:contains("Delete")',
  nameContent: '[data-cy="NameContent"]',
  descriptionContent: '[data-cy="DescriptionContent"]',
  areaContent: '[data-cy="AreaContent"]',
  barcodeContent: '[data-cy="BarcodeContent"]',
  customersContent: '[data-cy="CustomersContent"]',
  vendorsContent: '[data-cy="VendorsContent"]',
  modelContent: '[data-cy="ModelContent"]',
  workerContent: '[data-cy="primary-worker"]',
  teamContent: '[data-cy="team"]',
  checkOutButton: '[data-cy="CheckOutButton"]',
  dialogCheckOutButton: '[role="dialog"] [data-cy="CheckOutButton"]',
  checkInButton: '[data-cy="CheckInButton"]',
  dialogCheckInButton: '[role="dialog"] [data-cy="CheckInButton"]',
  checkOutInCommentInput: '[data-cy="CommentInput"]',
  activityButton: '[data-cy="[objectObject]Button"]',
  activityModal: '[role="dialog"]',
  activityModalCloseButton: '[role="dialog"] button[variant="outlined"]',
  downtimeStatusMenu: '[data-cy="downtimeStatusMenu"]',
  downtimeStatusOperational: '[data-cy="downtimeStatus-Operational"]',
  downtimeStatusNonOperational: '[data-cy="downtimeStatus-Not Operational"]',
  viewDowntimeLogButton: '[data-cy="ViewDowntimeLogButton"]',
  addDowntimeButton: '[data-cy="AddDowntimeButton"]',
  addDowntimeButtonModal: '[role="dialog"] [data-cy="AddDowntimeButton"]',
  downtimeHours: '[data-cy="downtime-hours"] input',
  downtimeMinutes: '[data-cy="downtime-minutes"] input',
  downtimeStatus: '[data-cy="Status"] input',
  downtimeDateInput: '[data-cy="downtimeDate"] input',
  downtimeTimeInput: '[data-cy="downtimeTime"] input',
  downtimeDescription: '[data-cy="DescriptionInput"]',
  downtimeLogRow: (name) => `tr:contains("${name}")`,
  closeAddDowntimeModalButton:
    'h1:contains("Downtime Log") + [data-cy="icon-button"]',
  customDataHeader: 'h2:contains("Custom Data")',
  customFieldInput: (fieldName) => `[data-cy="${fieldName}Input"]`,
  customDropdown: (dropdown) => `[data-cy="${dropdown}"] input`,
  customData: (fieldName) => `[data-cy="${fieldName}Content"]`,
});

export default details;
