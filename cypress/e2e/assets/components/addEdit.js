import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  nameInput: '[data-cy="NameInput"] input',
  description: 'textarea',
  model: '[data-cy="ModelInput"] input',
  category: 'label:contains("Category") +div input',
  area: '[data-cy="AreaInput"] input',
  barcode: '[data-cy="BarcodeInput"] input',
  additionalInformation: '[data-cy="Additional InformationInput"] textarea',
  image: 'input[data-cy="drag-drop-upload"]:first',
  file: 'input[data-cy="drag-drop-upload"]:last',
  parentAssetSearchBar: '[placeholder="Search by Name or Barcode"]',
  setParentAssetButton: '[data-cy="SetParentAssetButton"]',
  confirmParentAssetButton: '[role="dialog"] [data-cy="SetParentAssetButton"]',
  setLocationButton: '[data-cy="SetLocationButton"]',
  setLocationSearchBar: '[placeholder="Search"]',
  setLocationFirstItemInList: 'ul:nth-child(1) p:nth-child(1)',
  confirmSetLocationButton: '[role="dialog"] [data-cy="SetLocationButton"]',
  workerDropdown: '[id="WorkerListButton"]',
  teamDropdown: '[id="TeamsListButton"]',
  customerInput: '[id="CustomersListButton"]',
  vendorInput: '[id="VendorsListButton"]',
  saveChangesButton: '[data-cy="SaveChangesButton"]',
  createAssetButton: '[data-cy="CreateAssetButton"]',
  addPartsButton: '[data-cy="AddPartsButton"]',
  selectPartButtonModal:
    '[data-cy="part-select-modal"] [data-cy="AddPartsButton"]',
  customCurrencyInput: (now) => `[data-cy="Custom Currency ${now}Input"]`,
  customDropdown: (now) => `[data-cy="Custom Dropdown ${now}"]`,
  customMultiLineText: (now) =>
    `[data-cy="Custom Multi-Line Text ${now}Input"]`,
  customNumber: (now) => `[data-cy="Custom Number ${now}Input"]`,
  customSingleLineText: (now) =>
    `[data-cy="Custom Single Line Text ${now}Input"]`,
  backButton: '[data-cy="backButton"]',
  dropdownSearchBar: '[data-cy="menu-list-search"]',
});

export default addEdit;
