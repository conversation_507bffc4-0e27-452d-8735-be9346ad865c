import filterTests from '../../support/filterTests';
import * as assetTests from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const testId = 'assetAdminTier2';

  describe('Asset Admin - Tier 2', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
    });

    assetTests.adminTests.canCreateAssetWithCustomFields();
    assetTests.adminTests.canVisitAssetsPage();
    assetTests.adminTests.canCreateAssetWithCustomFields();
  });
});
