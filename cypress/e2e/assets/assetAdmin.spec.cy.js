import faker from 'faker';
import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const now = Date.now();
  const team = 'My Cool Team';
  const testId = 'assetAdmin';
  const worker = 'Tester McTestFace';

  describe('Assets checkout, filter, log', () => {
    Cypress.on('uncaught:exception', () => false);
    const partName = faker.commerce.productName();

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', team);
    });

    const testArgs = {
      worker,
      team,
      partName,
      now,
    };

    adminTests.shouldCheckOutAndInAsset(testArgs.worker);
    adminTests.shouldFilterAndSearch();
    adminTests.canLogAssetDowntime();
    adminTests.shouldBulkDeleteAssetsFromList(testArgs);
    adminTests.shouldCreateAsset(testArgs);
  });
});
