/* eslint-disable cypress/no-force */
import { DateTime } from 'luxon';
import { getIframeBody } from '../../../helpers/iframeHelpers';
import * as assetPages from '../components';
import * as settingsPages from '../../settings/components/withSettingsRevampOn';

export const selectVendor = (name) => {
  assetPages.addEdit.vendorInput.scrollIntoView();
  assetPages.addEdit.vendorInput.click();
  assetPages.addEdit.dropdownSearchBar.type(name);
  cy.contains(name).click();
  cy.contains('More Information').click({
    scrollBehavior: 'bottom',
    force: true,
  });
};

export const setLocation = (name) => {
  assetPages.addEdit.setLocationButton.click();
  assetPages.addEdit.setLocationSearchBar.type(name, { scrollTo: false });
  cy.contains(name).click();
  assetPages.addEdit.confirmSetLocationButton.click();
  assetPages.addEdit.confirmSetLocationButton.shouldNotExist();
};

export const setParentAsset = (name) => {
  assetPages.addEdit.setParentAssetButton.click();
  assetPages.addEdit.parentAssetSearchBar.type(name, { scrollTo: false });
  cy.contains(name).click();
  assetPages.addEdit.confirmParentAssetButton.click();
  assetPages.addEdit.confirmParentAssetButton.shouldNotExist();
};
export const selectWorker = (name) => {
  assetPages.addEdit.workerDropdown.scrollIntoView();
  assetPages.addEdit.workerDropdown.click();
  assetPages.addEdit.dropdownSearchBar.type(name);
  cy.contains(name).click();
};
export const selectTeam = (name) => {
  assetPages.addEdit.teamDropdown.scrollIntoView();
  assetPages.addEdit.teamDropdown.click();
  assetPages.addEdit.dropdownSearchBar.type(name);
  cy.contains(name).click();
  cy.contains('Assigned To').click({
    scrollBehavior: 'bottom',
    force: true,
  });
};

export const setNameAndDescription = (name, description) => {
  assetPages.addEdit.nameInput.type(name);
  assetPages.addEdit.description.type(description);
};

export const setBarcode = (barcode) => {
  assetPages.addEdit.barcode.type(barcode);
};

export const filterLocation = (location) => {
  assetPages.list.locationFilterCheckbox(location).click();
  assetPages.list.saveLocationButton.click();
};

export const selectPart = (name) => {
  assetPages.addEdit.addPartsButton.click({ scrollBehavior: 'bottom' });
  cy.get(`td:contains(${name})`).first().click({ force: true });
  assetPages.addEdit.selectPartButtonModal.click();
};

export const selectCustomer = (name) => {
  assetPages.addEdit.customerInput.click({ scrollBehavior: 'bottom' });
  cy.get('[data-cy="menu-list-search"]').type(name);
  cy.contains(name).click({ scrollBehavior: 'bottom' });
  cy.contains('More Information').click({
    scrollBehavior: 'bottom',
    force: true,
  });
};

/**
 * Populate Create asset details
 * Name, description, model, category, area and barcode
 * @param {object} details
 * @param {boolean} fullPopulate - If {true} also populates vendor/location/parent asset/worker and team
 */
export const populateCreateAsset = (details, fullPopulate) => {
  const {
    assetDetails: { name, description, model, category, area, barcode },
    parentAsset,
    location,
    vendor,
    worker,
    // team,
  } = details;

  assetPages.list.createButton.click();

  assetPages.addEdit.nameInput.type(name);
  assetPages.addEdit.description.type(description);
  assetPages.addEdit.model.type(model);
  assetPages.addEdit.category.type(category);
  assetPages.addEdit.area.type(area);
  assetPages.addEdit.barcode.type(barcode);

  if (fullPopulate) {
    selectVendor(vendor);
    setLocation(location);
    setParentAsset(parentAsset);
    selectWorker(worker);
    // selectTeam(team);
    // ! This has been commented out because dropdown component doesn't close and cause issues with subsequent tests
  }
};

export const createCsvData = (testData = {}) => {
  let csv = '';
  csv =
    `Name,Description,Barcode,Location Name,Worker Email,Category,Customer Assigned Names,Vendor Assigned Names\n` +
    `${testData.assetName},${testData.description},${testData.barcode},${testData.location},${testData.worker},${testData.category},${testData.customer},${testData.vendor}`;
  return csv;
};

export const goToImportExportPage = () => {
  cy.wait(1250);
  assetPages.list.dropdownButton.click();
  cy.contains('p', 'Import').click();
  cy.url().should('include', '/web/imports/asset');
};

export const importAssets = (file) => {
  assetPages.dromoImport.startAssetImport.click();
  getIframeBody('https://widget.dromo.io/')
    .find('[data-cy="file-input"]')
    .selectFile(`cypress/fixtures/${file}`, {
      action: 'drag-drop',
      force: true,
    });
};

export const verifyImportCreateSuccess = () => {
  cy.contains('Import Complete. Created 1 and updated 0 Assets').should(
    'be.visible',
  );
};

export const searchForAsset = (asset) => {
  assetPages.list.searchBar.click().type(asset);
};

export const verifyAssetsInList = (testAssets) => {
  Object.values(testAssets).forEach((values) => {
    cy.contains(values).should('exist');
  });
};

export const exportAssetList = () => {
  // cy.wait(1250);
  assetPages.list.dropdownButton.click();
  assetPages.list.exportFilteredList.click();

  // * This happens too fast to see
  // cy.contains('Downloading assets. This may take a while.').should(
  //   'be.visible',
  // );
};

export const verifyAssetsInCsv = (content, testAssets) => {
  Object.values(testAssets).forEach((values) => {
    expect(content).to.contain(values);
  });
};

export const interceptFileExport = () => {
  cy.intercept({
    method: 'POST',
    url: `${Cypress.env('CYPRESS_API_URL')}/api/v1/imports/asset/export`,
  }).as('interceptFileExport');
};

export const exportAssetsFromImportPage = () => {
  assetPages.dromoImport.exportCurrentAssets.click();
};

export const downloadAssetsTemplate = () => {
  assetPages.dromoImport.downloadTemplate.click();
};

/**
 * Populate Create Custom Asset Fields
 * Custom single line text, multi-line text, dropdown, date, number, and currency
 * @param {object} customFieldNames
 */

export const createCustomAssetFields = ({
  customSingleLineText,
  customMultiLineText,
  customDropdown,
  customDate,
  customNumber,
  customCurrency,
}) => {
  settingsPages.sidebar.assetsFields.click();

  // create custom single line text
  cy.wait(1250);
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.singleLineText.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customSingleLineText);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');

  // create custom multi line text
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.multiLineText.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customMultiLineText);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');

  // create custom dropdown
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.dropdown.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customDropdown);
  settingsPages.fieldsSettings.dropdownOptionField.click().type(`Option 1`);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');

  // create custom date
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.date.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customDate);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');

  // create custom number
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.number.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customNumber);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');

  // create custom currency
  settingsPages.fieldsSettings.createFieldButton.click();
  settingsPages.fieldsSettings.currency.click();
  settingsPages.fieldsSettings.fieldInput.click().type(customCurrency);
  settingsPages.fieldsSettings.createFieldDialogButton.click();
  cy.contains('Field created').should('be.visible');
};

export const verifyCustomFields = (customAssetFields) => {
  Object.values(customAssetFields).forEach((values) => {
    cy.get(`tr:contains(${values})`).scrollIntoView();
    cy.contains(values).should('exist');
  });
};

/**
 * Populate Custom Asset Fields
 * @param {object} customAssetFields custom field names
 * @param {object} customFieldDetails custom field details
 */
export const populateCustomFields = (customAssetFields, customFieldDetails) => {
  assetPages.details.customDataHeader.scrollIntoView();

  // Input custom single line text
  assetPages.details
    .customFieldInput(customAssetFields.customSingleLineText)
    .type(customFieldDetails.singleLineText);

  // Input custom multi line text
  assetPages.details
    .customFieldInput(customAssetFields.customMultiLineText)
    .type(customFieldDetails.multiLineText);

  // Input custom number
  assetPages.details
    .customFieldInput(customAssetFields.customNumber)
    .type(customFieldDetails.numberText);

  // Select custom dropdown
  assetPages.details
    .customDropdown(customAssetFields.customDropdown)
    .scrollIntoView()
    .click();
  cy.get('li:contains("Option 1")').click();

  // Select custom date
  cy.contains('div', customAssetFields.customDate).scrollIntoView().click();
  cy.get('[class="rw-calendar-footer"]').click();

  // Input custom currency
  assetPages.details
    .customFieldInput(customAssetFields.customCurrency)
    .type(customFieldDetails.currencyText);
};

export const filterByStatus = (name) => {
  assetPages.list.allFiltersButton.click();
  assetPages.list.addFilterButton.click();
  assetPages.list.multiSelect('Status').click();
  assetPages.list.statusFilterDropdown.click();
  assetPages.list.statusOption(name).click();
  assetPages.list.modalCardHeader.click({ force: true }); // to collapse status dropdown
  cy.wait(500);
  assetPages.list.applyFilterButton.click();
};

export const submitAssetForm = (assetName) => {
  assetPages.addEdit.createAssetButton.click({ scrollBehavior: 'top' });
  cy.contains('Asset created').should('be.visible');
  cy.wait(1250);
  assetPages.list.assetRow(assetName).shouldExist();
};

export const selectAssetInList = (assetName) => {
  cy.contains('td', assetName).click();
};

export const verifyAssetCustomFields = (
  customAssetFields,
  customFieldDetails,
) => {
  const today = DateTime.now().toLocaleString(DateTime.DATE_FULL);

  assetPages.details.detailsTab.click();

  // Single line text
  assetPages.details
    .customData(customAssetFields.customSingleLineText)
    .shouldContain(customFieldDetails.singleLineText);

  // Multi line text
  assetPages.details
    .customData(customAssetFields.customMultiLineText)
    .shouldContain(customFieldDetails.multiLineText);

  // Dropdown data
  assetPages.details
    .customData(customAssetFields.customDropdown)
    .shouldContain('Option 1');

  // Date data
  assetPages.details
    .customData(customAssetFields.customDate)
    .shouldContain(`${today}`);

  // Number data
  assetPages.details
    .customData(customAssetFields.customNumber)
    .shouldContain(customFieldDetails.numberText);

  // Currency data
  assetPages.details
    .customData(customAssetFields.customCurrency)
    .shouldContain(customFieldDetails.currencyText);
};
