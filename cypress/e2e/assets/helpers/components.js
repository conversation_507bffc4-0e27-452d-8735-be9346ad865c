import { configureSelectorProxy } from '../../../helpers';

export const list = configureSelectorProxy({
  betaSwitch: '[data-cy="betaSwitch"]',
  searchBar: '[data-cy="searchInput"] input',
  createButton: '[data-cy="CreateAssetButton"]',
  firstAssetInList: '[data-cy="cell-row-0"]',
  idValueInTable: 'span[data-cy="id-value"]',
  toggleExpandRow: (parentAsset) =>
    `[role="row"]:contains(${parentAsset}) [title="Toggle Row Expanded"]`,
  assetRow: (assetName) => `[role="row"]:contains(${assetName})`,
});

export const details = configureSelectorProxy({
  title: '[data-cy="Assets-header-title"]',
  workOrdersTab: '[data-cy="generic.workOrders"]',
  detailsTab: '[data-cy="generic.labels.details"]',
  partsTab: '[data-cy="generic.upkeepEntity.parts"]',
  filesTab: '[data-cy="generic.upkeepEntity.files"]',
  metersTab: '[data-cy="generic.upkeepEntity.meters"]',
  editButton: '[data-cy="EditButton"]',
  createWorkOrderButton: 'button[data-cy="CreateWorkOrderButton"]:first',
  menuOptionsDropdown: '[data-test="menu-options"] button',
  deleteAsset: 'li:contains("Delete")',
  deleteAssetConfirm: 'span:contains("Delete")',
  nameContent: '[data-cy="NameContent"]',
  descriptionContent: '[data-cy="DescriptionContent"]',
  areaContent: '[data-cy="AreaContent"]',
  barcodeContent: '[data-cy="BarcodeContent"]',
  vendorsContent: '[data-cy="VendorsContent"]',
  modelContent: '[data-cy="ModelContent"]',
  workerContent: '[data-cy="primary-worker"]',
  teamContent: '[data-cy="team"]',
});

export const form = configureSelectorProxy({
  nameInput: '[data-cy="NameInput"] input',
  description: 'textarea',
  model: '[data-cy="ModelInput"] input',
  category: 'label:contains("Category") +div input',
  area: '[data-cy="AreaInput"] input',
  barcode: '[data-cy="BarcodeInput"] input',
  additionalInformation: '[data-cy="Additional InformationInput"] textarea',
  image: '[data-cy="drag-drop-upload"]',
  setParentAssetButton: '[data-cy="SetParentAssetButton"]',
  confirmParentAssetButton: '[role="dialog"] [data-cy="SetParentAssetButton"]',
  setLocationButton: '[data-cy="SetLocationButton"]',
  confirmSetLocationButton: '[role="dialog"] [data-cy="SetLocationButton"]',
  workerDropdown: 'label:contains("Worker") +div [title="Open"]',
  teamDropdown: 'label:contains("Teams") +div [title="Open"]',
  vendorInput: 'label:contains("Vendors") +div [title="Open"]',
  saveChangesButton: '[data-cy="SaveChangesButton"]',
  createAssetButton: '[data-cy="CreateAssetButton"]',
});
