import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const shouldDeleteAsset = () => {
  it('should delete an asset', { testCaseId: 'QA-T6305' }, () => {
    const now = Date.now();
    const parentAsset = `Parent Asset Name ${now}`;
    const location = `Location A ${now}`;
    cy.get('#upkeep-logo').should('be.visible');

    h.createLocation({ stringName: location }).then((loc) => {
      h.createAsset(
        {
          objectLocation: loc.body.result.id,
          Name: parentAsset,
          availabilityTrackingOn: true,
        },
        true,
      );
    });
    upkeepPages.ASSETS.go();
    cy.contains(parentAsset).should('exist');

    assetPages.list.assetRow(parentAsset).click();
    cy.contains('No Work Orders').should('be.visible');
    assetPages.details.menuOptionsDropdown.click();
    assetPages.details.deleteAsset.click();
    assetPages.details.deleteAssetConfirm.click();

    cy.contains(parentAsset).should('not.exist');
  });
};

export default shouldDeleteAsset;
