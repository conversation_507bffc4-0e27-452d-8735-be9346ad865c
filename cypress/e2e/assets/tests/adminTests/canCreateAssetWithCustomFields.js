import { upkeepPages } from '../../../../support/constants';
import * as assetHelpers from '../../helpers/assetsHelpers';
import * as settingsHelpers from '../../../settings/helpers/settingsHelpers';

const canCreateAssetWithCustomFields = () => {
  it(
    '<PERSON><PERSON> can create an asset with all custom fields',
    { testCaseId: 'QA-T5491' },
    () => {
      const now = Date.now();

      const customAssetFields = {
        customSingleLineText: `Custom Single Line Text ${now}`,
        customMultiLineText: `Custom Multi-Line Text ${now}`,
        customDropdown: `Custom Dropdown ${now}`,
        customDate: `Custom Date ${now}`,
        customNumber: `Custom Number ${now}`,
        customCurrency: `Custom Currency ${now}`,
      };

      const customFieldDetails = {
        singleLineText: 'Single Line Text Input',
        multiLineText: 'Multi-Line Input',
        numberText: '678',
        currencyText: '111',
      };

      const assetDetails = {
        name: `Asset w/ Custom Fields ${now}`,
        description: `Description ${now}`,
        model: 'Custom Fields Model',
        category: 'Custom Fields Category',
        area: 'Custom Fields Area',
        barcode: now,
      };

      // Create custom asset fields
      upkeepPages.SETTINGS.go();
      assetHelpers.createCustomAssetFields(customAssetFields);
      assetHelpers.verifyCustomFields(customAssetFields);
      settingsHelpers.closeSettings();

      // Create new asset with custom fields
      upkeepPages.ASSETS.go();
      cy.reload();
      assetHelpers.populateCreateAsset({ assetDetails }, false);
      assetHelpers.populateCustomFields(customAssetFields, customFieldDetails);
      assetHelpers.submitAssetForm(assetDetails.name);

      // Verify asset was created with custom fields
      assetHelpers.selectAssetInList(assetDetails.name);
      assetHelpers.verifyAssetCustomFields(
        customAssetFields,
        customFieldDetails,
      );
    },
  );
};

export default canCreateAssetWithCustomFields;
