import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as assetHelpers from '../../helpers/assetsHelpers';
import * as assetPages from '../../components';

const canExportFilteredViewByOperationalStatus = () => {
  it(
    'user can export assets filtered view by operational status',
    { testCaseId: 'QA-T6370' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now() + 1;
      const assetB = `Asset B ${now}`;
      const assetC = `Asset C ${now}`;
      const assetD = `Asset D`;

      const testAssets = {
        assetB,
        assetC,
        assetD,
      };

      for (let i = 0; i < Object.keys(testAssets).length; i++) {
        h.createAsset({ Name: Object.values(testAssets)[i] }, true);
      }

      upkeepPages.ASSETS.go();
      assetPages.list.assetRow(assetB).shouldExist();

      // filter by status operational
      // ! this doesn't filter assets without status set in v2 page but export still works as expected
      assetHelpers.filterByStatus('Operational');

      // export assets list
      assetHelpers.exportAssetList();
      cy.wait(1500);

      cy.readFile('cypress/downloads/upkeep-assets.csv').then((content) => {
        assetHelpers.verifyAssetsInCsv(content, { assetB, assetC, assetD });
      });
    },
  );
};

export default canExportFilteredViewByOperationalStatus;
