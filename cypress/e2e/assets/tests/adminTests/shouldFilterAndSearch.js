import * as helpers from '../../../../helpers';
import * as assetPages from '../../components';
import * as assetHelpers from '../../helpers/assetsHelpers';
import { upkeepPages } from '../../../../support/constants';

const shouldFilterAndSearch = () => {
  it('should filter by location', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const parentAsset = `Parent Asset Name`;
    const location = `Location A ${now}`;

    helpers.createLocation({ stringName: location }).then((loc) => {
      helpers.createAsset(
        {
          objectLocation: loc.body.result.id,
          Name: parentAsset,
          availabilityTrackingOn: true,
        },
        true,
      );
    });
    const locationB = `Location B ${now}`;
    const assetBdetails = {
      name: `Test asset B ${now}`,
      description: `Test description B ${now}`,
      barcode: now,
    };

    // Create 1 extra location (Location B)
    helpers.createLocation({ stringName: locationB });

    upkeepPages.ASSETS.go();
    // I have at least 2 different assets assigned to 2 different locations
    // Create second asset with second location (Location B)
    assetPages.list.createButton.click();
    assetHelpers.setNameAndDescription(
      assetBdetails.name,
      assetBdetails.description,
    );
    assetHelpers.setBarcode(assetBdetails.barcode);
    assetHelpers.setLocation(locationB);
    assetPages.addEdit.createAssetButton.scrollIntoView().click();

    // Filter by location A
    assetPages.list.button('Location').click();
    assetHelpers.filterLocation(location);

    // The list refreshes + I am on asset list
    // I only see assets assigned to the locations
    assetPages.list.valueInTable(parentAsset).shouldContain(parentAsset);
    assetPages.list.rowInTable('location').shouldContain(location);

    // I clear filter
    assetPages.list.resetFilter.click();

    // I filter by Location B
    assetPages.list.button('Location').click();
    assetHelpers.filterLocation(locationB);

    // The list refreshes + I am on the asset list
    // I only see assets assigned to the locations
    assetPages.list
      .valueInTable(assetBdetails.name)
      .shouldContain(assetBdetails.name);
    assetPages.list.rowInTable('location').shouldContain(locationB);
  });
};

export default shouldFilterAndSearch;
