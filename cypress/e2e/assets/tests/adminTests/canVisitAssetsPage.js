import { upkeepPages } from '../../../../support/constants';
import * as assetHelpers from '../../helpers/assetsHelpers';

const canVisitAssetsPage = () => {
  it(
    'User can navigate to Assets page from any other page in the navbar menu',
    { testCaseId: 'QA-T5661' },
    () => {
      const now = Date.now();
      const assetDetails = {
        name: `Test Asset ${now}`,
        description: `Description ${now}`,
        model: 'Test Model',
        category: 'Testing',
        area: 'Test Area',
        barcode: now,
      };

      // Create new asset with custom fields
      upkeepPages.ASSETS.go();
      assetHelpers.populateCreateAsset({ assetDetails }, false);
      assetHelpers.submitAssetForm(assetDetails.name);

      // Visit different pages and navigate back to Assets page
      upkeepPages.WORK_ORDERS.go();
      cy.url().should('not.include', '/assets/list');

      upkeepPages.ASSETS.go();
      cy.reload();
      cy.url().should('include', '/assets/list');
      cy.contains('td', assetDetails.name).should('be.visible');
    },
  );
};

export default canVisitAssetsPage;
