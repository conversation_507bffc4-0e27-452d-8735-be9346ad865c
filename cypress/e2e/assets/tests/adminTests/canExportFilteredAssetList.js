import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as assetHelpers from '../../helpers/assetsHelpers';

const canExportFilteredAssetList = () => {
  it(
    'User is able to export filtered assets from Asset List page',
    { testCaseId: 'QA-T4957' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now() + 1;
      const assetB = `Asset B ${now}`;
      const assetC = `Asset C ${now}`;
      const assetD = `Asset D ${now}`;
      const assetE = `Asset E`;

      const testAssets = {
        assetB,
        assetC,
        assetD,
        assetE,
      };

      for (let i = 0; i < Object.keys(testAssets).length; i++) {
        h.createAsset({ Name: Object.values(testAssets)[i] }, true);
      }

      upkeepPages.ASSETS.go();

      // filter assets via search
      assetHelpers.searchForAsset(now);
      cy.wait(1500);
      assetHelpers.verifyAssetsInList([assetB, assetC, assetD]);

      // export assets list
      assetHelpers.exportAssetList();
      cy.wait(1500);

      cy.readFile('cypress/downloads/upkeep-assets.csv').then((content) => {
        assetHelpers.verifyAssetsInCsv(content, { assetB, assetC, assetD });
        expect(content).to.not.contain(assetE);
      });
    },
  );
};

export default canExportFilteredAssetList;
