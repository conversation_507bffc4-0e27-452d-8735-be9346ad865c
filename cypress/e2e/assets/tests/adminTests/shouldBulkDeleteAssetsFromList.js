import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const shouldBulkDeleteAssetsFromList = ({ now }) => {
  it(
    'should bulk delete assets from the list',
    { testCaseId: 'QA-T6319' },
    () => {
      const assetsToDelete = 4;
      const assetName = `delete Asset ${now}`;
      for (let i = 0; i < assetsToDelete; i++) {
        h.createAsset(
          {
            Name: `${assetName} ${i}`,
          },
          true,
        );
      }

      upkeepPages.ASSETS.go();
      assetPages.list
        .assetRowCheckBox(assetName)
        .shouldHaveLength(assetsToDelete)
        .click({ multiple: true });

      cy.contains(`${assetsToDelete} selected`).should('exist');
      assetPages.list.actionButtonDeleteAssets.click();
      assetPages.list.deleteButtonInModal.click();

      assetPages.list.assetRow(assetName).shouldNotExist();
    },
  );
};

export default shouldBulkDeleteAssetsFromList;
