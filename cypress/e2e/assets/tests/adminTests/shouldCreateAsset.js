import * as assetHelpers from '../../helpers/assetsHelpers';
import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const shouldCreateAsset = ({ worker, team, partName }) => {
  it(
    'should create an asset',
    // setting behind ff due https://upkeep.atlassian.net/browse/UPD-2078
    {
      featureFlags: { assetWorkOrderTableV2: true },
      testCaseId: 'QA-T6267',
    },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const customer = `Customer ${now}`;
      const parentAsset = `Parent Asset Name ${now}`;
      const location = `Location A ${now}`;
      const vendor = `Business Vendor ${now}`;
      const filename = 'test-file.txt';
      const image = 'cat.jpeg';
      const imageExtension = '.jpeg';
      const meterName = `Meter A ${now}`;

      const assetDetails = {
        name: `Test asset ${now}`,
        description: `Test description ${now}`,
        model: 'Test model',
        category: 'Test category',
        area: 'Test area',
        barcode: now,
      };
      const config = {
        assetDetails,
        location,
        parentAsset,
        partName,
        team,
        vendor,
        worker,
      };

      h.createLocation({ stringName: location }).then((loc) => {
        h.createAsset(
          {
            objectLocation: loc.body.result.id,
            Name: parentAsset,
            availabilityTrackingOn: true,
          },
          true,
        ).then((asset) => {
          h.createMeter({
            name: meterName,
            assetAssigned: asset.body.results.id,
          });
        });
        h.createPart(
          {
            partName,
            partLines: [{ objectLocation: loc.body.result.id }],
          },
          true,
        );
      });

      h.createVendor(vendor);
      h.createCustomer(customer);
      upkeepPages.ASSETS.go();

      assetHelpers.populateCreateAsset(config, true);
      assetHelpers.selectCustomer(customer);

      // upload image/file
      assetPages.addEdit.image.selectFile(`cypress/fixtures/${image}`, {
        action: 'drag-drop',
        force: true,
      });
      assetPages.addEdit.file.selectFile(`cypress/fixtures/${filename}`, {
        action: 'drag-drop',
        force: true,
      });

      assetHelpers.selectPart(partName);

      assetPages.addEdit.createAssetButton.scrollIntoView().click();
      assetPages.list.toggleExpandRow(parentAsset).click();

      assetPages.list.assetRow(assetDetails.name).click();
      assetPages.details.detailsTab.click();
      assetPages.details.nameContent.shouldContain(assetDetails.name);
      assetPages.details.descriptionContent.shouldContain(
        assetDetails.description,
      );
      cy.get(`img[src*="${imageExtension}"]`).should('exist');
      assetPages.details.areaContent.shouldContain(assetDetails.area);
      assetPages.details.barcodeContent.shouldContain(assetDetails.barcode);
      assetPages.details.customersContent.shouldContain(customer);
      assetPages.details.modelContent.shouldContain(assetDetails.model);
      assetPages.details.vendorsContent.shouldContain(vendor);
      assetPages.details.workerContent.shouldContain(worker);
      // This is an invalid check as the team is not being added to the asset
      // The team dropdown is not behaving well in the cypress test, but works fine in production
      // assetPages.details.teamContent.shouldContain(team);
      cy.contains(parentAsset).should('exist');
      cy.contains(location).should('exist');

      // verify user can visit files tab
      assetPages.details.filesTab.click();
      cy.contains('td', filename).should('exist');

      // verify user can visit meters tab
      assetPages.addEdit.backButton.click();
      // eslint-disable-next-line cypress/no-force
      cy.contains(parentAsset).click({ force: true });
      assetPages.details.metersTab.click();
      cy.contains('p', meterName).should('exist');

      // go back to overview tab
      assetPages.details.workOrdersTab.click();
      cy.contains('No Work Orders').should('exist');
    },
  );
};

export default shouldCreateAsset;
