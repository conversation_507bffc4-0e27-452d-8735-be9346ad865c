import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as assetHelpers from '../../helpers/assetsHelpers';

const canExportFromListPage = () => {
  it(
    'User is able to export assets from Asset List page',
    { testCaseId: 'QA-T4981' },
    () => {
      const now = Date.now();
      const assetName = `Asset H ${now}`;

      h.createAsset({ Name: assetName }, true);

      upkeepPages.ASSETS.go();

      assetHelpers.verifyAssetsInList({ assetName });
      assetHelpers.exportAssetList();
      cy.wait(1500);

      cy.readFile('cypress/downloads/upkeep-assets.csv').then((content) => {
        expect(content).to.contain(assetName);
      });
    },
  );
};

export default canExportFromListPage;
