// eslint-disable-next-line import/no-unresolved
import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../../support/constants';
import { startDromoImport } from '../../../../helpers/dromoImportHelpers';
import * as h from '../../../../helpers';
import * as assetHelpers from '../../helpers/assetsHelpers';

const canCreateAssetViaImport = (emails) => {
  it('should create asset via import', { testCaseId: 'QA-T5488' }, () => {
    Cypress.on('uncaught:exception', () => false);
    upkeepPages.ASSETS.go();
    const now = Date.now();
    const csvFile = 'assets-import.csv';
    const assetName = `Asset A ${now}`;
    const description = `${faker.lorem.sentences()}`;
    const barcode = `${now}`;
    const worker = emails.TECH;
    const category = 'Category A';
    const customer = `Customer A ${now}`;
    const location = `Location A ${now}`;
    const vendor = `Vendor A ${now}`;

    const testData = {
      assetName,
      description,
      barcode,
      worker,
      category,
      customer,
      location,
      vendor,
    };

    h.createLocation({ stringName: location });
    h.createCustomerWithData({ customerName: customer });
    h.createVendor(vendor);

    // Create csv
    const csvData = assetHelpers.createCsvData(testData);
    cy.writeFile(`cypress/fixtures/${csvFile}`, csvData);

    // Begin import
    assetHelpers.goToImportExportPage();
    assetHelpers.importAssets(csvFile);
    startDromoImport();
    assetHelpers.verifyImportCreateSuccess();
  });
};

export default canCreateAssetViaImport;
