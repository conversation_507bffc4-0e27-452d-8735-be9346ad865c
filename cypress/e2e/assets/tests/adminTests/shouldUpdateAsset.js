import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const shouldUpdateAsset = () => {
  it('should update an asset', { testCaseId: 'QA-T6269' }, () => {
    const now = Date.now();
    const parentAsset = `Parent Asset Name ${now}`;
    h.createAsset(
      {
        Name: parentAsset,
        availabilityTrackingOn: true,
      },
      true,
    );

    upkeepPages.ASSETS.go();

    const newModelValue = `new model ${now}`;
    assetPages.list.assetRow(parentAsset).click();
    assetPages.details.editButton.click();
    assetPages.addEdit.model.type(newModelValue);
    assetPages.addEdit.saveChangesButton.scrollIntoView().click();
    cy.url().should('contain', '/assets/view');
    assetPages.details.detailsTab.click();
    assetPages.details.modelContent.shouldContain(newModelValue);
  });
};

export default shouldUpdateAsset;
