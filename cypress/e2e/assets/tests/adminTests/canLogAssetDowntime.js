import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const canLogAssetDowntime = () => {
  it('Can log downtime on an asset', { testCaseId: 'QA-T6318' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const downtimeDuration = {
      hours: '1',
      minutes: '19',
    };

    const now = Date.now();
    const asset = `asset test ${now}`;
    const description = 'downtime description';
    h.createAsset({ Name: asset }, true);

    upkeepPages.ASSETS.go();
    // When I am on the Asset details page
    // And the Asset is in an "Operational" status
    assetPages.list.searchBar.type(asset);
    assetPages.list.assetRow(asset).click();
    cy.contains('No Work Orders').should('be.visible');

    assetPages.details.downtimeStatusMenu.click();
    // And I click the status dropdown
    // And I select "Not Operational"
    assetPages.details.downtimeStatusNonOperational.click({ force: true });

    // Then I am on the Asset details page
    // And the Asset's status has been changed to "Not Operational"
    assetPages.details.downtimeStatusMenu.shouldContain('Not Operational');

    // And a new downtime can be viewed in downtime log
    assetPages.details.viewDowntimeLogButton.click();

    // And the new downtime has a start time equal to the same time I changed to status to "Not Operational"
    const currentDate = new Date();
    const formattedDate = currentDate.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
    });
    assetPages.details.downtimeLogRow(formattedDate).shouldExist();

    // And the new downtime has a duration equal to the time between when I changed to status to "Not Operational" and the current time
    assetPages.details.downtimeLogRow('0').shouldExist();

    // And the new downtime shows a status of "Not Operational"
    assetPages.details.downtimeLogRow('Not Operational').shouldExist();

    // close downtime log
    assetPages.details.closeAddDowntimeModalButton.click();

    // Changing an asset’s status to Operational
    assetPages.details.downtimeStatusMenu.shouldContain('Not Operational');
    assetPages.details.downtimeStatusMenu.click();

    assetPages.details.downtimeStatusOperational.click({ force: true });
    assetPages.details.downtimeStatusMenu.shouldContain('Operational');
    cy.wait(1500); // wait for operational menu to be updated
    assetPages.details.viewDowntimeLogButton.click();
    assetPages.details.downtimeLogRow(formattedDate).shouldExist();
    assetPages.details.closeAddDowntimeModalButton.click();

    // Adding manual downtime
    // And I click Add Downtime
    cy.contains('No Work Orders').should('be.visible');
    cy.wait(1500); // page re-renders after No work orders gets displayed
    assetPages.details.addDowntimeButton.click();

    // And I enter a duration, status, and description
    assetPages.details.downtimeHours.click();
    assetPages.details.downtimeHours.type(downtimeDuration.hours);
    assetPages.details.downtimeMinutes.click();
    assetPages.details.downtimeMinutes.get().type(downtimeDuration.minutes);
    assetPages.details.downtimeTimeInput.type('12:00 AM');
    assetPages.details.downtimeStatus.click();
    cy.contains('li', 'Not Operational').click();
    assetPages.details.downtimeDescription.type(description);

    assetPages.details.addDowntimeButtonModal.click();
    assetPages.details.addDowntimeButtonModal.shouldNotExist();

    cy.wait(1500); // page re-renders after adding downtime
    assetPages.details.viewDowntimeLogButton.click();
    assetPages.details
      .downtimeLogRow(
        `${downtimeDuration.hours} hour, ${downtimeDuration.minutes} min`,
      )
      .shouldExist();
  });
};

export default canLogAssetDowntime;
