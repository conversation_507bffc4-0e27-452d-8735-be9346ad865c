import { upkeepPages } from '../../../../support/constants';
import * as woPages from '../../../workOrders2.0/components';
import * as assetPages from '../../components';
import * as h from '../../../../helpers';

const shouldCreateWorkOrderFromAsset = () => {
  it(
    'allows me to create a work order from asset details page',
    { testCaseId: 'QA-T6270' },
    () => {
      const now = Date.now();
      const parentAsset = `Parent Asset Name ${now}`;
      const location = `Location A ${now}`;

      h.createLocation({ stringName: location }).then((loc) => {
        h.createAsset(
          {
            objectLocation: loc.body.result.id,
            Name: parentAsset,
            availabilityTrackingOn: true,
          },
          true,
        );
      });

      upkeepPages.ASSETS.go();
      assetPages.list.assetRow(parentAsset).click();

      cy.url().then((url) => {
        const splitUrl = url.split('/');
        const assetId = splitUrl[splitUrl.length - 2]; // get second to last element

        assetPages.details.createWorkOrderButton.click();
        // Checks if asset is populated in the work order form
        woPages.form.woAssetSearchDropdown.get().then((input) => {
          if (input[0].value === '') {
            woPages.form.cancelButton.click();
            assetPages.details.createWorkOrderButton.click();
          }
        });
        woPages.form.woAssetSearchDropdown.shouldHaveValue(parentAsset);
        woPages.form.woTitleInput.type('Work Order Title');
        woPages.form.woDescriptionInput.type('Work Order Description');
        woPages.form.submitWorkOrderButton.click();
        cy.contains('Work order created');
        cy.url().should('include', `assets/view/${assetId}`);
        woPages.details.asset.shouldContain(parentAsset);
      });
    },
  );
};

export default shouldCreateWorkOrderFromAsset;
