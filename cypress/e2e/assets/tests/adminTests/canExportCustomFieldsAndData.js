import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as assetHelpers from '../../helpers/assetsHelpers';
import * as assetPages from '../../components';

const canExportCustomFieldsAndData = () => {
  it(
    'User is able to export filtered assets with custom field names and associated data',
    { testCaseId: 'QA-T4964' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const assetCustomFields = `AssetWithCustomFields${now}`;

      h.createAsset({ Name: assetCustomFields }, true);

      const customSingleLineText = `Custom Single Line Text ${now}`;
      const customMultiLineText = `Custom Multi-Line Text ${now}`;
      const customDropdown = `Custom Dropdown ${now}`;
      const customDate = `Custom Date ${now}`;
      const customNumber = `Custom Number ${now}`;
      const customCurrency = `Custom Currency ${now}`;

      const customAssetFields = {
        customSingleLineText,
        customMultiLineText,
        customDropdown,
        customDate,
        customNumber,
        customCurrency,
      };

      const customAssetData = {
        currency: '123',
        multiline: 'Lorem ipsum dolor sit amet',
        dropdown: 'Description',
        number: '987',
        singleLine: 'test',
      };

      upkeepPages.SETTINGS.go();
      assetHelpers.createCustomAssetFields(customAssetFields);
      assetHelpers.verifyCustomFields(customAssetFields);
      h.createAsset({ Name: assetCustomFields }, true);
      h.closeSettings();

      upkeepPages.ASSETS.go();
      assetPages.list.firstAssetInList.click();
      assetPages.details.editButton.click();

      assetPages.addEdit
        .customCurrencyInput(now)
        .type(customAssetData.currency);
      assetPages.addEdit.customDropdown(now).click();
      cy.contains('li', 'Option 1').click();
      assetPages.addEdit
        .customMultiLineText(now)
        .type(customAssetData.multiline);
      assetPages.addEdit.customNumber(now).type(customAssetData.number);
      assetPages.addEdit
        .customSingleLineText(now)
        .type(customAssetData.singleLine);
      assetPages.addEdit.saveChangesButton.click({ scrollBehavior: 'top' });

      // filter assets via search
      assetPages.details.detailsTab.click();
      cy.contains('[data-cy="NameContent"]', assetCustomFields).should(
        'be.visible',
      );
      upkeepPages.ASSETS.go();

      assetHelpers.searchForAsset(now);
      assetHelpers.verifyAssetsInList({ assetCustomFields });

      // export assets list
      assetHelpers.exportAssetList();
      cy.wait(1500);

      cy.readFile('cypress/downloads/upkeep-assets.csv').then((content) => {
        assetHelpers.verifyAssetsInCsv(content, { assetCustomFields });
        expect(content).to.contain(customSingleLineText);
        expect(content).to.contain(customMultiLineText);
        expect(content).to.contain(customDropdown);
        expect(content).to.contain(customNumber);
        expect(content).to.contain(customCurrency);
        expect(content).to.contain(customAssetData.singleLine);
        expect(content).to.contain(customAssetData.currency);
        expect(content).to.contain(customAssetData.multiline);
        expect(content).to.contain(customAssetData.dropdown);
        expect(content).to.contain(customAssetData.number);
      });
    },
  );
};

export default canExportCustomFieldsAndData;
