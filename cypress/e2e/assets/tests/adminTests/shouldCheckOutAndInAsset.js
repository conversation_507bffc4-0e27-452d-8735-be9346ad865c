import * as assetPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const shouldCheckOutAndInAsset = (worker) => {
  it(
    'should check out then check in an asset',
    { testCaseId: 'QA-T6322' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const checkOutMessage = `check out asset ${now}`;
      const assetCheckOutLog = 'checked the asset out';
      const checkInMessage = `check in asset ${now}`;
      const assetCheckInLog = 'checked the asset in';
      const parentAsset = `checkout Asset Name ${now}`;

      h.createAsset(
        {
          Name: parentAsset,
          availabilityTrackingOn: true,
        },
        true,
      );

      upkeepPages.ASSETS.go();

      assetPages.list.assetRow(parentAsset).click();

      // Check out asset
      cy.contains('No Work Orders').should('be.visible');
      assetPages.details.checkOutButton.click();
      assetPages.details.checkOutInCommentInput.shouldBeVisible();
      assetPages.details.checkOutInCommentInput.type(checkOutMessage);
      assetPages.details.dialogCheckOutButton.click();
      assetPages.details.checkInButton.should('exist');

      // verify check out log in activity logs
      assetPages.details.activityButton.click();
      assetPages.details.activityModal.shouldContain(checkOutMessage);
      assetPages.details.activityModal.shouldContain(worker);
      assetPages.details.activityModal.shouldContain(assetCheckOutLog);
      assetPages.details.activityModalCloseButton.click();

      // check in asset
      assetPages.details.checkInButton.click();
      assetPages.details.checkOutInCommentInput.type(checkInMessage);
      assetPages.details.dialogCheckInButton.click();
      assetPages.details.checkOutButton.should('exist');

      // verify check in log in activity log
      assetPages.details.activityButton.click();
      assetPages.details.activityModal.shouldContain(checkInMessage);
      assetPages.details.activityModal.shouldContain(worker);
      assetPages.details.activityModal.shouldContain(assetCheckInLog);
      assetPages.details.activityModalCloseButton.click();
    },
  );
};

export default shouldCheckOutAndInAsset;
