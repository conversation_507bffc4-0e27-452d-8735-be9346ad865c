import { upkeepPages } from '../../../../support/constants';
import * as assetPages from '../../components';
import * as h from '../../../../helpers';

const canSearchAssets = () => {
  it(
    'Can search for Assets using the search bar',
    { testCaseId: 'QA-T6271' },
    () => {
      const now = Date.now();
      const parentAsset = `Parent Asset Name`;
      const location = `Location A ${now}`;
      h.createLocation({ stringName: location }).then((loc) => {
        h.createAsset(
          {
            objectLocation: loc.body.result.id,
            Name: parentAsset,
            availabilityTrackingOn: true,
          },
          true,
        );
      });
      upkeepPages.ASSETS.go();
      assetPages.list.searchBar.type(`${parentAsset} 2`);
      cy.contains('No Results');
      assetPages.list.searchBar.type(parentAsset);
      assetPages.list.firstAssetInList.shouldContain(parentAsset);
    },
  );
};

export default canSearchAssets;
