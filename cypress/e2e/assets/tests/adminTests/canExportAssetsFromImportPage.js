import { upkeepPages } from '../../../../support/constants';
import * as assetHelpers from '../../helpers/assetsHelpers';
import * as h from '../../../../helpers';

const canExportAssetsFromImportPage = () => {
  it(
    'User is able to export assets from Asset Import page',
    { testCaseId: 'QA-T4959' },
    () => {
      Cypress.on('uncaught:exception', () => false);

      const now = Date.now();
      const assetF = `Asset F ${now}`;
      const assetG = `Asset G ${now}`;

      h.createAsset({ Name: assetF }, true);
      h.createAsset({ Name: assetG }, true);

      upkeepPages.ASSETS.go();
      cy.reload();

      // verify assetF has been created
      assetHelpers.verifyAssetsInList({ assetF });
      assetHelpers.verifyAssetsInList({ assetG });

      // go to import export page
      assetHelpers.goToImportExportPage();
      assetHelpers.exportAssetsFromImportPage();

      cy.readFile('cypress/downloads/upkeep-assets.csv').then((content) => {
        assetHelpers.verifyAssetsInCsv(content, { assetF, assetG });
      });
    },
  );
};

export default canExportAssetsFromImportPage;
