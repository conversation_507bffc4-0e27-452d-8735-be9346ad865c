import { upkeepPages } from '../../../../support/constants';
import * as assetHelpers from '../../helpers/assetsHelpers';

const canDownloadAssetImportTemplate = () => {
  it(
    'User is able to download the Asset Import template',
    { testCaseId: 'QA-T4960' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      upkeepPages.ASSETS.go();
      cy.contains('h2', 'Assets').should('be.visible');

      assetHelpers.goToImportExportPage();
      cy.wait(1400);
      assetHelpers.downloadAssetsTemplate();
      cy.wait(1500);

      cy.readFile('cypress/downloads/asset-sample.csv').then((content) => {
        expect(content).to.contain(
          `Importing Notes (delete this column before importing),Asset ID`,
        );
      });
    },
  );
};

export default canDownloadAssetImportTemplate;
