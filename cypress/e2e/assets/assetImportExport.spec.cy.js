import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Asset Exports', () => {
    Cypress.on('uncaught:exception', () => false);
    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'assets-import';
    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
      TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(
        testId,
        ['ADMIN', 'TECH'],
        'BUSINESS_PLUS',
        'team assets',
        emails,
      );
      cy.contains('Work Orders').should('be.visible');
    });
    after(() => {
      cy.exec('rm -rf cypress/fixtures/assets-import.csv');
      cy.exec('rm -rf cypress/downloads/upkeep-assets.csv');
      cy.exec('rm -rf cypress/downloads/asset-sample.csv');
    });

    adminTests.canExportAssetsFromImportPage();
    adminTests.canCreateAssetViaImport(emails);
    adminTests.canExportFilteredAssetList();
    adminTests.canExportFromListPage();
    adminTests.canExportFilteredViewByOperationalStatus();
    adminTests.canDownloadAssetImportTemplate();
    adminTests.canExportCustomFieldsAndData();
  });
});
