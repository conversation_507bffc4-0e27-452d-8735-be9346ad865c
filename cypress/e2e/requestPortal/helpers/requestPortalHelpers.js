import * as requestPortalPages from '../components';

// Toggle Company Request Portal on
export const enableCompanyRequestPortal = () => {
  requestPortalPages.adminPage.companyRequestPortalToggle.shouldBeVisible();
  requestPortalPages.adminPage.companyRequestPortalToggle
    .invoke('attr', 'class')
    .then((toggle) => {
      if (!toggle.includes('checked')) {
        requestPortalPages.adminPage.companyRequestPortalToggle.click();
      }
    });
  cy.contains('Create Work Requests').should('be.visible');
};

// To enable Purchase Order requests, Work Order requests must be enabled as well
export const enablePurchaseOrderRequestPortal = () => {
  cy.visit('/web/settings/sections/purchase-orders/request-portal');

  requestPortalPages.adminPage.purchaseOrderRequestToggle.scrollIntoView();
  requestPortalPages.adminPage.purchaseOrderRequestToggle.shouldBeVisible();
  requestPortalPages.adminPage.purchaseOrderRequestToggle
    .invoke('attr', 'class')
    .then((toggle) => {
      if (!toggle.includes('checked')) {
        requestPortalPages.adminPage.purchaseOrderRequestToggle.click();
      }
    });
  cy.contains('Create via Request Portal').should('be.visible');
};

// Open Work Order Requests page in new tab and verify tab is open
export const goToWorkOrderRequestPage = () => {
  requestPortalPages.adminPage.woRequestLink.invoke('text').then((url) => {
    cy.visit(url, { failOnStatusCode: false });
  });

  cy.url().should('include', 'work-orders/create');
  requestPortalPages.woRequests.submitWoRequestButton.shouldExist();
};

// Open Purchase Order Requests page in new tab and verify tab is open
export const goToPurchaseOrderRequestPage = () => {
  requestPortalPages.adminPage.createPurchaseOrderLink
    .invoke('text')
    .then((url) => {
      cy.visit(url, { failOnStatusCode: false });
    });

  cy.url().should('include', 'purchase-orders/create');
  requestPortalPages.poRequests.submitPoRequestButton.shouldExist();
};

// Login to Requests Portal with "Login to view requests" link
export const goToRequestsPortalLogin = (email) => {
  requestPortalPages.adminPage.loginToRequestsLink
    .invoke('text')
    .then((url) => {
      cy.visit(url, { failOnStatusCode: false });
    });

  // Login to Request Portal
  requestPortalPages.woRequests.emailInput.type(email);
  requestPortalPages.woRequests.loginButton.click();

  // Verify user is logged in to Request Portal
  cy.contains(email).should('be.visible');
  cy.contains('My Work Order Requests').should('be.visible');
};

// Admin creates a work order via Work Order request portal
export const createWoRequest = (email, woTitle, description) => {
  requestPortalPages.woRequests.createWoRequestButton.click();

  // Verify user is logged in and taken to create Work Order Requests page
  cy.contains('Requester Info').should('be.visible');
  requestPortalPages.woRequests.emailInput.shouldHaveValue(email);

  // Fill out Request Details
  requestPortalPages.woRequests.titleInput.type(woTitle);
  requestPortalPages.woRequests.descriptionInput.type(description);

  // Click to submit Work Order request
  requestPortalPages.woRequests.submitWoRequestButton.click({
    scrollBehavior: 'bottom',
  });

  cy.contains('Your request has been submitted').should('be.visible');
};

// Verify submitted work order request exists in request portal
export const verifyWoRequestSubmitted = (woTitle, description) => {
  requestPortalPages.woRequests.woRequestsLink.scrollIntoView().click();
  cy.contains('My Work Order Requests').should('be.visible');
  cy.contains(woTitle).should('be.visible');
  cy.contains(description).should('be.visible');
};

export const goToCreatePoRequestPortal = () => {
  requestPortalPages.adminPage.createPurchaseOrderLink
    .invoke('text')
    .then((url) => {
      cy.visit(url, { failOnStatusCode: false });
    });

  cy.url().should('include', 'purchase-orders/create');
  requestPortalPages.poRequests.submitPoRequestButton.shouldExist();
};

export const loginToPoPortal = (email) => {
  requestPortalPages.adminPage.loginButton.click();
  requestPortalPages.adminPage.emailInput.type(email);
  requestPortalPages.adminPage.submitLogin.click();
};

export const clickCreateNewPoRequest = () => {
  cy.contains('Purchase Order Requests').click();
  requestPortalPages.poRequests.addNewRequestLink.click();
};

export const checkIfLoggedIn = (email) => {
  cy.get('body').then(($body) => {
    if ($body.find('button:contains("Log In")').is(':visible')) {
      loginToPoPortal(email);
      clickCreateNewPoRequest();
    }
  });
};

export const fillAndSubmitPoRequest = (title, part) => {
  requestPortalPages.poRequests.titleInput.type(title);
  requestPortalPages.poRequests.companyNameInput.type('Test LLC');

  // Add part to Purchase Order
  requestPortalPages.poRequests.addPartFromInventoryButton.click({
    scrollTo: 'bottom',
    force: true,
  });
  requestPortalPages.poRequests.partNameDropdown.click({
    scrollTo: 'bottom',
    force: true,
  });
  requestPortalPages.poRequests.partSelectOption(part).click({ force: true });

  // Submit Purchase Order
  requestPortalPages.poRequests.submitPoRequestButton
    .scrollIntoView()
    .click({ force: true });
};

// Verify submitted purchase order request exists in request portal
export const verifyPoRequestSubmitted = (poTitle) => {
  cy.reload();
  requestPortalPages.poRequests.poRequestsLink.click();
  cy.contains('My Purchase Order Requests').should('be.visible');
  cy.contains(poTitle).should('be.visible');
};

export const viewPoRequestInPortal = (poTitle) => {
  cy.contains(poTitle).click();
};

export const verifyPoDetails = (poTitle, part) => {
  cy.contains('h2', poTitle).should('be.visible');
  cy.contains('Details').click();
  cy.contains('span', part).should('be.visible');
};
