import filterTests from '../../support/filterTests';
import * as requestPortalTests from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe('Requests Portal - Work Order Requests', () => {
    Cypress.on('uncaught:exception', () => false);

    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'wo-reqportal';
    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '', emails);
    });

    requestPortalTests.canEnableWorkOrderRequestPortal();
    requestPortalTests.canNavigateToWoRequestLink();
    requestPortalTests.canLoginAndSubmitWoRequest(emails.ADMIN);
  });
});
