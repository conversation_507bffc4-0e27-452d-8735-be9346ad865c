import { configureSelectorProxy } from '../../../helpers';

const woRequests = configureSelectorProxy({
  submitWoRequestButton: 'button:contains("Submit Work Order Request")',
  emailInput: '[type="email"]',
  loginButton: 'button:contains("Log In")',
  createWoRequestButton: 'a[href*="/work-orders/create"]',
  titleInput: 'input[placeholder="Title to describe the issue"]',
  descriptionInput:
    'textarea[placeholder="Provide some details for this maintenance request"]',
  woRequestsLink: 'a:contains("Work Order Requests")',
});

export default woRequests;
