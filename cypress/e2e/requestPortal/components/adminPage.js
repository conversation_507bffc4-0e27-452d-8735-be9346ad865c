import { configureSelectorProxy } from '../../../helpers';

const adminPage = configureSelectorProxy({
  companyRequestPortalToggle: '.toggle-switch',
  purchaseOrderRequestToggle: '.toggle-switch',
  woRequestLink: 'a:contains("/work-orders/create")',
  loginToRequestsLink: 'a:contains("/login")',
  loginButton: 'button:contains("Log In")',
  emailInput: '[type="email"]',
  submitLogin: 'button:contains("Log In")',
  createPurchaseOrderLink: 'a:contains("/purchase-orders/create")',
});

export default adminPage;
