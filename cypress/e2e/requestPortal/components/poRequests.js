import { configureSelectorProxy } from '../../../helpers';

const poRequests = configureSelectorProxy({
  submitPoRequestButton: 'button:contains("Submit Purchase Order Request")',
  titleInput: 'input[placeholder*="Title"]',
  emailInput: 'input[type*="email"]',
  companyNameInput: 'input[placeholder="Company Name"]',
  addPartFromInventoryButton: 'button:contains("Part from Inventory")',
  partNameDropdown: 'div.container [class="react-select__input"]',
  partNameMenu: '#react-select__menu',
  partSelectOption: (part) => `[id*="react-select"]:contains("${part}")`,
  poRequestsLink: 'a:contains("Purchase Order Requests")',
  addNewRequestLink: '[href*="/purchase-orders/create"]',
});

export default poRequests;
