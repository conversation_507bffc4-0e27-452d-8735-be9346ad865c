import filterTests from '../../support/filterTests';
import * as requestPortalTests from './tests';

filterTests(['all', 'ui'], () => {
  describe('Request Portal - Purchase Order Requests', () => {
    Cypress.on('uncaught:exception', () => false);

    const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
    const now = Date.now();
    const testId = 'po-reqportal';
    const emails = {
      ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
    };

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '', emails);
    });

    requestPortalTests.canEnablePurchaseOrderRequests();
    requestPortalTests.canLoginAndCreatePurchaseOrder(emails.ADMIN);
    requestPortalTests.canViewPurchaseOrderDetails(emails.ADMIN);
  });
});
