import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';

const canLoginAndSubmitWoRequest = (email) => {
  it(
    '<PERSON><PERSON> is able to login to Request Portal',
    { testCaseId: 'QA-T99' },
    () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enableCompanyRequestPortal();
      requestPortalHelpers.goToRequestsPortalLogin(email);
    },
  );

  it(
    '<PERSON><PERSON> is able to submit a Work Order request from Request Portal',
    { testCaseId: 'QA-T89' },
    () => {
      const now = Date.now();
      const woTitle = `WO Request Portal ${now}`;
      const description = `Work Order submitted through Public Request Portal ${now}`;

      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enableCompanyRequestPortal();
      requestPortalHelpers.goToRequestsPortalLogin(email);
      requestPortalHelpers.createWoRequest(email, woTitle, description);
      requestPortalHelpers.verifyWoRequestSubmitted(woTitle, description);
    },
  );
};

export default canLoginAndSubmitWoRequest;
