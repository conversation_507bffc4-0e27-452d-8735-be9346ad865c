import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';
import * as h from '../../../helpers';

const canLoginAndCreatePurchaseOrder = (email) => {
  it(
    '<PERSON><PERSON> can create a Purchase Order from the Request Portal',
    { testCaseId: 'QA-T90' },
    () => {
      const now = Date.now();
      const title = `PO Request ${now}`;
      const part = `Part A ${now}`;

      h.createPart({ partName: part }, true);

      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enablePurchaseOrderRequestPortal();
      requestPortalHelpers.goToPurchaseOrderRequestPage();
      requestPortalHelpers.checkIfLoggedIn(email);
      requestPortalHelpers.fillAndSubmitPoRequest(title, part);
      requestPortalHelpers.verifyPoRequestSubmitted(title);
    },
  );
};

export default canLoginAndCreatePurchaseOrder;
