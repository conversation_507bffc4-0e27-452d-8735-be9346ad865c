import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';

const canEnablePurchaseOrderRequests = () => {
  it(
    'Admin can enable Purchase Order Request Portal',
    { testCaseId: 'QA-T428' },
    () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enablePurchaseOrderRequestPortal();
    },
  );
};

export default canEnablePurchaseOrderRequests;
