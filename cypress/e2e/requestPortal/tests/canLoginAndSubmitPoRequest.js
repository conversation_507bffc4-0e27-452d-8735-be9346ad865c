import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';

const canLoginAndSubmitPoRequest = () => {
  it(
    'Admin can submit a Purchase Order request from the Request Portal',
    { testCaseId: 'QA-T90' },
    () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enablePurchaseOrderRequestPortal();
      requestPortalHelpers.goToCreatePoRequestPortal();
    },
  );
};

export default canLoginAndSubmitPoRequest;
