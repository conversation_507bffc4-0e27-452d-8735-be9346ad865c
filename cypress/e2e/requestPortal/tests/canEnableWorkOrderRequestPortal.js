import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';

const canEnableWorkOrderRequestPortal = () => {
  it(
    'Admin is able to navigate to Request Portal page',
    { testCaseId: 'QA-T59' },
    () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enableCompanyRequestPortal();
    },
  );
};

export default canEnableWorkOrderRequestPortal;
