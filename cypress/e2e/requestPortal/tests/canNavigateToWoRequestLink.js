import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';

const canNavigateToWoRequestLink = () => {
  it(
    '<PERSON><PERSON> is able to navigate to Work Order Request link from Request Portal page',
    { testCaseId: 'QA-T60' },
    () => {
      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enableCompanyRequestPortal();
      requestPortalHelpers.goToWorkOrderRequestPage();
    },
  );
};

export default canNavigateToWoRequestLink;
