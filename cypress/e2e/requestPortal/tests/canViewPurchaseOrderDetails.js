import { leftNavigation } from '../../../support/constants';
import * as requestPortalHelpers from '../helpers/requestPortalHelpers';
import * as h from '../../../helpers';

const canViewPurchaseOrderDetails = (email) => {
  it(
    '<PERSON><PERSON> can view Purchase Order details from the Request Portal',
    { testCaseId: 'QA-T93' },
    () => {
      const now = Date.now();
      const title = `PO Request ${now}`;
      const part = `Part B ${now}`;

      h.createPart({ partName: part }, true);

      cy.get(leftNavigation.REQUEST_PORTAL.navSelector).click();

      requestPortalHelpers.enablePurchaseOrderRequestPortal();
      requestPortalHelpers.goToPurchaseOrderRequestPage();
      requestPortalHelpers.loginToPoPortal(email);
      requestPortalHelpers.clickCreateNewPoRequest();
      requestPortalHelpers.fillAndSubmitPoRequest(title, part);
      requestPortalHelpers.verifyPoRequestSubmitted(title);
      requestPortalHelpers.viewPoRequestInPortal(title);
      requestPortalHelpers.verifyPoDetails(title, part);
    },
  );
};

export default canViewPurchaseOrderDetails;
