import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const testId = 'checklistAdminTier2';
  const teamName = 'My Cool Team';

  describe(
    'Checklists Admin Tier 2',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      beforeEach(() => {
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
      });

      adminTests.canCloneChecklist();
    },
  );
});
