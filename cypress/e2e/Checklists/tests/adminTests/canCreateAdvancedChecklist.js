import * as checklistPages from '../../components';
import * as h from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';

const canCreateAdvancedChecklist = () => {
  it('basic functions in Create Checklist page works as intended', () => {
    const now = Date.now();
    const name = 'create advanced test checklist';
    const description = 'create advanced description';
    const assetName = `testAsset-${now}`;
    const taskName = `testsTask-${now}`;
    h.createAsset({ Name: assetName }, true);

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.addChecklistButton.click();
    checklistPages.addEdit.nameInput.type(name, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(description, {
      scrollTo: false,
    });

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskType.click();

    checklistPages.addEdit.subtaskType.shouldExist();
    checklistPages.addEdit.textFieldType.shouldExist();
    checklistPages.addEdit.numberFieldType.shouldExist();
    checklistPages.addEdit.inspectionCheckType.shouldExist();
    checklistPages.addEdit.multipleChoiceType.shouldExist();
    checklistPages.addEdit.meterReadingType.shouldExist();
    checklistPages.addEdit.checklistTaskType.click();

    checklistPages.addEdit.checklistTaskOptions.click();
    checklistPages.addEdit.assignUserOption.click();
    checklistPages.addEdit.assignUserDropdown.nestedSelect([
      '.react-select__option',
      'Tester McTestFace',
    ]);
    checklistPages.addEdit.removeUserOrAssetButton.click();

    checklistPages.addEdit.checklistTaskOptions.click();
    checklistPages.addEdit.assignAssetOption.click();
    checklistPages.addEdit.assignAssetDropdown.nestedSelect([
      '.react-select__option',
      assetName,
    ]);
    checklistPages.addEdit.removeUserOrAssetButton.click();

    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(1);
    checklistPages.addEdit.removeTask.click();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(0);

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskType.click();
    checklistPages.addEdit.subtaskType.click({ force: true });
    checklistPages.addEdit.checklistTaskNameInput.type(taskName);
    checklistPages.addEdit
      .checklistTaskNameInputContains(taskName)
      .shouldBeVisible();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(1);
    checklistPages.addEdit.removeTask.click();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(0);

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskType.click();
    checklistPages.addEdit.textFieldType.click({ force: true });
    checklistPages.addEdit.checklistTaskNameInput.type(taskName);
    checklistPages.addEdit
      .checklistTaskNameInputContains(taskName)
      .shouldBeVisible();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(1);
    checklistPages.addEdit.removeTask.click();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(0);

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskType.click();
    checklistPages.addEdit.numberFieldType.click({ force: true });
    checklistPages.addEdit.checklistTaskNameInput.type(taskName);
    checklistPages.addEdit
      .checklistTaskNameInputContains(taskName)
      .shouldBeVisible();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(1);
    checklistPages.addEdit.removeTask.click();
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(0);

    checklistPages.addEdit.cancelButton.click();
  });
};

export default canCreateAdvancedChecklist;
