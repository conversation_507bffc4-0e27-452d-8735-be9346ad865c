import { createChecklist } from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';
import * as checklistPages from '../../components';

const createChecklists = () => {
  for (let cnt = 0; cnt <= 10; cnt++) {
    createChecklist({ name: `test name ${cnt}` });
  }
};
const canSearchChecklists = () => {
  it('Can Search Checklists', () => {
    upkeepPages.CHECKLISTS.go();
    createChecklists();
    checklistPages.list.searchInput.type('test name 1');
    cy.wait(1000);
    checklistPages.list.checklistRow('test name 1').shouldExist();
    checklistPages.list.searchInput.clear();
    cy.wait(1000);
    checklistPages.list.checklistRows.shouldHaveLengthGreaterThan(10);
  });
};

export default canSearchChecklists;
