import * as checklistPages from '../../components';

const canGenerateChecklistBySmartImport = () => {
  it(
    'can smart import a checklist',
    {
      featureFlags: { checklistSmartImport: true },
      defaultCommandTimeout: 60_000, // PDF Processing may take up to a minute or longer
    },
    () => {
      cy.visit('/web/checklists');
      checklistPages.list.addChecklistButton.click();
      checklistPages.checklistSmartImport.collapsibleIcon.click();
      checklistPages.checklistSmartImport.fileDropZone.selectFile(
        'cypress/fixtures/smartImportPdf.pdf',
        { action: 'drag-drop', force: true },
      );
      checklistPages.checklistSmartImport.processPdfButton.click();
      checklistPages.addEdit.nameInput.invoke('val').should('not.be.empty');

      checklistPages.addEdit.checklistTaskRow.should(
        'have.length.greaterThan',
        1,
      );
    },
  );
};

export default canGenerateChecklistBySmartImport;
