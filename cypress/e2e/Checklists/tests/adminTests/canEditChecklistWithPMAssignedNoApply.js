import * as checklistPages from '../../components';
import * as pmPages from '../../../preventativeMaintenance/components';
import { upkeepPages } from '../../../../support/constants';
import { createChecklistTestData } from '../../helpers/checklistHelpers';
import * as woPagesBeta from '../../../workOrders2.0/components';

const canEditChecklistWithPMAssignedNoApply = () => {
  it('can edit existing checklist that have been added to at least one PM Trigger containing WO without applying changes', () => {
    const {
      now,
      pmTriggerWOName,
      pmTriggerName,
      checklistInWODesc,
      checklistInPMTriggerName,
    } = createChecklistTestData('pmTrigger');

    const editedChecklistInWODesc = `${checklistInWODesc} ${now}`;
    const addedTaskName = `task edit ${now}`;

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.searchInput.type(checklistInPMTriggerName);
    checklistPages.list.checklistRow(checklistInPMTriggerName).click();

    checklistPages.addEdit.nameInput.type(checklistInPMTriggerName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });
    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });
    checklistPages.addEdit.cancelButton.click();

    checklistPages.list.searchInput.type(checklistInPMTriggerName);
    cy.contains(checklistInPMTriggerName).should('exist');
    cy.contains(checklistInWODesc).should('exist');

    checklistPages.list.checklistRow(checklistInPMTriggerName).click();

    checklistPages.addEdit.nameInput.type(checklistInPMTriggerName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });

    checklistPages.addEdit.saveChecklistButton.click();
    checklistPages.addEdit.checklistModalWarning.shouldBeVisible();
    // * Save without applying to WO
    checklistPages.addEdit.saveWithoutApplyButton.click();
    // * Save without applying to PM
    checklistPages.addEdit.saveWithoutApplyButton.click();

    checklistPages.list.searchInput.type(checklistInPMTriggerName);
    cy.contains(checklistInPMTriggerName).should('exist');
    cy.contains(editedChecklistInWODesc).should('exist');

    upkeepPages.WORK_ORDERS.go();
    cy.reload();
    cy.contains(pmTriggerWOName).click();
    woPagesBeta.details.tasksTab.click();
    woPagesBeta.details.checklistItemsList.shouldHaveLength(1);
    woPagesBeta.details.checklistItemsList.shouldNotContain(addedTaskName);

    cy.visit('/web/preventive-maintenance/list?order=ASC');
    cy.reload();
    pmPages.list.pmTriggerRow(pmTriggerName).click();
    pmPages.details.detailsTab.click();
    cy.contains(addedTaskName).should('not.exist');
  });
};

export default canEditChecklistWithPMAssignedNoApply;
