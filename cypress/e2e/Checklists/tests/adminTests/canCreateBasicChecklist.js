import * as checklistPages from '../../components';
import { upkeepPages } from '../../../../support/constants';

const canCreateBasicChecklist = () => {
  it('creates checklist', { testCaseId: 'QA-T6278' }, () => {
    const name = 'create basic test checklist';
    const description = 'create basic description';
    const taskName = 'basic task name';
    upkeepPages.CHECKLISTS.go();

    checklistPages.list.addChecklistButton.click();
    checklistPages.addEdit.nameInput.type(name, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(description, {
      scrollTo: false,
    });

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(taskName, {
      position: 'last',
    });

    checklistPages.addEdit.createChecklistButton.click();
    checklistPages.list.searchInput.type(name);
    checklistPages.list.checklistRow(name).click();

    checklistPages.addEdit.nameInput.shouldHaveValue(name);
    checklistPages.addEdit.descriptionInput.shouldHaveValue(description);
    checklistPages.addEdit.checklistTaskNameInput.shouldHaveValue(taskName);
  });
};

export default canCreateBasicChecklist;
