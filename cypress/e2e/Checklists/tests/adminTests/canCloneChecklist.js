import { createChecklist } from '../../../../helpers';
import * as checklistPages from '../../components';
import { upkeepPages } from '../../../../support/constants';

const canCloneChecklist = () => {
  it('can clone checklist in settings page', { testCaseId: 'QA-T5651' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const name = `Can clone checklist test ${now}`;
    const clonedChecklist = `Clone:${name}`;
    const description = 'a basic description';
    const items = [
      {
        type: 'Task',
        name: `task1 ${now}`,
      },
      {
        type: 'Task',
        name: `task2 ${now}`,
      },
      {
        type: 'Task',
        name: `task3 ${now}`,
      },
    ];

    createChecklist({ name, description, items });
    upkeepPages.CHECKLISTS.go();

    checklistPages.list.checklistRow(name).shouldBeVisible();
    checklistPages.list.rowIconButton(name).click();
    checklistPages.list.duplicateMenuButton.click();
    cy.reload();

    checklistPages.list.checklistRow(clonedChecklist).shouldBeVisible();
    checklistPages.list.checklistRow(clonedChecklist).click({ force: true });
    cy.contains('Edit Checklist').should('be.visible');

    items.forEach((task) => {
      checklistPages.addEdit
        .checklistTaskNameInputContains(task.name)
        .shouldBeVisible();
    });
  });
};

export default canCloneChecklist;
