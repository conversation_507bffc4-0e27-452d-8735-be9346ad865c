import * as checklistPages from '../../components';
import { upkeepPages } from '../../../../support/constants';
import { createChecklist } from '../../../../helpers/createHelpers/createChecklist';
import { createBasicPMTrigger } from '../../../../helpers/createHelpers/createPMTrigger';

const canEditChecklistWithOnlyPMAssigned = () => {
  it('shows PM update modal when editing a checklist with only PM triggers (no work orders)', () => {
    // Create unique identifiers
    const now = Date.now();
    const checklistName = `Checklist with PM Only ${now}`;
    const checklistDesc = `This checklist is used only in PM triggers ${now}`;
    const pmTriggerName = `PM Trigger with Checklist ${now}`;
    const addedTaskName = `New task ${now}`;

    // Create checklist first
    cy.log('Creating test checklist and PM trigger');
    createChecklist({
      name: checklistName,
      description: checklistDesc,
      items: [{ type: 'Task', name: 'Task 1' }],
    }).then((data) => {
      const checklistId = data.body.result.objectId;

      // Create PM with checklist attached but don't create work order
      createBasicPMTrigger({
        name: pmTriggerName,
        formTemplate: checklistId,
        createFirstWO: false, // Important - don't create a work order
        priorityNumber: 0,
        scheduleType: 'EVERY_N_DAYS',
        isNextOccurrenceBasedOnCompletion: false,
        rrfreq: 'DAILY',
        rrinterval: 1,
        rrtzid: 'Asia/Calcutta',
        cadenceType: 'manual',
        cadenceFreq: 'DAILY',
        cadenceInterval: 1,
        arrayOfFormItems: [
          { type: 'Task', name: 'Task 1', originFormTemplateId: checklistId },
        ],
      });

      // Now navigate to checklists and edit the one we created
      upkeepPages.CHECKLISTS.go();
      checklistPages.list.searchInput.type(checklistName);
      checklistPages.list.checklistRow(checklistName).click();

      // Make some edits
      const editedDescription = `${checklistDesc} - edited`;
      checklistPages.addEdit.descriptionInput.type(editedDescription, {
        scrollTo: false,
      });

      // Add a new task
      checklistPages.addEdit.addTaskButton.click();
      checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
        position: 'last',
      });

      // Save the checklist
      cy.log('Saving checklist - should see PM update modal');
      checklistPages.addEdit.saveChecklistButton.click();

      // Verify PM modal is shown (important part - this is what was missing in tests)
      checklistPages.addEdit.checklistModalWarning.shouldBeVisible();
    });
  });
};

export default canEditChecklistWithOnlyPMAssigned;
