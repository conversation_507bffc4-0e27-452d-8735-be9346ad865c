import * as checklistPages from '../../components';
import * as workOrderPages from '../../../workOrders2.0/components';
import { upkeepPages } from '../../../../support/constants';
import { createBasicWorkOrder, createChecklist } from '../../../../helpers';
import { selectChecklistWith } from '../../../workOrders2.0/helpers/woHelpers';

const canEditChecklistWithWOAssignedApplyChanges = () => {
  it.skip('can edit existing checklist that have been added to at least one Work Orders and apply changes', () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const checklistInWOName = `Checklist used in WO ${now}`;
    const checklistInWODesc = 'This checklist will be used in work order';
    const woName = `Work Order with Checklist ${now}`;
    const editedChecklistInWODesc = `${checklistInWODesc} ${now}`;
    const addedTaskName = `task edit ${now}`;

    createBasicWorkOrder({
      mainDescription: woName,
    });

    createChecklist({
      name: checklistInWOName,
      items: [{ type: 'Task', name: 'Task 1' }],
    });

    upkeepPages.WORK_ORDERS.go();
    workOrderPages.list.searchInput.type(woName);
    workOrderPages.list.searchButton.click();
    cy.contains(woName).click();
    selectChecklistWith(checklistInWOName);
    cy.visit('/web/checklists?sort=createdAt');

    checklistPages.list.searchInput.type(checklistInWOName);
    checklistPages.list.checklistRow(checklistInWOName).click();

    checklistPages.addEdit.nameInput.type(checklistInWOName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });

    checklistPages.addEdit.taskList.shouldHaveLength(1);
    checklistPages.addEdit.taskListPreview.shouldHaveLength(1);
    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });
    checklistPages.addEdit.taskList.shouldHaveLength(2);
    checklistPages.addEdit.taskListPreview.shouldHaveLength(2);

    // ! update and apply changes to WO
    checklistPages.addEdit.saveChecklistButton.click();
    checklistPages.addEdit.checklistModalWarning.shouldBeVisible();
    checklistPages.addEdit.applyChangesCheckboxWO.click({
      scrollBehavior: 'bottom',
    });
    checklistPages.addEdit.saveAndApplyButtonWO.click();

    checklistPages.list.searchInput.type(checklistInWOName);
    cy.contains(checklistInWOName).should('exist');
    cy.contains(editedChecklistInWODesc).should('exist');
    cy.wait(5000);

    upkeepPages.WORK_ORDERS.go();
    workOrderPages.list.searchInput.type(woName);
    workOrderPages.list.searchButton.click();
    cy.contains(woName).click();
    workOrderPages.details.checklistItemsList.shouldHaveLength(2);
    workOrderPages.details.checklistItemsList.shouldContain(addedTaskName);
  });
};

export default canEditChecklistWithWOAssignedApplyChanges;
