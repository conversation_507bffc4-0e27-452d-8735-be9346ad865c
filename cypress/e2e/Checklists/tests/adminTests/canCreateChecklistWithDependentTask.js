import * as checklistPages from '../../components';
import * as woPages from '../../../workOrders/components';
import * as woHelpers from '../../../workOrders/helpers/workOrdersHelpers';
import { upkeepPages } from '../../../../support/constants';
import * as h from '../../../../helpers';
import { checkForFeatureFlag } from '../../../../helpers/checkForFeatureFlag';

const canCreateChecklistWithDependentTask = () => {
  it(
    'can create checklist that has a dependent task',
    { featureFlags: { checklistGuidance: true } },
    () => {
      const featureFlag = 'checklistBranching';
      const flag = checkForFeatureFlag(featureFlag, true);
      if (!flag) {
        cy.log(
          `skipping test for feature flag mismatch: ${featureFlag} is false`,
        );
        cy.state('runnable').ctx.skip();
      }

      const assetName = 'Asset 1';
      h.createAsset({ Name: assetName }, true);

      const checklistName = 'checklist with dependent task';
      const checklistDescription =
        'this checklist has tasks with parent-child relationship';
      const parentTaskName = 'parent task';
      const dependentTaskName = 'dependent task';
      const dependentTaskValue = 'dependent value';

      checklistPages.list.addChecklistButton.click();
      checklistPages.addEdit.nameInput.type(checklistName, {
        scrollTo: false,
      });
      checklistPages.addEdit.descriptionInput.type(checklistDescription, {
        scrollTo: false,
      });

      // add parent task
      checklistPages.addEdit.addTaskButton.click();
      checklistPages.addEdit.checklistTaskNameInput.type(parentTaskName, {
        position: 'last',
      });
      checklistPages.addEdit.checklistTaskType.click();
      checklistPages.addEdit.textFieldType.click();
      checklistPages.addEdit.checklistTaskOptions.click();
      checklistPages.addEdit.assignUserOption.click();
      checklistPages.addEdit.assignUserDropdown.nestedSelect([
        '.react-select__option',
        'Tester McTestFace',
      ]);
      checklistPages.addEdit.requiredTaskButton.click();

      // add dependent task
      checklistPages.addEdit.checklistTaskOptions.click();
      checklistPages.addEdit.addDependentTaskOption.click();
      checklistPages.addEdit.dependentTaskValueInput.type(dependentTaskValue, {
        position: 'last',
      });
      checklistPages.addEdit.dependentTaskNameInput.type(dependentTaskName, {
        position: 'last',
      });
      checklistPages.addEdit.dependentTaskOptions.click();
      checklistPages.addEdit.assignAssetOption.click();
      checklistPages.addEdit.assignAssetDropdown.nestedSelect([
        '.react-select__option',
        assetName,
      ]);

      // create and confirm
      checklistPages.addEdit.createChecklistButton.click();
      checklistPages.list.searchInput.type(checklistName);
      checklistPages.list.checklistRow(checklistName).click();

      checklistPages.addEdit.nameInput.shouldHaveValue(checklistName);
      checklistPages.addEdit.descriptionInput.shouldHaveValue(
        checklistDescription,
      );

      checklistPages.addEdit.checklistTaskNameInput.shouldHaveValue(
        parentTaskName,
      );
      checklistPages.addEdit
        .dropdownContains('Tester McTestFace')
        .shouldExist();
      checklistPages.addEdit.requiredTaskButton
        .invoke('attr', 'class')
        .should('contain', 'checked');

      checklistPages.addEdit.dependentTaskValueInput.shouldHaveValue(
        dependentTaskValue,
      );
      checklistPages.addEdit.dependentTaskNameInput.shouldHaveValue(
        dependentTaskName,
      );
      checklistPages.addEdit.dropdownContains(assetName).shouldExist();

      // apply to WO and confirm
      upkeepPages.WORK_ORDERS.go();
      woHelpers.openCreateWorkOrder();
      woHelpers.inputTextFields();
      woPages.edit.addTaskButton.click({
        scrollBehavior: 'bottom',
        force: true,
      });
      woPages.edit.addChecklistButton.click();
      woHelpers.selectChecklistByName(checklistName);
      woPages.edit.updateWorkOrderButton.click();

      checklistPages.workOrderAddEdit.dependentTask.shouldExist();
      woPages.edit.addChecklistButton.click();
      cy.get('div.first-section').find(
        'input[data-cy="checklist-dependent-task-value"]',
      );
      cy.get('div.second-section').find(
        'input[data-cy="checklist-dependent-task-name"]',
      );
      checklistPages.workOrderAddEdit.dependentTaskValueInput.shouldHaveValue(
        dependentTaskValue,
      );
      checklistPages.workOrderAddEdit.dependentTaskNameInput.shouldHaveValue(
        dependentTaskName,
      );
      woPages.edit.saveTask.click();
      woHelpers.submitWorkOrder();

      // dependent task should be hidden at first
      checklistPages.workOrderAddEdit.woFormItem.shouldExist();
      checklistPages.workOrderAddEdit.woFormItem.shouldHaveLength(1);
      checklistPages.workOrderAddEdit
        .woFormItemContains(parentTaskName)
        .shouldExist();
      checklistPages.workOrderAddEdit
        .woFormItemContains(dependentTaskName)
        .shouldNotExist();

      // dependent task should show when value matches
      checklistPages.workOrderAddEdit.woFormItemInput.type(dependentTaskValue, {
        scrollTo: false,
      });
      checklistPages.workOrderAddEdit.woFormItem.click();
      checklistPages.workOrderAddEdit
        .woFormItemContains(dependentTaskName)
        .shouldExist();
    },
  );
};

export default canCreateChecklistWithDependentTask;
