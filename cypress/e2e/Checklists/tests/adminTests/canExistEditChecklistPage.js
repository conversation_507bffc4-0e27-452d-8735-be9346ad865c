import { createChecklist } from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';
import * as checklistPages from '../../components';

const canExistEditChecklistPage = () => {
  it('Can exit edit checklist page', { testCaseId: 'QA-T6281' }, () => {
    const name = 'Can exit edit checklist test';
    const description = 'a basic description';
    createChecklist({ name, description });

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.searchInput.type(name);
    checklistPages.list.checklistRow(name).click();
    checklistPages.addEdit.closeButton.click();
    checklistPages.list.pageTitle.shouldContain('Checklists');
  });
};

export default canExistEditChecklistPage;
