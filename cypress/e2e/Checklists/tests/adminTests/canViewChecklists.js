import { createChecklist } from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';
import * as checklistPages from '../../components';

const canViewChecklists = () => {
  it('Can view existing checklists', { testCaseId: 'QA-T6275' }, () => {
    const name = 'view test checklist';
    const description = 'a basic description';
    createChecklist({ name, description });

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.searchInput.type(name);
    checklistPages.list.checklistRow(name).click();

    checklistPages.addEdit.nameInput.shouldHaveValue(name);
    checklistPages.addEdit.descriptionInput.shouldHaveValue(description);
    checklistPages.addEdit.taskList.shouldHaveLength(3);
  });
};

export default canViewChecklists;
