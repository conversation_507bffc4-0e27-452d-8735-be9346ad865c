import * as checklistPages from '../../components';
import { upkeepPages } from '../../../../support/constants';
import { checkForFeatureFlag } from '../../../../helpers/checkForFeatureFlag';

const canGenerateChecklistWithAI = () => {
  const assetType = 'Car';
  const featureFlag = 'checklistGenerator';
  beforeEach(() => {
    upkeepPages.CHECKLISTS.go();
    checklistPages.list.addChecklistButton.click();
  });

  it('should contain expected values in the Number of Tasks dropdown', () => {
    const flag = checkForFeatureFlag(featureFlag, true);
    if (!flag) {
      cy.log(
        `skipping test for feature flag mismatch: ${featureFlag} is false`,
      );
      cy.state('runnable').ctx.skip();
    }
    checklistPages.checklistGenerator.collapsibleIcon.click();
    checklistPages.checklistGenerator.numberOfTasksDropdown.shouldExist();
    checklistPages.checklistGenerator.numberOfTasksDropdown.click();
    cy.get('[role="option"]').should('have.length', 4);
    cy.get('[role="option"]').should((option) => {
      expect(option.eq(0)).to.contain('5');
      expect(option.eq(1)).to.contain('10');
      expect(option.eq(2)).to.contain('15');
      expect(option.eq(3)).to.contain('20');
    });
  });

  it('should contain expected values in the Checklist Frequency dropdown', () => {
    const flag = checkForFeatureFlag(featureFlag, true);
    if (!flag) {
      cy.log(
        `skipping test for feature flag mismatch: ${featureFlag} is false`,
      );
      cy.state('runnable').ctx.skip();
    }
    checklistPages.checklistGenerator.collapsibleIcon.click();
    checklistPages.checklistGenerator.checklistFrequencyDropdown.shouldExist();
    checklistPages.checklistGenerator.checklistFrequencyDropdown.click();
    cy.get('[role="option"]').should('have.length', 5);
    cy.get('[role="option"]').should((option) => {
      expect(option.eq(0)).to.contain('Select an option');
      expect(option.eq(1)).to.contain('Daily');
      expect(option.eq(2)).to.contain('Weekly');
      expect(option.eq(3)).to.contain('Monthly');
      expect(option.eq(4)).to.contain('Yearly');
    });
  });

  it('should contain expected values in the Checklist Type dropdown', () => {
    const flag = checkForFeatureFlag(featureFlag, true);
    if (!flag) {
      cy.log(
        `skipping test for feature flag mismatch: ${featureFlag} is false`,
      );
      cy.state('runnable').ctx.skip();
    }
    checklistPages.checklistGenerator.collapsibleIcon.click();
    checklistPages.checklistGenerator.checklistTypeDropdown.shouldExist();
    checklistPages.checklistGenerator.checklistTypeDropdown.click();
    cy.get('[role="option"]').should('have.length', 6);
    cy.get('[role="option"]').should((option) => {
      expect(option.eq(0)).to.contain('Select an option');
      expect(option.eq(1)).to.contain('Inspection');
      expect(option.eq(2)).to.contain('Preventative Maintenance');
      expect(option.eq(3)).to.contain('Maintenance');
      expect(option.eq(4)).to.contain('Safety');
      expect(option.eq(5)).to.contain('Audit');
    });
  });

  it('should generate checklist with AI', () => {
    const flag = checkForFeatureFlag(featureFlag, true);
    if (!flag) {
      cy.log(
        `skipping test for feature flag mismatch: ${featureFlag} is false`,
      );
      cy.state('runnable').ctx.skip();
    }
    checklistPages.checklistGenerator.collapsibleIcon.click();
    checklistPages.checklistGenerator.assetTypeInput.type(assetType, {
      scrollTo: false,
    });
    checklistPages.checklistGenerator.numberOfTasksDropdown.click();
    cy.get('[role="option"]').eq(0).click();
    checklistPages.checklistGenerator.checklistFrequencyDropdown.click();
    cy.get('[role="option"]').eq(0).click();
    checklistPages.checklistGenerator.checklistTypeDropdown.click();
    cy.get('[role="option"]').eq(0).click();
    checklistPages.checklistGenerator.generateExampleButton.click();
    checklistPages.addEdit.nameInput.invoke('val').should('not.be.empty');
    checklistPages.addEdit.descriptionInput
      .invoke('val')
      .should('not.be.empty');
    checklistPages.addEdit.checklistTaskRow.shouldHaveLength(5);
  });
};

export default canGenerateChecklistWithAI;
