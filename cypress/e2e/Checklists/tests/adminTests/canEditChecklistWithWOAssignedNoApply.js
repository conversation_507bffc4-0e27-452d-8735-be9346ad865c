import * as checklistPages from '../../components';
import * as workOrderPages from '../../../workOrders2.0/components';
import { upkeepPages } from '../../../../support/constants';
import { createChecklistTestData } from '../../helpers/checklistHelpers';

const canEditChecklistWithWOAssignedNoApply = () => {
  it('can edit existing checklist that have been added to at least one Work Orders without applying changes', () => {
    const { now, checklistInWOName, checklistInWODesc, woName } =
      createChecklistTestData('workOrder');

    const editedChecklistInWODesc = `${checklistInWODesc} ${now} edited`;
    const addedTaskName = `task edit ${now}`;

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.searchInput.type(checklistInWOName);
    checklistPages.list.checklistRow(checklistInWOName).click();

    checklistPages.addEdit.nameInput.type(checklistInWOName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });
    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });
    checklistPages.addEdit.cancelButton.click();

    cy.contains(checklistInWOName).should('exist');
    cy.contains(checklistInWODesc).should('exist');

    checklistPages.list.checklistRow(checklistInWOName).click();

    checklistPages.addEdit.nameInput.type(checklistInWOName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });

    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });

    checklistPages.addEdit.saveChecklistButton.click();
    checklistPages.addEdit.checklistModalWarning.shouldBeVisible();
    checklistPages.addEdit.saveWithoutApplyButton.click();

    cy.contains(checklistInWOName).should('exist');
    cy.contains(editedChecklistInWODesc).should('exist');

    upkeepPages.WORK_ORDERS.go();
    workOrderPages.list.searchInput.type(woName);
    workOrderPages.list.searchButton.click();
    cy.contains(woName).click();
    workOrderPages.details.tasksTab.click();
    workOrderPages.details.checklistItemsList.shouldHaveLength(1);
    workOrderPages.details.checklistItemsList.shouldNotContain(addedTaskName);
  });
};

export default canEditChecklistWithWOAssignedNoApply;
