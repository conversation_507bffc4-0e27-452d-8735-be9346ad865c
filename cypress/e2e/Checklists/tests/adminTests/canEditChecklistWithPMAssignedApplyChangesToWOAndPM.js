import * as checklistPages from '../../components';
import * as pmPages from '../../../preventativeMaintenance/components';
import { upkeepPages } from '../../../../support/constants';
import { createChecklistTestData } from '../../helpers/checklistHelpers';
import * as woPagesBeta from '../../../workOrders2.0/components';

const canEditChecklistWithPMAssignedApplyChangesToWOAndPM = () => {
  it('can edit existing checklist that have been added to at least one PM Trigger containing WO and apply changes for both', () => {
    const {
      now,
      pmTriggerWOName,
      pmTriggerName,
      checklistInWODesc,
      checklistInPMTriggerName,
    } = createChecklistTestData('pmTrigger');

    const editedChecklistInWODesc = `${checklistInWODesc} ${now}`;
    const editedChecklistInWOName = `${checklistInPMTriggerName} ${now}`;
    const addedTaskName = `task edit ${now}`;

    upkeepPages.CHECKLISTS.go();
    checklistPages.list.searchInput.type(checklistInPMTriggerName);
    checklistPages.list.checklistRow(checklistInPMTriggerName).click();

    checklistPages.addEdit.nameInput.type(editedChecklistInWOName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistInWODesc, {
      scrollTo: false,
    });
    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(addedTaskName, {
      position: 'last',
    });

    checklistPages.addEdit.saveChecklistButton.click();
    checklistPages.addEdit.checklistModalWarning.shouldBeVisible();
    checklistPages.addEdit.applyChangesCheckboxWO.click({
      scrollBehavior: 'bottom',
    });
    checklistPages.addEdit.saveAndApplyButtonWO.click();
    checklistPages.addEdit.applyChangesCheckboxPM.click({
      scrollBehavior: 'bottom',
    });
    checklistPages.addEdit.saveAndApplyButtonPM.click();

    checklistPages.list.searchInput.type(checklistInPMTriggerName);
    cy.contains(checklistInPMTriggerName).should('exist');
    cy.contains(editedChecklistInWODesc).should('exist');

    upkeepPages.WORK_ORDERS.go();
    cy.reload();
    cy.contains(pmTriggerWOName).click();
    woPagesBeta.details.tasksTab.click();
    woPagesBeta.details.checklistItemsList.shouldContain(addedTaskName);

    cy.visit('/web/preventive-maintenance/list?order=ASC');
    cy.reload();
    pmPages.list.pmTriggerRow(pmTriggerName).click();
    pmPages.details.detailsTab.click();
    cy.contains(addedTaskName).should('exist');
  });
};

export default canEditChecklistWithPMAssignedApplyChangesToWOAndPM;
