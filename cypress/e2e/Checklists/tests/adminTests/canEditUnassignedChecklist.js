import { createChecklist } from '../../../../helpers';
import { upkeepPages } from '../../../../support/constants';
import * as checklistPages from '../../components';

const canEditUnassignedChecklist = () => {
  it('can edit existing checklist that has not been added to any Work Orders or PM Triggers', () => {
    const name = 'edit unassigned test checklist';
    const description = 'a basic description';
    const editedChecklistName = `${name} edited`;
    const editedChecklistDescription = `${description} edited`;
    const newTaskName = 'new task added';

    upkeepPages.CHECKLISTS.go();
    createChecklist({ name, description });

    checklistPages.list.searchInput.type(name);
    checklistPages.list.checklistRow(name).click();

    checklistPages.addEdit.nameInput.type(editedChecklistName, {
      scrollTo: false,
    });
    checklistPages.addEdit.descriptionInput.type(editedChecklistDescription, {
      scrollTo: false,
    });
    checklistPages.addEdit.addTaskButton.click();
    checklistPages.addEdit.checklistTaskNameInput.type(newTaskName, {
      position: 'last',
    });

    checklistPages.addEdit.saveChecklistButton.click();
    checklistPages.list.searchInput.type(name);
    checklistPages.list.checklistRow(editedChecklistName).shouldExist();
  });
};

export default canEditUnassignedChecklist;
