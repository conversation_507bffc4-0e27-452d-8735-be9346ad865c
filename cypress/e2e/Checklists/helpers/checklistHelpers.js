import { createChecklistWithData } from '../../../helpers';

export const createChecklistTestData = (type) => {
  const now = Date.now();
  const pmTriggerName = `PM Trigger with Checklist ${now}`;
  const pmTriggerWOName = `Work Order with PM and Checklist ${now}`;
  const checklistInWOName = `Checklist used in WO ${now}`;
  const checklistInWODesc = 'This checklist will be used in work order';
  const checklistInPMTriggerName = `Checklist used in WO and PM ${now}`;
  const woName = `Work Order with Checklist ${now}`;
  let [data, name, description] = [];

  if (type === 'workOrder') {
    name = checklistInWOName;
    description = checklistInWODesc;
    data = { workOrder: { mainDescription: woName } };
  }
  if (type === 'pmTrigger') {
    name = checklistInPMTriggerName;
    description = checklistInWODesc;
    data = {
      pmTrigger: {
        mainDescription: pmTriggerWOName,
        name: pmTriggerName,
      },
    };
  }

  createChecklistWithData({ name, description }, data);
  return {
    now,
    pmTriggerName,
    pmTriggerWOName,
    checklistInWOName,
    checklistInWODesc,
    checklistInPMTriggerName,
    woName,
  };
};
