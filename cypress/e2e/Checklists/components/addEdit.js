import { configureSelectorProxy } from '../../../helpers';

const addEdit = configureSelectorProxy({
  nameInput: 'input[data-cy="checklist-name"]',
  descriptionInput: 'textarea[data-cy="checklist-description"]',
  addTaskButton: '[data-cy="addTaskButton"]',
  taskList: '.item-container',
  taskListPreview: '[data-cy="form-item-view"]',
  createChecklistButton: 'button[data-cy="CreateChecklistButton"]',
  saveChecklistButton: 'button[data-cy="SaveChecklistButton"]',
  checklistTaskNameInput: 'input[data-cy="checklist-task-name"]',
  checklistTaskNameInputContains: (name) =>
    `[data-cy="checklist-task"] [value="${name}"] `,
  dependentTaskNameInput: 'input[data-cy="checklist-dependent-task-name"]',
  dependentTaskValueInput: 'input[data-cy="checklist-dependent-task-value"]',
  applyChangesCheckboxPM: '[data-cy="checklist-modal-checkbox-pm"]',
  applyChangesCheckboxWO: '[data-cy="checklist-modal-checkbox-wo"]',
  saveWithoutApplyButton: 'button[data-cy="SaveWithoutApplyingButton"]',
  saveAndApplyButtonWO: 'button[data-cy="Save&ApplytoWorkOrdersButton"]',
  saveAndApplyButtonPM: 'button[data-cy="Save&ApplytoPMTriggersButton"]',
  checklistModalWarning: '[data-cy="checklist-modal-warning"]',
  createPageHelpText: '[data-cy="createPageHelpText"]',
  cancelButton: 'button[data-cy="CancelButton"]',
  closeButton: '[data-cy="checklist-header-left"] .icon-close',
  checklistTaskRow: '[data-cy="checklist-task"]',
  checklistTaskType: '[data-cy="checklist-task-type"]',
  subtaskType: 'li[data-cy="Sub-task Status-0"]',
  textFieldType: 'li[data-cy="Text Field-1"]',
  numberFieldType: 'li[data-cy="Number Field-2"]',
  inspectionCheckType: 'li[data-cy="Inspection Check-3"]',
  multipleChoiceType: 'li[data-cy="Multiple Choice-4"]',
  meterReadingType: 'li[data-cy="Meter Reading-5"]',
  imageType: 'li[data-cy="Image-6"]',
  image: 'div[data-cy="dropzone"]',
  fileType: 'li[data-cy="File-7"]',
  signatureType: 'li[data-cy="Signature-8"]',
  checklistTaskOptions: '[data-cy="checklist-task-options"]',
  dependentTaskOptions: 'div.second-section [data-cy="checklist-task-options"]',
  assignUserOption: 'li[data-cy="Assign User-0"]',
  assignAssetOption: 'li[data-cy="Assign Asset-1"]',
  addDependentTaskOption: 'li[data-cy="Add dependent task-2"]',
  assignUserDropdown: '[data-cy="checklist-task-assign-user"]',
  assignAssetDropdown: '[data-cy="checklist-task-assign-asset"]',
  removeTask: '[data-cy="checklist-task-remove"]',
  removeUserOrAssetButton: '.react-select__clear-indicator',
  requiredTaskButton: '[data-cy="required-task"] [name="required-switch"]',
  dropdownContains: (val) => `.react-select__single-value:contains("${val}")`,
  tagListDropdown: '#TagsListButton',
  tagListOption: (val) => `.item-container:contains("${val}")`,
  tagPill: (val) => `.tag-text:contains("${val}")`,
});

export default addEdit;
