import { configureSelectorProxy } from '../../../helpers';

const checklistGenerator = configureSelectorProxy({
  collapsibleIcon: '[data-cy="toggle-expand-checklistGenerator"]',
  assetTypeInput: 'input[data-cy="checklist-asset-name"]',
  numberOfTasksDropdown: '[data-cy="checklist-generator-number-of-tasks"]',
  checklistFrequencyDropdown: '[data-cy="checklist-generator-frequency"]',
  checklistTypeDropdown: '[data-cy="checklist-generator-checklist-type"]',
  generateExampleButton: '[data-cy="runPromptButton"]',
});

export default checklistGenerator;
