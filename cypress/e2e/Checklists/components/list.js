import { configureSelectorProxy } from '../../../helpers';

const list = configureSelectorProxy({
  pageTitle: '[data-cy="Checklists-header-title"]',
  addChecklistButton: '[data-cy="AddChecklistButton"]',
  checklistRow: (name) => `td:contains("${name}")`,
  searchInput: '[data-cy="searchInput"] input',
  checklistRows: 'table tbody tr',
  rowIconButton: (name) => `tr:contains("${name}") button`,
  deleteMenuButton: '[data-cy="menu-item-Delete"]',
  duplicateMenuButton: 'button:contains("Duplicate")',
});

export default list;
