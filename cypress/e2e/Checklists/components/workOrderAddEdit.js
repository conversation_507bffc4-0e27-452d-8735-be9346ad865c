import { configureSelectorProxy } from '../../../helpers';

const workOrderAddEdit = configureSelectorProxy({
  dependentTask: 'div.dependent-task',
  dependentTaskFirstSection: 'div.first-section',
  dependentTaskSecondSection: 'div.second-section',
  dependentTaskNameInput: 'input[data-cy="checklist-dependent-task-name"]',
  dependentTaskValueInput: 'input[data-cy="checklist-dependent-task-value"]',
  woFormItem: 'div[data-cy="form-item-view"]',
  woFormItemContains: (val) =>
    `div[data-cy="form-item-view"]:contains("${val}")`,
  woFormItemInput: 'div[data-cy="form-item-view"] textarea',
});

export default workOrderAddEdit;
