import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const teamName = 'My Cool Team';

  describe(
    'Edit tests with PM applied to checklist tests',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });
      const testId = 'checklistAdminEditApplyPM';

      beforeEach(() => {
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
      });

      adminTests.canEditChecklistWithPMAssignedNoApply();
      adminTests.canEditChecklistWithPMAssignedApplyChangesToWO();
      adminTests.canEditChecklistWithPMAssignedApplyChangesToWOAndPM();
      adminTests.canEditChecklistWithOnlyPMAssigned();
    },
  );
});
