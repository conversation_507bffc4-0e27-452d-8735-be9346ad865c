import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  const teamName = 'My Cool Team';

  describe(
    'Checklists Admin Edit',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });

      const testId = 'checklistAdminEdit';

      beforeEach(() => {
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
      });

      adminTests.canEditUnassignedChecklist();
      adminTests.canExistEditChecklistPage();
      adminTests.canEditChecklistWithWOAssignedNoApply();
      adminTests.canEditChecklistWithWOAssignedApplyChanges();
    },
  );
});
