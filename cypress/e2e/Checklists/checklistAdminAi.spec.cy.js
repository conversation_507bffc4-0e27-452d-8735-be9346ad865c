import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const testId = 'checklistAdminAi';

  describe(
    'Checklists AI Tests',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });

      before(() => {
        const runnableEnvs = ['staging3', 'staging', 'production'];
        const env = Cypress.env('CYPRESS_ENV');
        if (!runnableEnvs.includes(env)) {
          cy.state('runnable').ctx.skip();
        }
      });

      beforeEach(() => {
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', '');
      });

      adminTests.canGenerateChecklistBySmartImport();
      adminTests.canGenerateChecklistWithAI();
    },
  );
});
