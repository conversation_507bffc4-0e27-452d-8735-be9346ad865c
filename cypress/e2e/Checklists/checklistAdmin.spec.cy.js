import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  const testId = 'checklistAdmin';
  const teamName = 'My Cool Team';

  describe(
    'Checklists Admin Create / View',
    { featureFlags: { 'checklist-ai-feature': false } },
    () => {
      Cypress.on('uncaught:exception', () => {
        return false;
      });

      beforeEach(() => {
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
      });

      describe('create tests', () => {
        adminTests.canCreateBasicChecklist();
        adminTests.canCreateAdvancedChecklist();
      });

      describe('View tests', () => {
        adminTests.canViewChecklists();
        adminTests.canSearchChecklists();
      });

      // Todo: this functionality appears to be broken
      // describe('Can create and apply checklist with dependent task', () => {
      //   adminTests.canCreateChecklistWithDependentTask();
      // });
    },
  );
});
