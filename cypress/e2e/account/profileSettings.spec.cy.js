import filterTests from '../../support/filterTests';
import * as profileTests from './tests';

filterTests(['all', 'smoke', 'ui'], () => {
  describe.skip('Account authentication', () => {
    const testId = 'account-authentication';
    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'team');
    });

    profileTests.canViewUserProfilePage();
    profileTests.canViewEditCompanyProfile();
  });

  describe('Account password reset', () => {
    profileTests.authenticationTests.canResetPasswordAndSignIn();
  });
});
