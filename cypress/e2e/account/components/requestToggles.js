import { configureSelectorProxy } from '../../../helpers';

const commonToggleSelector = '[aria-label="toggle switch"]';

const requestToggles = configureSelectorProxy({
  reqCreatedOnPortalRow: `[data-cy="togrow-reqCreatedOnPortal"] ${commonToggleSelector}`,
  reqNotAssignedUpdatesRow: `[data-cy="togrow-reqNotAssignedUpdates"] ${commonToggleSelector}`,
  requestSelfCreatedCancelledRow: `[data-cy="togrow-requestSelfCreatedCancelled"] ${commonToggleSelector}`,
});

export default requestToggles;
