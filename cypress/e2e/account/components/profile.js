import { configureSelectorProxy } from '../../../helpers';

const profile = configureSelectorProxy({
  profileName: 'div>h5',
  userDetail: (name) => `div[class="profile__item"]:contains("${name}")`,
  changePasswordLink: '[ui-sref="app.account.changePassword"]',
  currentPasswordInput: '[id="currentPassword"]',
  newPasswordInput: '[id="newPassword"]',
  confirmNewPassword: '[id="confirmPassword"]',
  saveButton: 'button[ng-click="ChangePasswordCtrl.saveChanges()"]:last()',
  editProfileButton: '[id="profile-dd"]',
  editProfileOption: 'a:contains("Edit Profile")',
  profileItem: (name) => `.profile__item:contains("${name}")`,
});

export default profile;
