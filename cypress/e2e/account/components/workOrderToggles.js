import { configureSelectorProxy } from '../../../helpers';

const commonToggleSelector = '[aria-label="toggle switch"]';

const workOrderToggles = configureSelectorProxy({
  woCreatedRow: `[data-cy="togrow-woCreated"] ${commonToggleSelector}`,
  woAssignedRow: `[data-cy="togrow-woAssigned"] ${commonToggleSelector}`,
  woAdditionalAssignedRow: `[data-cy="togrow-woAdditionalAssigned"] ${commonToggleSelector}`,
  woMyTeamAssignedRow: `[data-cy="togrow-woMyTeamAssigned"] ${commonToggleSelector}`,
  woFollowedSharedWoRow: `[data-cy="togrow-woFollowedSharedWo"] ${commonToggleSelector}`,
  woNotAssignedUpdatesRow: `[data-cy="togrow-woNotAssignedUpdates"] ${commonToggleSelector}`,
  woTaggedUpdatesRow: `[data-cy="togrow-woTaggedUpdates"] ${commonToggleSelector}`,
});

export default workOrderToggles;
