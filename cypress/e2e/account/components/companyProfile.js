import { configureSelectorProxy } from '../../../helpers';

const companyProfile = configureSelectorProxy({
  profileMenu: '.user-profile-avatar-button',
  companyProfile: '[data-cy="company-profile-button"]',
  editButton: 'button:contains("Edit")',
  companyNameInput: '[data-cy="companyName"]',
  addressInput: '[data-cy="businessAddressLine1"]',
  cityInput: '[data-cy="businessCity"]',
  stateInput: '[data-cy="businessState"]',
  zipCodeInput: '[data-cy="businessZipCode"]',
  phoneNumberInput: '[data-cy="companyPhoneNumber"]',
  saveButton: '[data-cy="Save"]',
  companyName: '[data-cy="companyName"]',
  companyAddress: '[data-cy="Address"]',
  companyPhoneNumber: '[data-cy="Phone Number"]',
});

export default companyProfile;
