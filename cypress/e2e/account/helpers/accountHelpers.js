import * as accountPages from '../components';

export const getUserInfo = () => {
  const user = JSON.parse(window.localStorage.getItem('currentUser'));
  return user;
};

export const verifyProfilePageShows = (name, email) => {
  accountPages.profile.profileName.shouldBeVisible();
  accountPages.profile.profileName.shouldContain(name);

  accountPages.profile.userDetail(email).shouldBeVisible();
};

export const goToChangePassword = () => {
  accountPages.profile.changePasswordLink.click();
  cy.contains('Change Password').should('be.visible');
};

export const changePassword = (newPassword) => {
  accountPages.profile.currentPasswordInput
    .click()
    .type(Cypress.env('PASSWORD'));
  accountPages.profile.newPasswordInput.click().type(newPassword);
  accountPages.profile.confirmNewPassword.click().type(newPassword);
  accountPages.profile.saveButton.click();

  cy.contains('Your password has successfully changed').should('be.visible');
};

export const logoutAndLoginWithNewPassword = (email, password) => {
  cy.logout();
  cy.login(email, password);
  cy.contains('Work Orders').should('be.visible');
};

export const verifyProfilePageContents = (user) => {
  accountPages.profile.userDetail('First Name').shouldBeVisible();
  accountPages.profile.userDetail(user.firstName).shouldBeVisible();

  accountPages.profile.userDetail('Last Name').shouldBeVisible();
  accountPages.profile.userDetail(user.lastName).shouldBeVisible();

  accountPages.profile.userDetail('Phone Number').shouldBeVisible();
  accountPages.profile.userDetail(user.phoneNumber).shouldBeVisible();

  accountPages.profile.userDetail('Job Title').shouldBeVisible();
  accountPages.profile.userDetail(user.jobTitle).shouldBeVisible();

  accountPages.profile.userDetail('Language').shouldBeVisible();
};

export const goToEditCompanyProfile = () => {
  cy.contains('Work Orders').should('be.visible');
  accountPages.companyProfile.profileMenu.shouldBeVisible().click();
  accountPages.companyProfile.companyProfile.shouldBeVisible().click();
  accountPages.companyProfile.editButton.click();
};

export const fillCompanyProfileForm = ({
  companyName,
  address,
  city,
  state,
  zipCode,
  phoneNumber,
}) => {
  cy.contains('Edit Company Profile').should('be.visible');

  accountPages.companyProfile.addressInput.click().clear().type(address);
  accountPages.companyProfile.cityInput.click().clear().type(city);
  accountPages.companyProfile.stateInput.click().clear().type(state);
  accountPages.companyProfile.zipCodeInput.click().clear().type(zipCode);
  accountPages.companyProfile.phoneNumberInput
    .click()
    .clear()
    .type(phoneNumber);
  accountPages.companyProfile.companyNameInput
    .click()
    .clear()
    .type(companyName);
};

export const saveCompanyProfileEdit = () => {
  accountPages.companyProfile.saveButton.click();
};

export const verifyCompanyProfile = ({
  companyName,
  address,
  city,
  state,
  zipCode,
  phoneNumber,
}) => {
  accountPages.companyProfile.companyName.shouldContain(companyName);
  accountPages.companyProfile.companyAddress.shouldContain(
    `${address}, ${city}, ${state}, ${zipCode}`,
  );
  accountPages.companyProfile.companyPhoneNumber.shouldContain(phoneNumber);
};
