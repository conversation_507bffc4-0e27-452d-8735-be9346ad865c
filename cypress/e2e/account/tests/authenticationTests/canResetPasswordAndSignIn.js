import { upkeepPages } from '../../../../support/constants';
import * as accountHelpers from '../../helpers/accountHelpers';

const canResetPasswordAndSignIn = () => {
  it('can reset ther password and sign in', { testCaseId: 'QA-T95' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const now = Date.now();
    const testId = `accountreset${now}`;
    const updatedPassword = 'Onupkeep1234';

    // this is a desctructive test, we need a new session on each run
    cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', 'team').then(() => {
      const user = accountHelpers.getUserInfo();
      const fullName = `${user.firstName} ${user.lastName}`;

      upkeepPages.PROFILE.go();
      accountHelpers.verifyProfilePageShows(fullName, user.email);
      accountHelpers.goToChangePassword();
      accountHelpers.changePassword(updatedPassword);
      accountHelpers.logoutAndLoginWithNewPassword(user.email, updatedPassword);
    });
  });
};

export default canResetPasswordAndSignIn;
