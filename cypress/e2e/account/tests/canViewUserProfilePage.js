import { faker } from '@faker-js/faker';
import { upkeepPages } from '../../../support/constants';
import * as accountHelpers from '../helpers/accountHelpers';
import * as accountPages from '../components';

const canViewUserProfilePage = () => {
  // Skipping this test because it's testing the old angular page, which is no longer used
  it.skip(
    '<PERSON><PERSON> can view user profile page',
    { testCaseId: 'QA-T94', retries: { runMode: 0, openMode: 0 } },

    () => {
      Cypress.on('uncaught:exception', () => false);

      const firstName = faker.name.firstName();
      const lastName = faker.name.lastName();
      const phoneNumber = `${faker.phone.number()}`;
      const jobTitle = faker.name.jobTitle();

      const user = accountHelpers.getUserInfo();
      const fullName = `${user.firstName} ${user.lastName}`;

      upkeepPages.PROFILE.go();
      accountHelpers.verifyProfilePageShows(fullName, user.email);
      accountHelpers.verifyProfilePageContents(user);

      accountPages.profile.editProfileButton.click();
      accountPages.profile.editProfileOption.click();
      accountPages.editProfile.firstName.type(firstName);
      accountPages.editProfile.lastName.type(lastName);
      accountPages.editProfile.phoneNumber.type(phoneNumber);
      accountPages.editProfile.jobTitle.type(jobTitle);
      accountPages.editProfile.saveButton.click();

      accountPages.profile.profileItem(firstName).shouldBeVisible();
      accountPages.profile.profileItem(lastName).shouldBeVisible();
      accountPages.profile.profileItem(phoneNumber).shouldBeVisible();
      accountPages.profile.profileItem(jobTitle).shouldBeVisible();
    },
  );
};

export default canViewUserProfilePage;
