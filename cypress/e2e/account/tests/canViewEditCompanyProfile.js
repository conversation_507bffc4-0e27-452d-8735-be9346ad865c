import { faker } from '@faker-js/faker';
import * as accountHelpers from '../helpers/accountHelpers';

const canViewEditCompanyProfile = () => {
  it(
    '<PERSON><PERSON> can view and edit Company Profile page',
    { testCaseId: 'QA-T98' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const companyName = `${faker.company.name()}`;
      const address = `${faker.address.streetAddress()}`;
      const city = `${faker.address.city()}`;
      const state = `${faker.address.state()}`;
      const zipCode = `${faker.address.zipCode()}`;
      const phoneNumber = `${faker.phone.number()}`;

      const testData = {
        companyName,
        address,
        city,
        state,
        zipCode,
        phoneNumber,
      };

      accountHelpers.goToEditCompanyProfile();
      accountHelpers.fillCompanyProfileForm(testData);
      accountHelpers.saveCompanyProfileEdit();
      accountHelpers.verifyCompanyProfile(testData);
    },
  );
};

export default canViewEditCompanyProfile;
