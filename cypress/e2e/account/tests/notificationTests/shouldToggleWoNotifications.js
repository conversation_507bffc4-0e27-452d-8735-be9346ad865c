import { workOrderToggles } from '../../components';

const shouldToggleWoNotifications = () => {
  it(
    'Should be able to toggle WO email notifications and retain the state on reload',
    { testCaseId: 'QA-T6310' },
    () => {
      workOrderToggles.woCreatedRow.should('not.have.class', 'checked');
      workOrderToggles.woCreatedRow.click();
      workOrderToggles.woAssignedRow.should('not.have.class', 'checked');
      workOrderToggles.woAssignedRow.click();
      workOrderToggles.woAdditionalAssignedRow.should(
        'not.have.class',
        'checked',
      );
      workOrderToggles.woAdditionalAssignedRow.click();
      workOrderToggles.woNotAssignedUpdatesRow.should(
        'not.have.class',
        'checked',
      );
      workOrderToggles.woNotAssignedUpdatesRow.click();

      // wait 5 seconds to complete all the notification toggles.
      cy.wait(5000);
      cy.reload();

      workOrderToggles.woCreatedRow.should('have.class', 'checked');
      workOrderToggles.woAssignedRow.should('have.class', 'checked');
      workOrderToggles.woAdditionalAssignedRow.should('have.class', 'checked');
      workOrderToggles.woNotAssignedUpdatesRow.should('have.class', 'checked');
    },
  );
};

export default shouldToggleWoNotifications;
