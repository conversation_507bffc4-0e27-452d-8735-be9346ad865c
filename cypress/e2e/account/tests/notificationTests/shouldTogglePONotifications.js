import { poToggles } from '../../components';

const shouldTogglePONotifications = () => {
  it(
    'Should be able to toggle Purchase Order email notifications and retain the state on reload',
    { testCaseId: 'QA-T6316' },
    () => {
      poToggles.poCreatedOnPortalRow.should('not.have.class', 'checked');
      poToggles.poCreatedOnPortalRow.click();
      poToggles.poSelfCreatedUpdatesRow.should('not.have.class', 'checked');
      poToggles.poSelfCreatedUpdatesRow.click();

      // wait 3 seconds to complete all the notification toggles.
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(3000);
      cy.reload();

      poToggles.poCreatedOnPortalRow.should('have.class', 'checked');
      poToggles.poSelfCreatedUpdatesRow.should('have.class', 'checked');
    },
  );
};

export default shouldTogglePONotifications;
