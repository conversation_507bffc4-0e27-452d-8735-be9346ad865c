import { reportToggles } from '../../components';

const shouldToggleReportsNotifications = () => {
  it(
    'Should be able to toggle summary and reports email notifications and retain the state on reload',
    { testCaseId: 'QA-T6320' },
    () => {
      reportToggles.dailySummaryEmailRow.scrollIntoView();
      reportToggles.dailySummaryEmailRow.should('not.have.class', 'checked');
      reportToggles.dailySummaryEmailRow.click();

      reportToggles.woDueNextWeekEmailRow.should('not.have.class', 'checked');
      reportToggles.woDueNextWeekEmailRow.click();

      // wait 3 seconds to complete all the notification toggles.
      cy.wait(3000);
      cy.reload();

      reportToggles.dailySummaryEmailRow.should('have.class', 'checked');
      reportToggles.woDueNextWeekEmailRow.should('have.class', 'checked');
    },
  );
};

export default shouldToggleReportsNotifications;
