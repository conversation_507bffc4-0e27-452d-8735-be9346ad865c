import { requestToggles } from '../../components';

const shouldToggleRequestNotifications = () => {
  it(
    'Should be able to toggle WO Request email notifications and retain the state on reload',
    { testCaseId: 'QA-T6312' },
    () => {
      requestToggles.reqCreatedOnPortalRow.should('not.have.class', 'checked');
      requestToggles.reqCreatedOnPortalRow.click();
      requestToggles.reqNotAssignedUpdatesRow.should(
        'not.have.class',
        'checked',
      );
      requestToggles.reqNotAssignedUpdatesRow.click();
      requestToggles.requestSelfCreatedCancelledRow.should(
        'not.have.class',
        'checked',
      );
      requestToggles.requestSelfCreatedCancelledRow.click();

      // wait 3 seconds to complete all the notification toggles.
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(3000);
      cy.reload();

      requestToggles.reqCreatedOnPortalRow.should('have.class', 'checked');
      requestToggles.reqNotAssignedUpdatesRow.should('have.class', 'checked');
      requestToggles.requestSelfCreatedCancelledRow.should(
        'have.class',
        'checked',
      );
    },
  );
};

export default shouldToggleRequestNotifications;
