import filterTests from '../../support/filterTests';

import { upkeepPages } from '../../support/constants';
import { notificationTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe('Account', () => {
    Cypress.on('uncaught:exception', () => {
      return false;
    });
    beforeEach(() => {
      const now = Date.now();
      const testId = `accountAdmin${now}`;
      const team = 'My Awesome Core Team';
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', team);
      upkeepPages.NOTIFICATION_SETTINGS.go();
      cy.contains('Notification Settings').should('be.visible');
    });

    notificationTests.shouldToggleWoNotifications();
    notificationTests.shouldToggleRequestNotifications();
    notificationTests.shouldTogglePONotifications();
    notificationTests.shouldToggleReportsNotifications();
  });
});
