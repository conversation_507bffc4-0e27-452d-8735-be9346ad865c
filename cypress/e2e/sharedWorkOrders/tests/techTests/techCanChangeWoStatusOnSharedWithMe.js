import * as h from '../../../../helpers';
import * as sharedPages from '../../components';
import * as workOrderHelpers from '../../../workOrders/helpers/workOrdersHelpers';
import { leftNavigation } from '../../../../support/constants';

const techCanChangeWoStatusOnSharedWithMe = (emails) => {
  it(
    'tech can change status of Work Order on Shared with me tab',
    { testCaseId: 'QA-T766' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const mainDescription = `Tech Change WO status${now}`;

      // pre-condition
      // Another user has shared at least one Work Order with the current user
      h.createBasicWorkOrder({
        mainDescription,
      });

      cy.reload();
      workOrderHelpers.selectWoFromList(mainDescription);
      workOrderHelpers.selectShareWorkOrder();
      workOrderHelpers.enterEmailToShareWorkOrder(emails.TECH);
      cy.wait(1500);

      // login as tech
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'TECH', {}, sessionToken);
      });

      // can navigate to shared work orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with me').should('be.visible');
      cy.contains('Shared with others').should('be.visible');
      cy.contains(mainDescription).should('be.visible');
      cy.contains(mainDescription).click();
      cy.contains('Work Order Description').should('be.visible');
      cy.contains('Details').should('be.visible');
      sharedPages.shared.inProgressItem.click();
      sharedPages.shared.orderStatusChangedAlert.shouldBeVisible();
    },
  );
};

export default techCanChangeWoStatusOnSharedWithMe;
