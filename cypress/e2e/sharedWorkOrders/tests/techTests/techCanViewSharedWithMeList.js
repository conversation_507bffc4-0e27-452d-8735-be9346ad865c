import * as h from '../../../../helpers';
import { leftNavigation } from '../../../../support/constants';
import * as workOrderHelpers from '../../../workOrders/helpers/workOrdersHelpers';

const techCanViewSharedWithMeList = (emails) => {
  it(
    'tech can view Shared with me list on the Shared Work Orders page',
    { testCaseId: 'QA-T763' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const mainDescription = `Tech Shared WO ${now}`;

      // pre-condition
      // Another user has shared at least one Work Order with the current user
      h.createBasicWorkOrder({
        mainDescription,
        priorityNumber: 1,
      });

      cy.reload();
      workOrderHelpers.selectWoFromList(mainDescription);
      workOrderHelpers.selectShareWorkOrder();
      workOrderHelpers.enterEmailToShareWorkOrder(emails.TECH);
      cy.wait(1500);

      // login as limited tech
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'TECH', {}, sessionToken);
      });

      // can navigate to shared work orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with me').should('be.visible');
      cy.contains('Shared with others').should('be.visible');
      cy.contains(mainDescription).should('be.visible');
      cy.contains(mainDescription).click();
      cy.contains('Work Order Description').should('be.visible');
      cy.contains('Details').should('be.visible');
    },
  );
};

export default techCanViewSharedWithMeList;
