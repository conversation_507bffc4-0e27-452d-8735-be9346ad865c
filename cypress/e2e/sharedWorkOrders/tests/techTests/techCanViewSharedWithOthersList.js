import * as h from '../../../../helpers';
import { leftNavigation } from '../../../../support/constants';
import { shareWorkOrderWith } from '../../helpers';

const techCanViewSharedWithOthersList = (emails) => {
  it(
    'tech can view ‘Shared with others’ list on the Shared Work Orders page',
    { testCaseId: 'QA-T764' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();

      const mainDescription = `tech can see shared WO with others ${now}`;
      h.createBasicWorkOrder({ mainDescription });
      cy.reload();

      shareWorkOrderWith(emails.TECH, mainDescription);

      // login as limited tech
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'TECH', {}, sessionToken);
      });

      // can navigate to shared work orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with others').click();
      cy.contains(mainDescription).should('exist');
    },
  );
};

export default techCanViewSharedWithOthersList;
