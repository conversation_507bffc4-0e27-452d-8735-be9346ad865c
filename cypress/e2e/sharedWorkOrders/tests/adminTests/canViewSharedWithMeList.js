import * as h from '../../../../helpers';
import { leftNavigation } from '../../../../support/constants';
import * as workOrderHelpers from '../../../workOrders/helpers/workOrdersHelpers';

const canViewSharedWithMeList = (testId, emails, now) => {
  it('admin can view shared with me list', { testCaseId: 'QA-T65' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const mainDescription = `Shared WO ${now}`;

    // pre-condition
    // Another user has shared at least one Work Order with the current user
    cy.window().then((window) => {
      const sessionToken = window.localStorage.authToken;
      cy.switchUserAndLoginUser(
        testId,
        'LIMITED_ADMIN',
        {
          firstName: 'Cool',
          lastName: 'Lim Tech',
        },
        sessionToken,
      );
    });

    h.createBasicWorkOrder({
      mainDescription,
      priorityNumber: 1,
    });

    cy.reload();
    workOrderHelpers.selectWoFromList(mainDescription);
    workOrderHelpers.selectShareWorkOrder();
    workOrderHelpers.enterEmailToShareWorkOrder(emails.ADMIN);

    // login as admin
    cy.createOrLoginAdmin(testId);

    // can navigate to shared work orders page
    cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
    cy.contains('Shared with me').should('be.visible');
    cy.contains('Shared with others').should('be.visible');
    cy.contains(mainDescription).should('be.visible');
    cy.contains(mainDescription).click();
    cy.contains('Work Order Description').should('be.visible');
    cy.contains('Details').should('be.visible');
  });
};

export default canViewSharedWithMeList;
