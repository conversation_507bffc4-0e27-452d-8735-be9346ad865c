import * as h from '../../../../helpers';
import { leftNavigation } from '../../../../support/constants';
import { shareWorkOrderWith } from '../../helpers';

const canViewSharedWithOthersList = (testId, emails, now) => {
  it('admin can view shared with others list', { testCaseId: 'QA-T68' }, () => {
    Cypress.on('uncaught:exception', () => false);
    const mainDescription = `Share WO with others ${now}`;
    h.createBasicWorkOrder({ mainDescription });
    cy.reload();

    shareWorkOrderWith(emails.TECH, mainDescription);

    // can navigate to shared work orders page
    cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
    cy.contains('Shared with others').click();
    cy.contains(mainDescription).should('be.visible');
  });
};

export default canViewSharedWithOthersList;
