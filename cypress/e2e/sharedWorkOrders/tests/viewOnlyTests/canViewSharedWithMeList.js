import { upkeepPages, leftNavigation } from '../../../../support/constants';
import * as h from '../../../../helpers';
import * as woHelpers from '../../../workOrders/helpers/workOrdersHelpers';

const canViewSharedWithMeList = (now, testId, emails) => {
  it(
    'View Only user can view "Shared With Me" list on Shared Work Orders page',
    { testCaseId: 'QA-T607' },
    () => {
      const mainDescription = `Shared WO ${now}`;

      // As Admin user, create a Work Order to share
      cy.createOrLoginAdmin(testId);
      h.createBasicWorkOrder({ mainDescription });
      upkeepPages.WORK_ORDERS.go();
      cy.reload();
      woHelpers.selectWoFromList(mainDescription);
      woHelpers.selectShareWorkOrder();
      woHelpers.enterEmailToShareWorkOrder(emails.VIEW_ONLY);

      // Logout and login as View Only user
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(
          testId,
          'VIEW_ONLY',
          {
            firstName: 'super',
            lastName: 'view only',
            email: emails.VIEW_ONLY,
          },
          sessionToken,
        );
      });

      // Go to Shared Work Orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with me').should('be.visible');
      cy.contains('Shared with others').should('be.visible');
      cy.contains(mainDescription).should('be.visible');
      cy.contains(mainDescription).click();
      cy.contains('Work Order Description').should('be.visible');
      cy.contains('Details').should('be.visible');
    },
  );
};

export default canViewSharedWithMeList;
