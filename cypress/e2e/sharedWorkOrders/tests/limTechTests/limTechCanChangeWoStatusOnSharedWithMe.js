import * as h from '../../../../helpers';
import * as sharedPages from '../../components';
import { leftNavigation } from '../../../../support/constants';
import * as workOrderHelpers from '../../../workOrders/helpers/workOrdersHelpers';

const limTechCanChangeWoStatusOnSharedWithMe = (emails) => {
  it(
    'limited tech can view shared with me list',
    { testCaseId: 'QA-T446' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();
      const mainDescription = `Lim Tech Change WO status${now}`;

      // pre-condition
      // Another user has shared at least one Work Order with the current user
      h.createBasicWorkOrder({
        mainDescription,
        priorityNumber: 2,
      });

      cy.reload();
      workOrderHelpers.selectWoFromList(mainDescription);
      workOrderHelpers.selectShareWorkOrder();
      workOrderHelpers.enterEmailToShareWorkOrder(emails.LIMITED_TECH);
      cy.wait(1500);

      // login as limited tech
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'LIMITED_TECH', {}, sessionToken);
      });

      // can navigate to shared work orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with me').should('be.visible');
      cy.contains('Shared with others').should('be.visible');
      cy.contains(mainDescription).should('be.visible');
      cy.contains(mainDescription).click();
      cy.contains('Work Order Description').should('be.visible');
      cy.contains('Details').should('be.visible');
      sharedPages.shared.inProgressItem.click();
      sharedPages.shared.orderStatusChangedAlert.shouldBeVisible();
    },
  );
};

export default limTechCanChangeWoStatusOnSharedWithMe;
