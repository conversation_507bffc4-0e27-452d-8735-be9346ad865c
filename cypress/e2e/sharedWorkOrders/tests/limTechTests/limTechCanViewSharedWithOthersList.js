import * as h from '../../../../helpers';
import { leftNavigation } from '../../../../support/constants';
import { shareWorkOrderWith } from '../../helpers';

const limTechCanViewSharedWithOthersList = (emails) => {
  it(
    'lim tech can view shared with others list',
    { testCaseId: 'QA-T444' },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const now = Date.now();

      const mainDescription = `Share WO with others ${now}`;
      h.createBasicWorkOrder({ mainDescription });
      cy.reload();

      shareWorkOrderWith(emails.TECH, mainDescription);

      // login as limited tech
      cy.window().then((window) => {
        const sessionToken = window.localStorage.authToken;
        cy.switchUserAndLoginUser(now, 'LIMITED_TECH', {}, sessionToken);
      });

      // can navigate to shared work orders page
      cy.get(leftNavigation.SHARED_WORK_ORDERS.navSelector).click();
      cy.contains('Shared with others').click();
      cy.contains(mainDescription).should('be.visible');
    },
  );
};

export default limTechCanViewSharedWithOthersList;
