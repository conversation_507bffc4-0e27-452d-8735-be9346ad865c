import filterTests from '../../support/filterTests';
import { techTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe(
    'Shared Work Orders Technician',
    { featureFlags: { enforceWOBetaForNewCustomers: true } },
    { retries: { runMode: 0, openMode: 0 } },
    () => {
      const now = Date.now();
      const teamId = 'My Cool Team';
      const testId = 'techsharedwo';
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const emails = {
        ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
        TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
        LIMITED_TECH: `engineering-test+${testId}_limtech_${now}@${domain}`,
      };

      beforeEach(() => {
        const env = Cypress.env('CYPRESS_ENV');
        if (env === 'production') {
          cy.state('runnable').ctx.skip();
        }
        cy.createOrLoginAdmin(now, [], 'BUSINESS_PLUS', teamId, emails);
        cy.window().then((adminSession) => {
          cy.switchUser(
            now,
            'TECH',
            {
              email: emails.TECH,
              firstName: 'Limited',
              lastName: 'Technician',
            },
            adminSession.localStorage.authToken,
          );
        });
      });

      techTests.techCanViewSharedWithMeList(emails);
      techTests.techCanViewSharedWithOthersList(emails);
      techTests.techCanChangeWoStatusOnSharedWithMe(emails);
    },
  );
});
