import filterTests from '../../support/filterTests';
import { adminTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe(
    'Shared Work Orders Admin',
    { featureFlags: { enforceWOBetaForNewCustomers: true } },
    { retries: { runMode: 0, openMode: 0 } },
    () => {
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');
      const now = Date.now();
      const teamId = 'My Cool Team';
      const testId = `adminsharedwo`;
      const emails = {
        ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
        LIMITED_ADMIN: `engineering-test+${testId}_limitedAdmin_${now}@${domain}`,
        TECH: `engineering-test+${testId}_tech_${now}@${domain}`,
      };

      beforeEach(() => {
        const env = Cypress.env('CYPRESS_ENV');
        if (env === 'production') {
          cy.state('runnable').ctx.skip();
        }
        cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamId, emails);
      });

      adminTests.canViewSharedWithMeList(testId, emails, now);
      adminTests.canViewSharedWithOthersList(testId, emails, now);
    },
  );
});
