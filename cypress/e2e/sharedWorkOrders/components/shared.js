import { configureSelectorProxy } from '../../../helpers';

const shared = configureSelectorProxy({
  inProgressItem: '[id="status"] li:contains("In Progress")',
  orderStatusChangedAlert: '.alert:contains("Order status is changed")',
  updatesTab: '[heading="UPDATES"] a',
  postUpdateTextarea: '[ng-model="updateForm.newTaskUpdate"]',
  postUpdateButton: '[ng-click="sendUpdate(task, updateForm.newTaskUpdate)"]',
});

export default shared;
