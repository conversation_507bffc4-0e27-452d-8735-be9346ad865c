import filterTests from '../../support/filterTests';
import { viewOnlyTests } from './tests';

filterTests(['all', 'ui', 'tier2'], () => {
  describe(
    'Shared Work Orders - View Only',
    { featureFlags: { enforceWOBetaForNewCustomers: true } },
    () => {
      Cypress.on('uncaught:exception', () => false);
      const domain = Cypress.env('CYPRESS_EMAIL_DOMAIN');

      const now = Date.now();
      const testId = 'sharedwo';
      const emails = {
        ADMIN: `engineering-test+${testId}_admin_${now}@${domain}`,
        VIEW_ONLY: `engineering-test+${testId}_vo_${now}@${domain}`,
      };

      beforeEach(() => {
        cy.createOrLoginAdmin(testId, ['VIEW_ONLY'], 'BUSINESS_PLUS', '');
      });

      viewOnlyTests.canViewSharedWithMeList(now, testId, emails);
    },
  );
});
