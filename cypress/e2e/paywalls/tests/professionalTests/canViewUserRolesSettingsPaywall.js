import { upkeepPages } from '../../../../support/constants';
import * as customRolesHelpers from '../../../customRoles/helpers/customRolesHelpers';
import * as paywall from '../../components';

const canViewUserRolesSettingsPaywall = () => {
  it('Verify user roles paywall banner is displayed', () => {
    // Load user roles settings page
    upkeepPages.SETTINGS.go();
    customRolesHelpers.visitUserRolesTab();

    // Verify user roles paywall banner is displayed
    paywall.paywallBanners.userRolesPaywallBannerText.shouldBeVisible();
    paywall.paywallBanners.upgradeButton.shouldBeVisible();
    paywall.paywallBanners.contactSalesButton.shouldBeVisible();
  });
};

export default canViewUserRolesSettingsPaywall;
