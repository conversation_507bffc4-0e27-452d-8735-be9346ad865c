import { upkeepPages } from '../../../../support/constants';
import * as paywall from '../../components';

const canViewPurchaseOrdersSettingsPaywall = () => {
  it('Verify purchase orders paywall banner is displayed', () => {
    // Load purchase orders settings page
    upkeepPages.SETTINGS.go();
    cy.get('span:contains("Purchase Orders")').click();

    // Verify purchase orders paywall banner is displayed
    paywall.paywallBanners.purchaseOrdersPaywallBannerText.shouldBeVisible();
    paywall.paywallBanners.upgradeButton.shouldBeVisible();
    paywall.paywallBanners.contactSalesButton.shouldBeVisible();
  });
};

export default canViewPurchaseOrdersSettingsPaywall;
