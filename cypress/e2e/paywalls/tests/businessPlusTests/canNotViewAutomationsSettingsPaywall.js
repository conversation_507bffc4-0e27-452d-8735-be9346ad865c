import { upkeepPages } from '../../../../support/constants';
import * as awfHelpers from '../../../automatedWorkflows/helpers/awfHelpers';
import * as paywall from '../../components';

const canNotViewAutomationsSettingsPaywall = () => {
  it('Verify automation paywall banner is not displayed', () => {
    // Load automation settings page
    upkeepPages.SETTINGS.go();
    awfHelpers.visitAutomationTab();

    // Verify automation paywall banner is not displayed
    paywall.paywallBanners.automationPaywallBannerText.shouldNotExist();
    paywall.paywallBanners.upgradeButton.shouldNotExist();
    paywall.paywallBanners.contactSalesButton.shouldNotExist();
  });
};

export default canNotViewAutomationsSettingsPaywall;
