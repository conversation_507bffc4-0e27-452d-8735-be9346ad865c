import { upkeepPages } from '../../../../support/constants';
import * as customRolesHelpers from '../../../customRoles/helpers/customRolesHelpers';
import * as paywall from '../../components';

const canNotViewUserRolesSettingsPaywall = () => {
  it('Verify user roles paywall banner is not displayed', () => {
    // Load user roles settings page
    upkeepPages.SETTINGS.go();
    customRolesHelpers.visitUserRolesTab();

    // Verify user roles paywall banner is not displayed
    paywall.paywallBanners.userRolesPaywallBannerText.shouldNotExist();
    paywall.paywallBanners.upgradeButton.shouldNotExist();
    paywall.paywallBanners.contactSalesButton.shouldNotExist();
  });
};

export default canNotViewUserRolesSettingsPaywall;
