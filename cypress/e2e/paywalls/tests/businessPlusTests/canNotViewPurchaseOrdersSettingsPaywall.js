import { upkeepPages } from '../../../../support/constants';
import * as paywall from '../../components';

const canNotViewPurchaseOrdersSettingsPaywall = () => {
  it('Verify purchase orders paywall banner is not displayed', () => {
    // Load purchase orders settings page
    upkeepPages.SETTINGS.go();
    cy.get('span:contains("Purchase Orders")').click();

    // Verify purchase orders paywall banner is not displayed
    paywall.paywallBanners.purchaseOrdersPaywallBannerText.shouldNotExist();
    paywall.paywallBanners.upgradeButton.shouldNotExist();
    paywall.paywallBanners.contactSalesButton.shouldNotExist();
  });
};

export default canNotViewPurchaseOrdersSettingsPaywall;
