import { upkeepPages } from '../../../../support/constants';
import * as awfHelpers from '../../../automatedWorkflows/helpers/awfHelpers';
import * as paywall from '../../components';

const canViewAutomationsSettingsPaywall = () => {
  it('Verify paywall banner is displayed', () => {
    // Load automation settings page
    upkeepPages.SETTINGS.go();
    awfHelpers.visitAutomationTab();

    // Verify paywall banner is displayed
    paywall.paywallBanners.automationPaywallBannerText.shouldBeVisible();
    paywall.paywallBanners.upgradeButton.shouldBeVisible();
    paywall.paywallBanners.contactSalesButton.shouldBeVisible();
  });
};

export default canViewAutomationsSettingsPaywall;
