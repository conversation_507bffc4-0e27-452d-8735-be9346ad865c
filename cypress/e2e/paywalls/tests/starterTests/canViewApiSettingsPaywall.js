import { upkeepPages } from '../../../../support/constants';
import * as settingsHelpers from '../../../settings/helpers/settingsHelpers';
import * as paywall from '../../components';

const canViewApiSettingsPaywall = () => {
  it('Verify API paywall banner is displayed', () => {
    // Load API settings page
    upkeepPages.SETTINGS.go();
    settingsHelpers.visitApiSettings();

    // Verify API paywall banner is displayed
    paywall.paywallBanners.apiPaywallBannerText.shouldBeVisible();
    paywall.paywallBanners.upgradeButton.shouldBeVisible();
    paywall.paywallBanners.contactSalesButton.shouldBeVisible();
  });
};

export default canViewApiSettingsPaywall;
