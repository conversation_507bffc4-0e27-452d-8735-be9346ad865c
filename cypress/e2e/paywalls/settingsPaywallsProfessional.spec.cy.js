import filterTests from '../../support/filterTests';
import { settingsPaywallsProfessionalTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe('Settings Paywalls Professional', () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      const testId = 'settings-paywalls-professional';
      const teamName = 'Automation Team';
      cy.createOrLoginAdmin(testId, [], 'PROFESSIONAL', teamName);
    });

    settingsPaywallsProfessionalTests.canViewAutomationsSettingsPaywall();
    settingsPaywallsProfessionalTests.canViewUserRolesSettingsPaywall();
    settingsPaywallsProfessionalTests.canViewPurchaseOrdersSettingsPaywall();
  });
});
