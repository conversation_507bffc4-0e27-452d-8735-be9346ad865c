import { AVAILABLE_PLANS_DISPLAY_NAMES } from '../../support/planConstants';
import filterTests from '../../support/filterTests';
import { settingsPaywallsBusinessPlusTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe(`Settings Paywalls ${AVAILABLE_PLANS_DISPLAY_NAMES.BUSINESS_PLUS}`, () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      const testId = 'settings-paywalls-business-plus';
      const teamName = 'Automation Team';
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', teamName);
    });

    settingsPaywallsBusinessPlusTests.canNotViewAutomationsSettingsPaywall();
    settingsPaywallsBusinessPlusTests.canNotViewUserRolesSettingsPaywall();
    settingsPaywallsBusinessPlusTests.canNotViewPurchaseOrdersSettingsPaywall();
  });
});
