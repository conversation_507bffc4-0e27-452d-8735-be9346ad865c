import { configureSelectorProxy } from '../../../helpers';

const paywallBanners = configureSelectorProxy({
  upgradeButton: '[data-cy="generic.upgrade"]',
  contactSalesButton: '[data-cy="generic.contactSales"]',
  automationPaywallBannerText:
    'span:contains("Create and manage multiple automated workflows with our Enterprise plan")',
  userRolesPaywallBannerText:
    'span:contains("Create custom user roles with our Enterprise plan")',
  purchaseOrdersPaywallBannerText:
    'span:contains("Create and manage purchase orders with our Enterprise plan")',
  apiPaywallBannerText:
    'span:contains("Create and manage API access with our Enterprise plan")',
});

export default paywallBanners;
