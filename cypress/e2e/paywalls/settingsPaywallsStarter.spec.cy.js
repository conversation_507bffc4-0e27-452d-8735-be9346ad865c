import { AVAILABLE_PLANS_DISPLAY_NAMES } from '../../support/planConstants';
import filterTests from '../../support/filterTests';
import { settingsPaywallsStarterTests } from './tests';

filterTests(['all', 'tier2', 'ui'], () => {
  describe(`Settings Paywalls ${AVAILABLE_PLANS_DISPLAY_NAMES.STARTER}`, () => {
    Cypress.on('uncaught:exception', () => false);

    beforeEach(() => {
      const testId = 'settings-paywalls-starter';
      const teamName = 'Automation Team';
      cy.createOrLoginAdmin(testId, [], 'STARTER', teamName);
    });

    settingsPaywallsStarterTests.canViewAutomationsSettingsPaywall();
    settingsPaywallsStarterTests.canViewUserRolesSettingsPaywall();
    settingsPaywallsStarterTests.canViewPurchaseOrdersSettingsPaywall();
    settingsPaywallsStarterTests.canViewApiSettingsPaywall();
  });
});
