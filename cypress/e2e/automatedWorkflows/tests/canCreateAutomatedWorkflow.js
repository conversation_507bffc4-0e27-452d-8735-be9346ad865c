import { createWoCategory, createAsset } from '../../../helpers/createHelpers';
import { upkeepPages } from '../../../support/constants';

import * as awfPages from '../components';
import * as awfHelpers from '../helpers/awfHelpers';

const canCreateAutomatedWorkflow = (team) => {
  it(
    '<PERSON><PERSON> can add and edit an Automated Workflow',
    { testCaseId: 'QA-T109' },
    () => {
      const now = Date.now();
      const awfName = `automated workflow ${now}`;
      const workflowTitle = `Edit Workflow - AW WorkOrder ${now}`;
      const categoryName = 'Category - AW WorkOrder if Category then Asset';
      const assetName = 'Asset - AW WorkOrder if Category then Asset';

      const values = {
        ifInitial: 'Work Order is created',
        andInitial: 'Work Order Category is...',
        andValueInitial: categoryName,
        thenInitial: 'Assign Asset to Work Order',
        thenValueInitial: assetName,
      };

      upkeepPages.SETTINGS.go();

      awfHelpers.visitAutomationTab();
      awfHelpers.createAutomatedWorkflow(awfName, team);
      awfHelpers.verifyWorkflowCreated(awfName);
      cy.contains(awfName).click();

      createWoCategory({ category: categoryName }).then(() => {
        createAsset({ Name: assetName }).then(() => {
          awfPages.automation.workflowTitleInput.type(workflowTitle);

          // And work order category is
          awfPages.automation.andListButton.click();
          awfPages.automation.woCategoryIsOption.click();
          awfPages.automation.categortListButton.click();
          cy.contains(categoryName).click();

          // then assign asset
          awfPages.automation.thenListButton.click();
          awfPages.automation.assignAssetToWoOption.click();
          awfPages.automation.assetListButton.click();
          cy.contains(assetName).click();

          awfPages.automation.saveButton.should('not.be.disabled');
          awfPages.automation.saveButton.click();
          awfPages.automation.workflowInList(workflowTitle).shouldExist();
          cy.contains(workflowTitle).click();

          awfPages.automation.ifListButton.shouldContain(values.ifInitial);
          awfPages.automation.andListButton.shouldContain(values.andInitial);
          awfPages.automation.categortListButton.shouldContain(
            values.andValueInitial,
          );
          awfPages.automation.thenListButton.shouldContain(values.thenInitial);
          awfPages.automation.assetListButton.shouldContain(
            values.thenValueInitial,
          );
          awfPages.automation.cancelButton.click();
        });
      });
    },
  );
};

export default canCreateAutomatedWorkflow;
