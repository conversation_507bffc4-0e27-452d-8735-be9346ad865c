import * as awfHelpers from '../helpers/awfHelpers';
import * as awfPages from '../components';

const canVisitAutomatedWorkflowTab = () => {
  it(
    'Admin can navigate to Automated Workflows tab',
    { testCaseId: 'QA-T108' },
    () => {
      awfPages.settings.settingsNavButton.scrollIntoView();
      awfPages.settings.settingsNavButton.click();
      awfHelpers.visitAutomationTab();
      cy.url().should('include', '/web/settings/sections/automation');
    },
  );
};

export default canVisitAutomatedWorkflowTab;
