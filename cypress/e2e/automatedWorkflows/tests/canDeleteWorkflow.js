import { upkeepPages } from '../../../support/constants';
import * as awfPages from '../components';
import * as awfHelpers from '../helpers/awfHelpers';

const canDeleteWorkflow = (team) => {
  it('Allows deletion a workflow', { testCaseId: 'QA-T6299' }, () => {
    const now = Date.now();
    const awfName = `delete workflow ${now}`;

    upkeepPages.SETTINGS.go();
    awfHelpers.visitAutomationTab();
    awfHelpers.createAutomatedWorkflow(awfName, team);
    awfPages.automation.workflowInList(awfName).shouldExist();

    awfPages.automation.deleteWorkflowButton.click();
    awfPages.automation.confirmDeleteButton.click();
    cy.wait(1500);
    awfPages.automation.workflowInList(awfName).shouldNotExist();
  });
};

export default canDeleteWorkflow;
