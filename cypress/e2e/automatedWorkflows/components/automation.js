import { configureSelectorProxy } from '../../../helpers';

const automation = configureSelectorProxy({
  // Page and workflow creation elements
  newWorkflowButton: 'button:contains("New Workflow")',
  workflowTitleInput: '[data-cy="workflow-title-field"]',

  // IF condition selectors
  ifListButton: '[id="IfListButton"]',
  ifDropdown: '[id="IfListButton"]',
  ifValueDropdown: '[data-cy="if-value-dropdown"]',
  ifTypeOption: (option) => `[data-cy="${option}"]`,
  woIsCreatedOption: '[data-cy="Work Order is created"]',

  // AND condition selectors
  addAndButton: 'button:contains("Add AND")',
  addNewAndButton: 'button:contains("Add New")',
  andListButton: '[id="And (optional)ListButton"]',
  andDropdown: '[id="And (optional)ListButton"]',
  andValueDropdown: '[data-cy="and-value-dropdown"]',
  andTypeOption: (option) => `[data-cy="${option}"]`,
  woPriorityIsOption: '[data-cy="Work Order Priority is..."]',
  woCategoryIsOption: '[data-cy="Work Order Category is..."]',

  // THEN action selectors
  thenListButton: '[id="ThenListButton"]',
  thenDropdown: '[id="ThenListButton"]',
  thenValueDropdown: '[data-cy="then-value-dropdown"]',
  thenTypeOption: (option) => `[data-cy="${option}"]`,
  assignTeamToWoOption: '[data-cy="Assign Team to Work Order"]',
  assignAssetToWoOption: '[data-cy="Assign Asset to Work Order"]',

  // Dropdown elements
  assetListButton: '[id="AssetListButton"]',
  locationListButton: '[id="LocationListButton"]',
  priorityListButton: '[id="PriorityListButton"]',
  categortListButton: '[id="CategoryListButton"]',
  userListButton: '[id="UserListButton"]',
  teamListButton: '[id="TeamListButton"]',
  dropdownSearchInput: '.dropdown-search-input',
  highPriorityOption: '[data-cy="High"]',
  teamOption: (team) => `[data-cy="${team}"]`,

  // Buttons
  cancelButton: 'button:contains("Cancel")',
  createButton: 'button:contains("Create")',
  saveButton: 'button:contains("Save")',
  saveWorkflowButton: 'button:contains("Save")',
  closePortal: '.close-portal',

  // Workflow management
  workflowInList: (name) => `[data-cy="workflowList"]:contains("${name}")`,
  workflowItem: (name) => `[data-cy="workflowList"]:contains("${name}")`,
  deleteWorkflowButton: '[data-cy="deleteWorkflowButton"]:last',
  confirmDeleteButton: '[data-cy="Delete"]',
});

export default automation;
