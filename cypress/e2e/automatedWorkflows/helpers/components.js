import { configureSelectorProxy } from '../../../helpers';

export const workflowSettings = configureSelectorProxy({
  createWorkflowButton: '[data-cy="create-workflow-button"]',
  cancelButton: '[data-cy="workflow-cancel-button"]',
  deleteButton: '[data-cy="deleteWorkflowButton"]',
  titleInput: '[data-cy="workflow-title-input"]',
  ifDropdown: '[data-cy="workflow-select-if"] .react-select__control',
  andDropdown: '[data-cy="workflow-select-and"] .react-select__control',
  andValueDropdown:
    '[data-cy="workflow-select-and-value"] .react-select__control',
  thenDropdown: '[data-cy="workflow-select-then"] .react-select__control',
  thenValueDropdown:
    '[data-cy="workflow-select-then-value"] .react-select__control',
  thenAssetValueDropdown:
    '[data-cy="select-asset-async"] .react-select__control',
  saveButton: '[data-cy="workflow-save-button"]',
  workflowList: '[data-cy="workflowList"]',
  workflowListTitle: '[data-cy="workflowListItemTitle"]',
});
