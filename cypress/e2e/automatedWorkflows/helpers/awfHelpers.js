import * as awfPages from '../components';

export const visitAutomationTab = () => {
  awfPages.settings.automationNavButton.click();
  cy.contains('Automated Workflows').should('be.visible');
};

export const createAutomatedWorkflow = (name, team) => {
  awfPages.automation.createButton.click();

  /// Input workflow title
  awfPages.automation.workflowTitleInput.type(name);

  // If work order is created
  awfPages.automation.ifListButton.click();
  awfPages.automation.woIsCreatedOption.click();

  // And work order priority is High
  awfPages.automation.andListButton.click();
  awfPages.automation.woPriorityIsOption.click();
  awfPages.automation.priorityListButton.click();
  awfPages.automation.highPriorityOption.click();

  // Then assign team to list
  awfPages.automation.thenListButton.click();
  awfPages.automation.assignTeamToWoOption.click();
  awfPages.automation.teamListButton.click();
  awfPages.automation.teamOption(team).click();

  // Save workflow
  awfPages.automation.saveButton.click();
};

export const verifyWorkflowCreated = (name) => {
  awfPages.automation.workflowInList(name).shouldBeVisible();
};
