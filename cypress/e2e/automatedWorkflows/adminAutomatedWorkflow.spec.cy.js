import filterTests from '../../support/filterTests';
import * as awfTests from './tests';
import { canDeleteWorkflow } from './tests';

filterTests(['all', 'ui', 'smoke'], () => {
  describe('Admin tests for Automated Workflows', () => {
    Cypress.on('uncaught:exception', () => false);

    const testId = 'awf-test';
    const team = 'My Cool Team';

    beforeEach(() => {
      cy.createOrLoginAdmin(testId, [], 'BUSINESS_PLUS', team);
    });

    awfTests.canVisitAutomatedWorkflowTab();
    awfTests.canCreateAutomatedWorkflow(team);
    awfTests.canLoadWorkflow(team);
    canDeleteWorkflow(team);
  });
});
