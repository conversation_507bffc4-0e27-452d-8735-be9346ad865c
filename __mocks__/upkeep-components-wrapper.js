// Custom wrapper for @upkeepapp/upkeep-components to fix UMD bundle styled-components issue

// Ensure styled-components is properly available for UMD bundle
const styledComponents = require('styled-components');

// Make sure styled-components methods are available directly (not just under .default)
if (styledComponents.default && typeof styledComponents.default.div === 'function' && !styledComponents.div) {
  Object.assign(styledComponents, styledComponents.default);
}

// Set up global for UMD bundle if needed
if (typeof global !== 'undefined') {
  global.styled = styledComponents;
}

// Now require the actual upkeep-components
module.exports = require('@upkeepapp/upkeep-components/dist/upkeep-components.umd.js'); 