const styledFn = () => {
  const fn = () => {
    const component = () => {
      const StyledComponent = () => null;
      StyledComponent.displayName = 'StyledComponent';
      StyledComponent.defaultProps = {
        display: 'true',
      };
      return StyledComponent;
    };
    component.withConfig = () => component;
    return component;
  };
  fn.withConfig = () => fn;
  return fn;
};

const styled = styledFn();

const elements = [
  'div', 'span', 'button', 'a', 'section', 'aside', 'h1', 'h2', 'h3', 
  'h4', 'h5', 'h6', 'p', 'label', 'input', 'textarea', 'select', 'img',
  'figure', 'figcaption', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th',
  'thead', 'tbody', 'form', 'nav', 'header', 'footer', 'main',
  'article', 'time', 'small', 'strong', 'em', 'i', 'b', 'pre', 'code', 'hr'
];

elements.forEach((element) => {
  styled[element] = styledFn();
});

module.exports = {
  __esModule: true,
  default: styled,
  createGlobalStyle: () => null,
  css: () => null,
  keyframes: () => '',
  ThemeProvider: ({ children }) => children,
};
