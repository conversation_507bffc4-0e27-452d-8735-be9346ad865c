name: Build
run-name: ${{ inputs.trigger_details != '' && inputs.trigger_details || inputs.trigger_details == '' && github.event.head_commit.message}}
on:
  push:
    branches-ignore:
      # - 'deploy-**'
      # - 'hotfix-**'
      - 'gh-readonly-queue/**'
    tags:
      - v**
  workflow_dispatch:
    inputs:
      trigger_details:
        type: string
        default: ''
      action:
        required: true
        type: string
        default: 'build'
      cypress_env:
        required: true
        type: string
        default: 'staging'
      cypress_url:
        required: true
        type: string
        default: 'https://staging.onupkeep.com'
      cypress_api_url:
        required: true
        type: string
        default: 'https://demo-api.onupkeep.com'
      cypress_spec:
        type: string
        default: './cypress/**/*.spec.cy.js'
      cypress_filter:
        type: string
        default: ''
      cypress_test_cycle_key:
        type: string
        default: ''
      slack_context:
        type: string
        default: ''
      cypress_x_api_key:
        type: string
        default: ''

env:
  COMMIT_SHA: ${{ github.sha }}
  BRANCH_TAG: ${{ github.event.repository.name }}
  GITHUB_REF: ${{ github.ref_name }}
  DOCKER_IMAGE: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com/${{ github.event.repository.name }}:${{ github.ref_name }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  CIRCLECI_TOKEN: ${{ secrets.CIRCLECI_TOKEN }}
  S3_BUCKET: 'assets.onupkeep.com'
  APP_NAME: 'app'
  SLACK_WEBHOOK_URL: ${{ inputs.slack_context == 'SlackWebhookPreRelease' && secrets.SLACK_WEBHOOK_URL || inputs.slack_context == 'SlackWebhookStaging' && secrets.SLACK_WEBHOOK_STAGING || inputs.slack_context == 'SlackWebhookPostRelease' && secrets.SLACK_WEBHOOK_POST_RELEASE || inputs.slack_context == 'SlackWebhookPreMerge' && secrets.SLACK_WEBHOOK_PRE_MERGE || '' }}
  CI_USERNAME: ${{ github.triggering_actor }}
  CI_BUILD_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  CI_BRANCH: ${{ github.ref_name }}
  CI_PROJECT_REPONAME: ${{ github.repository }}
  CI_SHA1: ${{ github.sha }}
  CDN_HOST_URL: 'https://assets.onupkeep.com'
concurrency:
  group: ci-web-app-${{ github.event.inputs.action }}-${{ github.ref_name }}
  cancel-in-progress: ${{ github.ref_name != 'develop' && github.ref_name != 'main' }}

jobs:
  ecr_login:
    uses: upkeepapp/github-actions/.github/workflows/ecr_token.yml@main
    secrets: inherit

  pre_test_image_build:
    if: ${{ inputs.action != 'deploy' && github.ref != 'refs/heads/develop' && github.ref != 'refs/heads/main' && !startsWith(github.ref, 'refs/tags/') && !startsWith(github.ref, 'refs/heads/deploy') }}
    needs: ecr_login
    runs-on: ubuntu-latest-m
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://npm.pkg.github.com'
      - name: copy .npmrc file to working dir
        run: |
          cp $NPM_CONFIG_USERCONFIG $GITHUB_WORKSPACE/
          sed -i 's/${NODE_AUTH_TOKEN}/${{ secrets.TECHOPS_PACKAGE_FULL_ACCESS }}/g' .npmrc
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

      - name: Build
        run: yarn build

      - name: Build storybook
        run: yarn build-storybook -o ./dist-storybook

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          role-session-name: GithubActionsWebApp

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build Docker Image
        run: |
          docker build -t ${{ env.DOCKER_IMAGE }} --secret id=npmrc,src=$GITHUB_WORKSPACE/.npmrc --build-arg ENV=staging --build-arg BUILD_TAG=${{ env.BRANCH_TAG }} --build-arg GITHUB_REF=${{ github.ref_name }} --build-arg COMMIT_SHA=${{ env.COMMIT_SHA }} .
          docker push ${{ env.DOCKER_IMAGE }}
      - name: Upload to s3
        run: |
          aws s3 cp --acl public-read --recursive --exclude dist/index.html --cache-control max-age=604800 dist s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/${{ env.COMMIT_SHA }}/
          aws s3 cp --acl public-read --cache-control max-age=604800 ./dist/assets/images/favicon/favicon.ico s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/static/web/assets/images/favicon/favicon.ico

  test_pretest_build:
    if: ${{ inputs.action != 'deploy' && github.ref != 'refs/heads/develop' && github.ref != 'refs/heads/main' && !startsWith(github.ref, 'refs/tags/') && !startsWith(github.ref, 'refs/heads/deploy') }}
    needs: ecr_login
    runs-on: ubuntu-latest-m
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://npm.pkg.github.com'
      - name: copy .npmrc file to working dir
        run: |
          cp $NPM_CONFIG_USERCONFIG $GITHUB_WORKSPACE/
          sed -i 's/${NODE_AUTH_TOKEN}/${{ secrets.TECHOPS_PACKAGE_FULL_ACCESS }}/g' .npmrc
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Test
        run: yarn test

  test-and-build:
    if: ${{ inputs.action != 'deploy' && github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/') || startsWith(github.ref, 'refs/heads/deploy') }}
    needs: ecr_login
    runs-on: ubuntu-latest-m
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://npm.pkg.github.com'
      - name: copy .npmrc file to working dir
        run: |
          cp $NPM_CONFIG_USERCONFIG $GITHUB_WORKSPACE/
          sed -i 's/${NODE_AUTH_TOKEN}/${{ secrets.TECHOPS_PACKAGE_FULL_ACCESS }}/g' .npmrc

      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

      - name: Test
        run: yarn test

      - name: Build
        run: yarn build

      - name: Build storybook
        run: yarn build-storybook -o ./dist-storybook

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          role-session-name: GithubActionsWebApp

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build Docker Image
        run: |
          docker build -t ${{ env.DOCKER_IMAGE }} --secret id=npmrc,src=$GITHUB_WORKSPACE/.npmrc --build-arg ENV=staging --build-arg BUILD_TAG=${{ env.BRANCH_TAG }} --build-arg GITHUB_REF=${{ github.ref_name }} --build-arg COMMIT_SHA=${{ env.COMMIT_SHA }} .
          docker push ${{ env.DOCKER_IMAGE }}

      - name: Sentry release and source maps upload
        continue-on-error: true
        uses: getsentry/action-release@v3
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ vars.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
        with:
          environment: app.onupkeep.com
          sourcemaps: "./dist"
          version: ${{ env.GITHUB_REF }}

      - name: Upload to s3
        run: |
          aws s3 cp --acl public-read --recursive --exclude dist/index.html --cache-control max-age=604800 dist s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/${{ env.COMMIT_SHA }}/
          aws s3 cp --acl public-read --cache-control max-age=604800 ./dist/assets/images/favicon/favicon.ico s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/static/web/assets/images/favicon/favicon.ico
      - name: Trigger Integration Pipeline
        if: github.ref == 'refs/heads/develop'
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: config.yml
          ref: nonprod/upkeep/integration
          client_payload: '{"repo-name": "${{ github.repository }}", "username": "${{ github.actor }}", "commit_url":"https://github.com/${{ github.repository}}/commit/${{ github.sha }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false

  cypress-tests:
    if: ${{ inputs.action == 'deploy' }}
    needs: ecr_login
    runs-on: ubuntu-latest-m
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    continue-on-error: true
    strategy:
      fail-fast: false
      matrix:
        # run copies of the current job in parallel
        containers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://npm.pkg.github.com'
      - name: copy .npmrc file to working dir
        run: |
          cp $NPM_CONFIG_USERCONFIG $GITHUB_WORKSPACE/
          sed -i 's/${NODE_AUTH_TOKEN}/${{ secrets.TECHOPS_PACKAGE_FULL_ACCESS }}/g' .npmrc

      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

      - name: Run split Cypress tests
        id: cypress
        uses: cypress-io/github-action@v6
        with:
          spec: '${{ inputs.cypress_spec }}'
          browser: chrome
          install: false
        env:
          MIXPANEL_TOKEN: false
          CYPRESS_ENV: '${{ inputs.cypress_env }}'
          CYPRESS_URL: '${{ inputs.cypress_url }}'
          CYPRESS_API_URL: '${{ inputs.cypress_api_url }}'
          SPLIT: ${{ strategy.job-total }}
          SPLIT_INDEX: ${{ strategy.job-index }}
          CYPRESS_testCycleKey: ${{inputs.cypress_test_cycle_key}}
          CYPRESS_X_API_KEY: ${{ inputs.cypress_x_api_key }}
          ZEPHYR_TOKEN: ${{secrets.ZEPHYR_TOKEN}}
          LAUNCH_DARKLY_TOKEN: ${{secrets.LAUNCH_DARKLY_TOKEN}}
          CYPRESS_tags: ${{ inputs.cypress_filter }}
          CYPRESS_SSO_AZURE_PASSWORD: ${{secrets.CYPRESS_SSO_AZURE_PASSWORD}}

      - name: Upload cypress-screenshots
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-screenshots-${{ strategy.job-index }}
          path: cypress/screenshots
          overwrite: true
      - name: Upload cypress-videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos-${{ strategy.job-index }}
          path: cypress/videos
      - name: Upload cypress-reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-${{ strategy.job-index }}
          path: cypress/reports
      - name: Upload cypress-results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-results-${{ strategy.job-index }}
          path: cypress/results

  deploy-reports:
    if: ${{ inputs.action == 'deploy' }}
    needs: [ecr_login, cypress-tests]
    runs-on: ubuntu-latest
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Set current date as env variable
        run: echo "builddate=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
        id: date
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download cypress test results
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-results-*
          path: cypress/results
          merge-multiple: true
      - name: Download cypress test videos
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-videos-*
          path: /tmp/cypress/videos
          merge-multiple: true
      - name: Download cypress test screenshots
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-screenshots-*
          path: /tmp/cypress/screenshots
          merge-multiple: true
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}
      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Merge Reports
        run: yarn report:merge
      - name: Generate Result
        run: yarn report:generate --reportTitle "${{ inputs.cypress_env }} ${{ inputs.cypress_url }} Tests"
      - name: Copy and rename files
        run: |
          cp -r /tmp/cypress/videos /tmp/cypress/reports
          cp -r /tmp/cypress/screenshots /tmp/cypress/reports
          mkdir -p ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
          cp -r /tmp/cypress/reports ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
      - name: Upload cypress-reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-temp
          path: /tmp/cypress/reports
      # - name: Upload S3
      #   uses: shallwefootball/s3-upload-action@master
      #   id: S3
      #   with:
      #     aws_key_id: ${{secrets.AWS_ACCESS_KEY_ID}}
      #     aws_secret_access_key: ${{secrets.AWS_SECRET_ACCESS_KEY}}
      #     aws_bucket: upkeep-cypress-test
      #     source_dir: ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
      #     destination_dir: ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: upload to s3
        run: aws s3 cp --acl public-read --recursive ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports s3://upkeep-cypress-test/${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/

  deploy-slack-alerts:
    if: ${{ inputs.action == 'deploy' && inputs.slack_context != null }}
    needs: [ecr_login, deploy-reports]
    runs-on: ubuntu-latest
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Set current date as env variable
        run: echo "builddate=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
        id: date
      - name: Checkout
        uses: actions/checkout@v4
      - name: Download cypress test results
        uses: actions/download-artifact@v4
        with:
          name: cypress-reports-temp
          path: /tmp/cypress/reports
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}
      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Update Results on Slack
        run: |
          yarn cypress-slack-reporter --report-dir /tmp/cypress/reports --ci-provider custom --custom-url https://s3.amazonaws.com/upkeep-cypress-test/${{ steps.date.outputs.builddate }}-${{ github.run_id }} --custom-text "Author: ${{ inputs.trigger_details }}, Environment: ${{ inputs.cypress_env }}" --verbose

  deploy-alert:
    if: ${{ inputs.action == 'deploy' }}
    needs: [ecr_login, deploy-reports]
    runs-on: ubuntu-latest
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Download cypress test results
        uses: actions/download-artifact@v4
        with:
          name: cypress-reports-temp
          path: /tmp/cypress/reports
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}
      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Fail on Failures
        run: yarn report:check:failures

  staging-prod-deployment:
    if: ${{ inputs.action != 'deploy'}}
    needs: [ecr_login, test-and-build]
    runs-on: ubuntu-latest
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:20
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Trigger Deploy Branch
        if: ${{ github.ref == 'refs/heads/develop' }}
        uses: convictional/trigger-workflow-and-wait@v1.6.5
        with:
          owner: upkeepapp
          repo: web-app
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: tag_bump_and_changelog.yml
          ref: develop
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false

      - name: Deploy to staging3
        if: startsWith(github.ref, 'refs/tags/') && contains(github.ref_name, 'beta') && contains(github.ref_name, '0-beta')
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: common_config.yml
          ref: nonprod/upkeep/staging3
          client_payload: '{"repo_name": "web-app", "image_tag": "${{ github.ref_name }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false

      # - name: Deploy to staging4
      #   if: startsWith(github.ref, 'refs/tags/') && contains(github.ref_name, 'beta') && contains(github.ref_name, '1-beta')
      #   uses: convictional/trigger-workflow-and-wait@v1.6.1
      #   with:
      #     owner: upkeepapp
      #     repo: upkeep
      #     github_token: ${{ secrets.TECHOPS_PAT }}
      #     github_user: upkeeptechops
      #     workflow_file_name: web-app_config.yml
      #     ref: nonprod/upkeep/staging4
      #     client_payload: '{"repo_name": "web-app", "image_tag": "${{ github.ref_name }}"}'
      #     propagate_failure: true
      #     trigger_workflow: true
      #     wait_workflow: false

      - name: Deploy to prod
        if: startsWith(github.ref, 'refs/tags/') && !contains(github.ref_name, 'beta')
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: update_tag.yml
          ref: prod/upkeep/web-app-release
          client_payload: '{"repo_name": "web-app", "image_tag": "${{ github.ref_name }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: false
