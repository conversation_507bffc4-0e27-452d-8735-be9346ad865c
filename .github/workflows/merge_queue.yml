name: Validate PR
run-name: validate ${{ github.ref_name }} by ${{ github.actor }}

on:
  merge_group:
    types: [checks_requested]

# concurrency:
#   group: workflow-lock
#   cancel-in-progress: false

env:
  COMMIT_SHA: ${{ github.sha }}
  S3_BUCKET: 'assets.onupkeep.com'
  APP_NAME: 'app'
jobs:
  ecr_login:
    uses: upkeepapp/github-actions/.github/workflows/ecr_token.yml@main
    secrets: inherit

  deploy-to-integration02:
    if: ${{ github.event_name == 'merge_group' }}
    needs: ecr_login
    runs-on: ubuntu-latest
    outputs:
      pr_number: ${{ steps.pr_number_image_tag.outputs.pr_number }}
      branch_name: ${{ steps.pr_number_image_tag.outputs.branch_name }}
      image_tag: ${{ steps.pr_number_image_tag.outputs.image_tag }}
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:18
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: 'https://npm.pkg.github.com'
      - name: Install GH CLI
        uses: dev-hanz-ops/install-gh-cli-action@v0.2.0
        with:
          gh-cli-version: 2.32.0 # optional, see action.yml for current default
      - name: Authenticate with GitHub CLI
        if: ${{ github.event_name == 'merge_group' }}
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | gh auth login --with-token
      - name: Fetch PR details using gh command
        id: pr_number_image_tag
        if: ${{ github.event_name == 'merge_group' }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git config --global --add safe.directory /__w/web-app/web-app
          IMAGE_TAG=$(echo "${{ github.ref_name }}" | grep -oP 'pr-\d+')
          PR_NUMBER=$(echo "${{ github.ref_name }}" | grep -oP '(?<=pr-)\d+')
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV
          echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
          echo "PR Number: $PR_NUMBER"
          echo "BRANCH_NAME=$(gh pr view $PR_NUMBER --json headRefName --jq '.headRefName')" >> $GITHUB_ENV
          echo "branch_name=$(gh pr view $PR_NUMBER --json headRefName --jq '.headRefName')" >> $GITHUB_OUTPUT
      - name: copy .npmrc file to working dir
        run: |
          cp $NPM_CONFIG_USERCONFIG $GITHUB_WORKSPACE/
          sed -i 's/${NODE_AUTH_TOKEN}/${{ secrets.TECHOPS_PACKAGE_FULL_ACCESS }}/g' .npmrc
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Build
        run: yarn build

      - name: Build storybook
        run: yarn build-storybook -o ./dist-storybook

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          role-session-name: MySessionName

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          mask-password: 'true'

      - name: Build Docker Image
        env:
          DOCKER_IMAGE: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com/web-app:${{ env.IMAGE_TAG }}-${{github.sha}}
        run: |
          docker build -t ${{ env.DOCKER_IMAGE }} --secret id=npmrc,src=$GITHUB_WORKSPACE/.npmrc --build-arg ENV=staging --build-arg BUILD_TAG=${{ github.repository }} --build-arg GITHUB_REF=${{ github.ref_name }} --build-arg COMMIT_SHA=${{ github.sha }} .
          docker push ${{ env.DOCKER_IMAGE }}
      - name: Upload to s3
        run: |
          aws s3 cp --acl public-read --recursive --exclude dist/index.html --cache-control max-age=604800 dist s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/${{ env.COMMIT_SHA }}/
          aws s3 cp --acl public-read --cache-control max-age=604800 ./dist/assets/images/favicon/favicon.ico s3://${{ env.S3_BUCKET }}/${{ env.APP_NAME }}/static/web/assets/images/favicon/favicon.ico
      - name: Trigger Integration Pipeline
        uses: convictional/trigger-workflow-and-wait@v1.6.5
        with:
          owner: upkeepapp
          repo: upkeep
          github_token: ${{ secrets.TECHOPS_PAT }}
          github_user: upkeeptechops
          workflow_file_name: config.yml
          ref: nonprod/upkeep/integration02
          client_payload: '{"repo_name": "web-app", "username": "${{ github.actor }}", "image_tags": "${{ env.IMAGE_TAG }}-${{github.sha}}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: true

  cypress-tests:
    if: ${{ github.event_name == 'merge_group' }}
    needs: [ecr_login, deploy-to-integration02]
    runs-on: ubuntu-latest-m
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:18
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    continue-on-error: true
    strategy:
      fail-fast: false
      matrix:
        # run copies of the current job in parallel
        containers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}

      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

      - name: Run split Cypress tests
        id: cypress
        uses: cypress-io/github-action@v6
        with:
          spec: './cypress/**/*.spec.cy.js'
          browser: chrome
          install: false
        env:
          MIXPANEL_TOKEN: false
          CYPRESS_ENV: 'integration02'
          CYPRESS_URL: 'https://app-integration02.onupkeep.com'
          CYPRESS_API_URL: 'https://api-integration02.onupkeep.com'
          SPLIT: ${{ strategy.job-total }}
          SPLIT_INDEX: ${{ strategy.job-index }}
          CYPRESS_testCycleKey: ''
          CYPRESS_X_API_KEY: 'app-integration02-random-token-123'
          ZEPHYR_TOKEN: ${{secrets.ZEPHYR_TOKEN}}
          LAUNCH_DARKLY_TOKEN: ${{secrets.LAUNCH_DARKLY_TOKEN}}
          CYPRESS_tags: ${{ vars.TEST_TYPE }}
      - name: Upload cypress-screenshots
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-screenshots-${{ strategy.job-index }}
          path: cypress/screenshots
          overwrite: true
      - name: Upload cypress-videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos-${{ strategy.job-index }}
          path: cypress/videos
      - name: Upload cypress-reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-${{ strategy.job-index }}
          path: cypress/reports
      - name: Upload cypress-results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-results-${{ strategy.job-index }}
          path: cypress/results

  validate-pr:
    needs: [ecr_login, cypress-tests, deploy-to-integration02]
    if: ${{ always() }}
    runs-on: ubuntu-latest
    timeout-minutes: 60
    container:
      image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:18
      credentials:
        username: AWS
        password: ${{ needs.ecr_login.outputs.ecr_password }}
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_PRE_MERGE }}
      CI_USERNAME: ${{ github.triggering_actor }}
      CI_BUILD_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
      CI_BRANCH: ${{ needs.deploy-to-integration02.outputs.branch_name }}
      CI_PROJECT_REPONAME: ${{ github.repository }}
      CI_SHA1: ${{ github.workflow_sha }}
    steps:
      - name: Set current date as env variable
        run: echo "builddate=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
        id: date
      - name: Checkout
        uses: actions/checkout@v4
      - name: Download cypress test results
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-results-*
          path: cypress/results
          merge-multiple: true
      - name: Download cypress test videos
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-videos-*
          path: /tmp/cypress/videos
          merge-multiple: true
      - name: Download cypress test screenshots
        uses: actions/download-artifact@v4
        with:
          pattern: cypress-screenshots-*
          path: /tmp/cypress/screenshots
          merge-multiple: true
      - name: Cache node_modules
        uses: actions/cache@v4
        id: cache-node_modules
        with:
          path: |
            node_modules
            /github/home/<USER>/Cypress
          key: ${{ runner.os }}-node_cypress_deps-${{ hashFiles('yarn.lock') }}
      - name: Install dependencies
        if: steps.cache-node_modules.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile
      - name: Merge Reports
        run: yarn report:merge
      - name: Generate Result
        run: yarn report:generate --reportTitle "Cypress Test result for image - ${{ needs.deploy-to-integration02.outputs.image_tag }} deployed to https://app-integration02.onupkeep.com/"
      - name: Copy and rename files
        run: |
          set +e
          cp -r /tmp/cypress/videos /tmp/cypress/reports
          cp -r /tmp/cypress/screenshots /tmp/cypress/reports
          mkdir -p ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
          cp -r /tmp/cypress/reports ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
      - name: Upload cypress-reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-temp
          path: /tmp/cypress/reports
      # - name: Upload S3
      #   uses: shallwefootball/s3-upload-action@master
      #   id: S3
      #   with:
      #     aws_key_id: ${{secrets.AWS_ACCESS_KEY_ID}}
      #     aws_secret_access_key: ${{secrets.AWS_SECRET_ACCESS_KEY}}
      #     aws_bucket: upkeep-cypress-test
      #     source_dir: ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports
      #     destination_dir: ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: upload to s3
        run: aws s3 cp --acl public-read --recursive ${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports s3://upkeep-cypress-test/${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/

      - name: Add the test results to Summary Page
        run: |
          echo "Cypress Test Results Report: https://s3.amazonaws.com/upkeep-cypress-test/${{ steps.date.outputs.builddate }}-${{ github.run_id }}/tmp/cypress/reports/index.html" >> $GITHUB_STEP_SUMMARY
      - name: Update Results on Slack
        run: |
          yarn cypress-slack-reporter --report-dir /tmp/cypress/reports --ci-provider custom --custom-url https://s3.amazonaws.com/upkeep-cypress-test/${{ steps.date.outputs.builddate }}-${{ github.run_id }} --custom-text "Author: ${{ github.actor }}, Environment: integration02" --verbose
      - name: Get cypress stats
        id: stats
        run: |
          apt install jq -y
          echo "FAILURES=$(cat /tmp/cypress/reports/index.json | jq '.stats.failures' )" >> $GITHUB_ENV
      - name: Get failure stats
        run: echo ${{ env.FAILURES }}
      - name: Check errors threshold
        if: ${{ github.event_name == 'merge_group' }}
        run: |
          if [ ${{ env.FAILURES }} -gt ${{ vars.THRESHOLD}} ]; then
            echo "Error threshold exceeded with ${{ env.FAILURES }} errors."
            exit 1
          else
            echo "Error threshold not exceeded. ${{ env.FAILURES }} errors found."
          fi
