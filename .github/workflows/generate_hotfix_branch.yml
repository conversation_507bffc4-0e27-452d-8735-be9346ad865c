name: Generate Hotfix Branch

on:
  workflow_dispatch:
jobs:
  cut-branch:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: 'main'
      - name: set user config
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Actions"
      - name: get-todays-date
        id: todays-date
        run: echo "date=$(TZ='America/Los_Angeles' date +'%Y-%m-%d-%H-%M-%S')" >> $GITHUB_OUTPUT
      - name: check relase sha against dev sha and cut branch if diff
        run: |
          git checkout -b hotfix-${{ steps.todays-date.outputs.date }}
          git push --set-upstream origin \
            hotfix-${{ steps.todays-date.outputs.date }}
