name: Generate Deploy Branch for prod release

on:
  schedule:
    - cron: "0 20 * * 1-5"
  workflow_dispatch:

jobs:
  conditionally-cut-branch:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: 'main'
      - name: set user config
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Actions"
      - name: Set owner and repo variables
        run: |
          echo "OWNER=$(echo ${{ github.repository }} | cut -d/ -f1)" >> $GITHUB_ENV
          echo "REPO=$(echo ${{ github.repository }} | cut -d/ -f2)" >> $GITHUB_ENV
      - name: get-todays-date
        id: todays-date
        run: echo "date=$(TZ='America/Los_Angeles' date +'%Y-%m-%d-%H-%M-%S')" >> $GITHUB_OUTPUT
      - name: Check relase sha against dev sha and cut branch if diff
        id: diff-and-push
        env:
          TODAY: ${{ steps.todays-date.outputs.date }}
        run: |
          git fetch origin develop --depth=1
          git checkout develop
          git pull
          changes="$(git diff origin/develop..origin/main)"
          if [ -z $changes ]; then
            echo "has-changes=false" >> $GITHUB_OUTPUT
          else
            echo "has-changes=true" >> $GITHUB_OUTPUT
            git checkout -b deploy-${{ steps.todays-date.outputs.date }}
            git push --set-upstream origin \
              deploy-$TODAY
          fi
      # - name: Trigger Deploy Branch
      #   if: ${{ steps.diff-and-push.outputs.has-changes == 'true' }}
      #   uses: convictional/trigger-workflow-and-wait@v1.6.1
      #   with:
      #     owner: ${{ env.OWNER }}
      #     repo: ${{ env.REPO }}
      #     github_token: ${{ secrets.TECHOPS_PAT }}
      #     github_user: upkeeptechops
      #     workflow_file_name: tag_bump_and_changelog.yml
      #     ref: deploy-${{ steps.todays-date.outputs.date }}
      #     client_payload: '{"deploy_branch": "deploy-${{ steps.todays-date.outputs.date }}"}'
      #     propagate_failure: true
      #     trigger_workflow: true
      #     wait_workflow: false
      - name: Create pull request
        if: ${{ steps.diff-and-push.outputs.has-changes == 'true' }}
        id: cpr
        uses: thomaseizinger/create-pull-request@master
        with:
          github_token: ${{ secrets.TECHOPS_PAT }}
          head: "deploy-${{ steps.todays-date.outputs.date }}"
          base: main
          title: "PR for deploy-${{ steps.todays-date.outputs.date }} prod release"
          reviewers: ${{ vars.PR_REVIEWERS }}

      - name: Send message to Slack API
        if: ${{ steps.diff-and-push.outputs.has-changes == 'true' }}
        uses: archive/github-actions-slack@v2.9.0
        id: notify
        with:
          slack-bot-user-oauth-access-token: ${{ secrets.SLACK_WEB_APP_ACCESS_TOKEN }}
          slack-channel: web-app-deployments
          slack-text: 'Pull Request URL - ${{ steps.cpr.outputs.html_url }} to create main tag for prod deployment - "deploy-${{ steps.todays-date.outputs.date }}"'
          slack-optional-icon_emoji: ":oh-yeah:"
