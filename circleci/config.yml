version: 2.1
parameters:
  action:
    type: enum
    enum: [build, deploy]
    default: build
  cypress_env:
    type: string
    default: staging
  cypress_url:
    type: string
    default: https://staging.onupkeep.com
  cypress_api_url:
    type: string
    default: https://demo-api.onupkeep.com
  cypress_spec:
    type: string
    default: ./cypress/e2e/**/*.spec.cy.js
  cypress_filter:
    type: string
    default: ''
  cypress_test_cycle_key:
    type: string
    default: ''
  slack_context:
    type: string
    default: ''
###############################
# JOB DEFINITIONS
###############################
jobs:
  test-build:
    docker:
      - image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:master
        aws_auth:
          aws_access_key_id: $ECR_API
          aws_secret_access_key: $ECR_SECRET
    steps:
      - checkout
      - restore_cache:
          keys:
            - web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run: yarn install --frozen-lockfile  --cache-folder ~/.cache/yarn
      - save_cache:
          paths:
            - node_modules
            - ~/.cache/yarn
          key: web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run: yarn test
      - run:
          name: Tag and cut release
          command: |
            # Default to staging
            DOCKER_TAG=${CIRCLE_BRANCH}

            # Save reference to docker tag; the deploy script needs this
            echo export DOCKER_TAG=$DOCKER_TAG >> $BASH_ENV
            echo export DOCKER_IMAGE=${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/$CIRCLE_PROJECT_REPONAME:$DOCKER_TAG >> $BASH_ENV
      - setup_remote_docker:
          version: 20.10.11
          docker_layer_caching: true
      - run:
          name: Build Image
          environment:
            CDN_HOST_URL: https://assets.onupkeep.com
          command: |
            COMMIT_SHA=$(git rev-parse --short=14 HEAD)
            ECS_CONFIG=staging
            BUILD_TAG=${CIRCLE_BRANCH}
            GITHUB_REF=${GITHUB_REF}
            echo 'export COMMIT_SHA=${COMMIT_SHA}' >> $BASH_ENV
            echo 'node version:'
            node -v
            source $BASH_ENV
            yarn build
            yarn build-storybook -o ./dist-storybook
            docker build -t $DOCKER_IMAGE --build-arg ENV=$ECS_CONFIG --build-arg BUILD_TAG=$BUILD_TAG --build-arg COMMIT_SHA=$COMMIT_SHA .
            ./.circleci/deploy.sh $COMMIT_SHA
      - run:
          name: Push Image to ECR
          command: |
            aws configure set region us-east-1
            aws configure set output json
            aws configure set aws_access_key_id $ECR_API
            aws configure set aws_secret_access_key $ECR_SECRET
            eval $(aws ecr get-login --region us-east-1 --no-include-email)
            docker push $DOCKER_IMAGE
  deploy-tests:
    docker:
      - image: cypress/included:12.4.1
    parallelism: 8
    steps:
      - checkout
      - restore_cache:
          keys:
            - web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run: yarn install --frozen-lockfile --cache-folder ~/.cache/yarn
      - save_cache:
          paths:
            - node_modules
            - ~/.cache/yarn
            - ~/.cache/Cypress
          key: web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run:
          name: Run Cypress Tests
          environment:
            CYPRESS_ENV: << pipeline.parameters.cypress_env >>
            CYPRESS_URL: << pipeline.parameters.cypress_url >>
            CYPRESS_API_URL: << pipeline.parameters.cypress_api_url >>
          command: |
            if [[ ! -z "<< pipeline.parameters.cypress_filter >>" ]]
            then
              CYPRESS_ENV="<< pipeline.parameters.cypress_env >>" yarn cypress run --browser chrome --spec "$(circleci tests glob "<< pipeline.parameters.cypress_spec >>" | circleci tests split --split-by=timings | paste -sd "," -)" --env tags="<< pipeline.parameters.cypress_filter >>" || true
            else
              CYPRESS_ENV="<< pipeline.parameters.cypress_env >>" yarn cypress run --browser chrome --spec "$(circleci tests glob "<< pipeline.parameters.cypress_spec >>" | circleci tests split --split-by=timings | paste -sd "," -)" --env testCycleKey="<< pipeline.parameters.cypress_test_cycle_key >>" || true
            fi
      - persist_to_workspace:
          root: cypress
          paths:
            - screenshots/
            - videos/
            - reports/
            - results/
      - store_artifacts:
          path: cypress/screenshots/
      - store_artifacts:
          path: cypress/videos/
      - store_artifacts:
          path: cypress/reports/
      - store_artifacts:
          path: cypress/results/
      - store_test_results:
          path: cypress/results/
  deploy-reports:
    docker:
      - image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:master
        aws_auth:
          aws_access_key_id: $ECR_API
          aws_secret_access_key: $ECR_SECRET
    steps:
      - checkout
      - restore_cache:
          keys:
            - web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - attach_workspace:
          at: /tmp/cypress
      - run: yarn install --frozen-lockfile --cache-folder ~/.cache/yarn
      - save_cache:
          paths:
            - node_modules
            - ~/.cache/yarn
          key: web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run:
          name: Merge Reports
          command: yarn report:merge
      - run:
          name: Generate Result
          command: yarn report:generate --reportTitle "<< pipeline.parameters.cypress_env >> << pipeline.parameters.cypress_url >> Tests"
      - run:
          name: Copy Screenshots
          command: |
            if [ -d "/tmp/cypress/screenshots" ]
            then
              echo "i see some screenshots, I'll copy them over."
              mkdir -p /tmp/cypress/results/screenshots
              cp -r /tmp/cypress/screenshots /tmp/cypress/reports/screenshots
            fi
      - run:
          name: Copy Videos
          command: |
            if [ -d "/tmp/cypress/videos" ]
            then
              echo "i see some videos, I'll copy them over."
              mkdir -p /tmp/cypress/results/videos
              cp -r /tmp/cypress/videos /tmp/cypress/reports/videos
            fi
      - persist_to_workspace:
          root: /tmp/cypress
          paths:
            - reports/
      - store_artifacts:
          path: /tmp/cypress/reports
      - store_artifacts:
          path: /tmp/cypress/reports/screenshots
      - store_artifacts:
          path: /tmp/cypress/reports/videos
      - store_test_results:
          path: cypress/results/
  deploy-slack-alerts:
    docker:
      - image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:master
        aws_auth:
          aws_access_key_id: $ECR_API
          aws_secret_access_key: $ECR_SECRET
    steps:
      - checkout
      - restore_cache:
          keys:
            - web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - attach_workspace:
          at: /tmp/cypress
      - run: yarn install --frozen-lockfile --cache-folder ~/.cache/yarn
      - save_cache:
          paths:
            - node_modules
            - ~/.cache/yarn
          key: web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run:
          name: Update Results on Slack
          command: |
            COMMIT_SHA=$(git rev-parse --short=14 HEAD)
            COMMIT_AUTHOR=$(git show -s --format='%ae' $COMMIT_SHA)
            yarn cypress-slack-reporter --report-dir /tmp/cypress/reports --custom-text "Author: $COMMIT_AUTHOR, Environment: << pipeline.parameters.cypress_env >>" --verbose
      - store_artifacts:
          path: /tmp/cypress/reports
  deploy-alert:
    docker:
      - image: 967579385234.dkr.ecr.us-east-1.amazonaws.com/node-build:master
        aws_auth:
          aws_access_key_id: $ECR_API
          aws_secret_access_key: $ECR_SECRET
    steps:
      - checkout
      - restore_cache:
          keys:
            - web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - attach_workspace:
          at: /tmp/cypress
      - run: yarn install --frozen-lockfile --cache-folder ~/.cache/yarn
      - save_cache:
          paths:
            - node_modules
            - ~/.cache/yarn
          key: web-app-yarn-deps-v3-{{ checksum "yarn.lock" }}
      - run:
          name: Fail on Failures
          command: yarn report:check:failures
  trigger-integration-pipeline:
    docker:
      - image: cimg/base:2021.11
    resource_class: small
    steps:
      - run:
          name: Ping another pipeline
          command: |
            curl --request POST \
              --url https://circleci.com/api/v2/project/gh/upkeepapp/upkeep/pipeline \
              --header "Circle-Token: $CIRCLECI_API_KEY" \
              --header "content-type: application/json" \
              --data '{"branch": "nonprod/upkeep/integration","parameters": {"repo-name": "'$CIRCLE_PROJECT_REPONAME'","username": "'$CIRCLE_USERNAME'"}}'
###############################
# WORKFLOW DEFINITIONS
###############################
workflows:
  test-and-build:
    when:
      equal: [build, << pipeline.parameters.action >>]
    jobs:
      - test-build:
          filters:
            tags:
              ignore:
                - /.*/
                - master
      - trigger-integration-pipeline:
          context: IntegrationTrigger
          requires:
            - test-build
          filters:
            branches:
              only:
                - develop
              ignore:
                - master
  # Can be fired from each upkeep repo deployment
  deploy-web-cypress-test:
    when:
      and:
        - equal: [deploy, << pipeline.parameters.action >>]
        - not: << pipeline.parameters.slack_context >>
    jobs:
      - deploy-tests:
          context: zephyr
      - deploy-reports:
          requires:
            - deploy-tests
      - deploy-alert:
          requires:
            - deploy-reports
  deploy-web-cypress-test-slack:
    when:
      and:
        - equal: [deploy, << pipeline.parameters.action >>]
        - << pipeline.parameters.slack_context >>
    jobs:
      - deploy-tests
      - deploy-reports:
          requires:
            - deploy-tests
      - deploy-slack-alerts:
          context: << pipeline.parameters.slack_context >>
          requires:
            - deploy-reports
      - deploy-alert:
          requires:
            - deploy-reports
