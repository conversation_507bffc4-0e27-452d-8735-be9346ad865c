#!/bin/bash

# Use ENV variable for this
BUCKET="assets.onupkeep.com"
APP_NAME="app"
BUILD_HASH=$1
 
# Configure AWS CLI 
aws configure set aws_access_key_id $AWS_KEY_ID
aws configure set aws_secret_access_key $AWS_KEY_SECRET
aws configure set region us-east-1
aws configure set output json

# Copy everything except the index.html file
aws s3 cp --acl public-read --recursive --exclude dist/index.html --cache-control max-age=604800 dist s3://$BUCKET/$APP_NAME/$BUILD_HASH/

# Assets that won't get hashed goes here (favicon / etc)
aws s3 cp --acl public-read --cache-control max-age=604800 ./dist/assets/images/favicon/favicon.ico s3://$BUCKET/$APP_NAME/static/web/assets/images/favicon/favicon.ico
