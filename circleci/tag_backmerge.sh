#!/bin/bash
if [[ ${CIRCLE_BRANCH} =~ release\/.* ]]; then
  # Download Build Tools from ops repo
  git clone --depth 1 **************:upkeepapp/ops.git
  cp ops/packages/release-notifier/builds/release-notifier-alpine ./release-notifier
  cp ops/packages/changelog-generator/builds/changelog-generator-alpine ./changelog-generator

  # Generate a changelog
  ./changelog-generator \
    --github-token $GITHUB_TOKEN \
    --github-user upkeepapp \
    --github-repo $CIRCLE_PROJECT_REPONAME \
    --git-tag-start v$(yarn -s output-version) > ./CHANGELOG.log

  # Output the changelog, just for debugging purposes
  cat ./CHANGELOG.log

  # Generate GitHub release
  git config --global user.email <EMAIL>
  git config --global user.name CircleCI
  git config --global push.default simple
  git checkout .
  yarn create-release-tag --force

  RELEASE_BRANCHNAME=$(git symbolic-ref --short -q HEAD)

  git fetch
  #Checkout master branch and merge release branch into master
  echo Merging $RELEASE_BRANCHNAME to master
  git checkout master
  git reset --hard origin/master
  git merge $RELEASE_BRANCHNAME --no-ff --no-edit
  git push

  #Checkout develop branch and merge release branch into develop
  echo Merging $RELEASE_BRANCHNAME to develop
  git checkout develop
  git reset --hard origin/develop
  git merge $RELEASE_BRANCHNAME --no-ff --no-edit
  git push
fi