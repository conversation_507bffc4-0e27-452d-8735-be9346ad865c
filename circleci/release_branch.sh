
if [ "${CIRCLE_<PERSON>ANCH}" == "develop" ]; then
  # Generate GitHub release-branch
  git config --global user.email <EMAIL>
  git config --global user.name CircleCI
  git config --global push.default simple
  git checkout .
  git describe --tags --abbrev=0

  yarn version --minor
  NEW_RELEASE=$(yarn -s output-version)
  RELEASE_BRANCH="release/$NEW_RELEASE"
  git checkout -b $RELEASE_BRANCH
  git push origin $RELEASE_BRANCH
fi