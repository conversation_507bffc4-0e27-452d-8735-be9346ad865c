#!/bin/bash

# Your existing pre-commit checks go here

# Define the regular expression pattern for commit message validation
pattern="^(RFAC|RTB|SDE|UPD|RVG|QA|TO|MMB|IPT|SDE|TI|NPS|TBD|EHS|AIP3|MAX)-[0-9]+: .*$"
releasePattern="^(release|backmerge|version|Merge|Revert).*$"

# Read the commit message from the COMMIT_EDITMSG file
commit_message="$(cat $1)"
# commit_message=$(cat .git/COMMIT_EDITMSG)
echo "the commit message is: $commit_message"

if [[ $commit_message =~ $releasePattern ]]; then
  echo "Release/Merge commit detected. Skipping commit message validation."
  exit 0
fi
# Check if the commit message matches the pattern
if ! [[ $commit_message =~ $pattern ]]; then
  echo "Commit message is not valid. It should start with RFAC, SDE, RTB, SDE, UPD, RVG, QA, TO, TBD, EHS, AIP3, or MMB followed by a hyphen, numbers, a colon, and then any characters."
  echo "commits starting with release, backmerge, Merge or Revert are also allowed."
  echo "examples:"
  echo "  RFAC-1234: your message here"
  echo "  release-1.2.3: your message here"
  echo "  Merge branch 'develop' into xxxx-1234"
  exit 1 # Exit with error status code
fi

# If all checks pass, continue with the commit
exit 0
