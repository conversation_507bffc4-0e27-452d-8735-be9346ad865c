import React from 'react';
import { Preview } from '@storybook/react';
import initI18n from '../src/locales/i18n';
import { LanguageContext } from '../src/hooks';

import './css/reset.css';

const withProvider = (Story, context) => {
  return (
    <LanguageContext>
      <Story {...context} />
    </LanguageContext>
  );
};

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [withProvider],
  loaders: [async () => await initI18n({})],
};

export default preview;
