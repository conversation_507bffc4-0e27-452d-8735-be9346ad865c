* {
  box-sizing: border-box;
}

html,
body,
input,
textarea,
select,
button {
  font-weight: 400;
}

html,
body {
  /* TODO: IMPORTANT: We have an inline style in index.html that also adds background-color to <body>
      This makes the transition between angular and react feel more natural.
      Eliminate it after angular goes away */
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

/* Remove default padding */
ul,
ol {
  padding: 0;
}

/* Remove default margin */
body,
h1,
h2,
h3,
h4,
p,
ul,
ol,
li,
figure,
figcaption,
blockquote,
dl,
dd {
  margin: 0;
}

/* Remove list styles on ul, ol elements with a class attribute */
ul,
ol {
  list-style: none;
}