const path = require('path');
const wpConfig = require('../webpack.config.js');

/** @type { import('@storybook/react-webpack5').StorybookConfig } */
const config = {
  stories: ['../src/components/@atomic/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-webpack5-compiler-swc',
    '@storybook/addon-onboarding',
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {
      builder: {
        useSWC: true,
      },
    },
  },
  swc: () => ({
    jsc: {
      transform: {
        react: {
          runtime: 'automatic',
        },
      },
    },
  }),
  docs: {
    autodocs: 'tag',
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript-plugin',
  },
  webpackFinal: async (storybookConfig) => {
    storybookConfig.resolve.alias = {
      ...storybookConfig.resolve.alias,

      // reuse aliases from custom webpack config
      ...wpConfig.resolve.alias,
    };
    storybookConfig.resolve.extensions.push('.ts', '.tsx', '.d.ts');
    storybookConfig.plugins.concat(wpConfig.module.plugins);

    const AppSourceDir = path.join(__dirname, '..', 'src');
    const fileLoaderRule = storybookConfig.module.rules.find((rule) =>
      rule.test.test('.svg'),
    );
    fileLoaderRule.exclude = [AppSourceDir];

    storybookConfig.module.rules.push(
      // * JS file loading
      wpConfig.module.rules[0],

      // SVG
      {
        test: /\.svg$/,
        include: [AppSourceDir],
        use: [
          'babel-loader',
          {
            loader: 'react-svg-loader',
            options: {
              jsx: true, // true outputs JSX tags
            },
          },
        ],
      },

      // Image file loading
      {
        test: /\.(gif|jpg|png)$/,
        loader: 'file-loader',
        exclude: /(node_modules)/,
      },

      // * For vendor images and fonts - use url-loader
      {
        test: /\.(gif|jpg|png|svg)$/,
        include: [path.resolve(__dirname, 'node_modules/react-widgets')],
        loader: 'url-loader',
        options: {
          name: '[name].[ext]',
        },
      },

      // * Font file loading
      {
        test: /\.(ttf|otf|eot|woff(2)?)(\?[a-z0-9]+)?$/,
        loader: 'file-loader',
        options: {
          name: 'fonts/[name].[ext]',
        },
      },

      // GraphQL file loading
      {
        test: /\.graphql$/,
        exclude: /node_modules/,
        loader: 'graphql-tag/loader',
      },

      // txt csv
      {
        test: /\.(txt|csv)$/,
        use: [
          {
            loader: 'file-loader',
            options: {},
          },
        ],
      },
    );

    return storybookConfig;
  },
};
export default config;
