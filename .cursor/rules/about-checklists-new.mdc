---
description: About our checklist project, use this rule to understand the project before starting any new component or feature
globs: 
alwaysApply: false
---
# About Checklists

## Overview
Checklists are a core feature in UpKeep's CMMS platform. They provide a structured way for maintenance technicians to complete work by following a predefined series of tasks in a specific order. <PERSON><PERSON> create checklists, which are then attached to work orders and assigned to technicians.

## Workflow
1. <PERSON><PERSON> create checklists with various task types
2. Checklists are attached to work orders
3. Work orders are assigned to technicians
4. Technicians complete each task on the checklist in sequence

## Current Task Types
- Subtask Status (on hold, in progress, complete)
- Text Field
- Number Field
- Inspection Check
- Multiple Choice
- Meter Reading

## Planned Enhancements
- Signature task type
- Acknowledgement tasks with timestamps
- Informational/warning text
- Required task completion option
- Referential media (images, files, videos)
- Notes and URL links for each task
- Conditional logic for tasks
- Sections to group tasks with titles and descriptions

## Tech Stack
- React 16
- TypeScript for type definitions
- React Router for navigation
- Context API for state management
- Custom hooks for reusable logic
- Analytics tracking using custom Analytics utility

### State Management
- Context API with custom providers:
  - `useChecklistsData` - Global checklist context
  - `useAddEditChecklistData` - Editing context
- Custom stores for component state

## Core Files Location
Main directory: `src/scenes/Checklists/`

### Key Files
- `src/scenes/Checklists/index.js` - Main entry point
- `src/scenes/Checklists/types.d.ts` - TypeScript type definitions
- `src/scenes/Checklists/styled.js` - Styled components
- `src/scenes/Checklists/TaskTypes/index.ts` - Task type definitions
- `src/scenes/Checklists/Builder/index.tsx` - Main builder component

### Key Directories
- `src/scenes/Checklists/data/` - Data management
  - `src/scenes/Checklists/data/api/` - API interactions including:
    - `getChecklists.ts` - Fetch checklists
    - `getChecklist.ts` - Fetch a single checklist
    - `upsertChecklist.ts` - Create/update checklists
    - `cloneChecklist.ts` - Clone existing checklists
    - `deleteChecklists.ts` - Delete checklists
    - `updateFormItem.ts` - Update individual checklist items
    - `generateChecklist.ts` - Generate checklists
    - `uploadPDF.ts` - Upload PDF files
- `src/scenes/Checklists/AddEdit/` - Add/Edit interface
- `src/scenes/Checklists/TemplateView/` - Template viewing interface
- `src/scenes/Checklists/ListView/` - List view interface
- `src/scenes/Checklists/TaskBuilder/` - Task builder interface
- `src/scenes/Checklists/Builder/` - Checklist builder interface
- `src/scenes/Checklists/TaskTypes/` - Task type implementations
- `src/scenes/Checklists/components/` - Shared components
- `src/scenes/Checklists/hooks/` - Custom hooks
- `src/scenes/Checklists/@types/` - Additional type definitions

### Key Components
- `src/scenes/Checklists/TaskTypes/index.ts` - Task type definitions and registration
- `src/scenes/Checklists/TaskBuilder/index.tsx` - Task builder component
- `src/scenes/Checklists/Builder/index.tsx` - Checklist builder component
- `src/scenes/Checklists/AddEdit/ChecklistDetails.tsx` - Checklist details editor
- `src/scenes/Checklists/AddEdit/ChecklistTasks.tsx` - Tasks management
- `src/scenes/Checklists/components/TaskInput/` - Task input components
- `src/scenes/Checklists/components/TaskEditInput/` - Task editing components

#### Reference for Design inspiration

- `src/scenes/Checklists/Builder/index.tsx` - Main builder component
- `src/scenes/Checklists/Builder/components/` - Builder sub-components
- `src/scenes/Checklists/TaskBuilder/index.tsx` - Task builder component
- `src/scenes/Checklists/TaskBuilder/components/` - Task builder sub-components
- `src/scenes/Checklists/TaskTypes/` - Individual task type implementations 

## Creating New Task Types
Each task type in the checklist system follows a consistent structure to ensure proper integration:

### Folder Structure
1. Create a new folder in `src/scenes/Checklists/TaskTypes/` with the task type name (e.g., `Signature/`)
2. Required files:
   - `index.ts` - Exports the task type definition
   - `[TaskType]Preview.tsx` - Component to display the task in preview mode
   - `[TaskType]Icon.svg` - SVG icon for the task type in the dropdown
   - `[TaskType]Builder.tsx` - *(Optional)* Component for configuring task-specific settings

### Task Type Definition
The `index.ts` file should export a default object with the TaskType interface:

```typescript
import { TaskType } from 'scenes/Checklists/@types';
import TaskTypePreview from './TaskTypePreview';
import TaskTypeBuilder from './TaskTypeBuilder'; // Optional
import TaskTypeIcon from './TaskTypeIcon.svg';

const TaskTypeName: TaskType = {
  taskTypeId: 0, // Task type ID from TaskTypes/index.ts array position
  name: 'taskTypeName', // Lowercase identifier
  label: 'Task Type Display Name', // User-facing name
  formType: 'Form Item Type', // e.g., 'Text Form Item', 'Checklist Form Item'
  Preview: TaskTypePreview,
  Builder: TaskTypeBuilder, // Optional, include only if needed
  Icon: TaskTypeIcon,
};

export default TaskTypeName;
```

### Preview Component
The Preview component should:
1. Import `TaskPreviewProps` from `scenes/Checklists/@types`
2. Use the `CommonPreviewLabel` component for consistent labeling
3. Show a realistic preview of the task as it will appear to users

Example:
```typescript
import { TaskPreviewProps } from 'scenes/Checklists/@types';
import { CommonPreviewLabel } from '../components';

const TaskTypePreview = ({ label, name }: TaskPreviewProps) => {
  return (
    <>
      <CommonPreviewLabel label={label} name={name} />
      {/* Task-specific preview UI */}
    </>
  );
};

export default TaskTypePreview;
```

### Builder Component
The Builder component is needed when a task type requires additional configuration beyond just a label. For example, the MultipleChoice task type needs a Builder to configure available options.

### Registering a New Task Type
After creating the task type, add it to the array in `src/scenes/Checklists/TaskTypes/index.ts`:

```typescript
import { TaskType } from 'scenes/Checklists/@types';
import Text from './Text';
// ... other imports
import NewTaskType from './NewTaskType';

const taskTypes: TaskType[] = [
  Status,
  Text,
  // ... other task types
  NewTaskType, // Add your new task type here
];

export default taskTypes;
```

### Best Practices
- Use Radix UI components for consistency with the design system
- Reference existing task types for implementation patterns
- Ensure the Preview component accurately represents the final UI
- Keep components focused and reusable
- Use TypeScript interfaces for type safety
